package cn.bztmaster.cnt.module.publicbiz.api.employment;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.employment.dto.AgencyPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employment.dto.AgencyRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employment.dto.AgencyUpdateReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.AgencyConvert;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 机构管理 API 实现类
 *
 * <AUTHOR>
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class AgencyApiImpl implements AgencyApi {

    @Resource
    private AgencyService agencyService;

    @Override
    public CommonResult<PageResult<AgencyRespDTO>> pageAgency(AgencyPageReqDTO reqDTO) {
        // 转换为 VO
        AgencyPageReqVO reqVO = AgencyConvert.INSTANCE.convert(reqDTO);
        // 分页查询
        PageResult<AgencyRespVO> pageResult = agencyService.pageAgency(reqVO);
        // 转换为 DTO
        PageResult<AgencyRespDTO> result = AgencyConvert.INSTANCE.convertPageToDTO(pageResult);
        return success(result);
    }

    @Override
    public CommonResult<AgencyRespDTO> getAgency(Long id) {
        // 获取机构详情
        AgencyRespVO agency = agencyService.getAgency(id);
        // 转换为 DTO
        AgencyRespDTO result = AgencyConvert.INSTANCE.convert(agency);
        return success(result);
    }

    @Override
    public CommonResult<Boolean> updateAgency(AgencyUpdateReqDTO reqDTO) {
        // 转换为 VO
        AgencyUpdateReqVO reqVO = AgencyConvert.INSTANCE.convert(reqDTO);
        // 更新机构
        agencyService.updateAgency(reqVO);
        return success(true);
    }
}