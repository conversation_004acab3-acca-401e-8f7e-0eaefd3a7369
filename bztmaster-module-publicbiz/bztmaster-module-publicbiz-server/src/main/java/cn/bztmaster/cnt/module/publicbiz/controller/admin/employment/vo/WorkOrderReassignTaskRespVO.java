package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 工单重新指派任务响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单重新指派任务响应 VO")
@Data
public class WorkOrderReassignTaskRespVO {

    @Schema(description = "重新指派是否成功", example = "true")
    private Boolean success;

    @Schema(description = "重新指派的任务数量", example = "5")
    private Integer reassignedCount;

    @Schema(description = "失败的任务列表")
    private List<String> failedTasks;
} 