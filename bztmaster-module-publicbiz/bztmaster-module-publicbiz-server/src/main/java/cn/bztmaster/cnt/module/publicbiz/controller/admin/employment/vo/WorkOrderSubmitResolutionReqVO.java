package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 工单提交处理结果请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单提交处理结果请求 VO")
@Data
public class WorkOrderSubmitResolutionReqVO {

    @Schema(description = "工单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "WO202312010001")
    @NotBlank(message = "工单编号不能为空")
    private String workOrderNo;

    @Schema(description = "处理结果内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "已联系客户，问题已解决")
    @NotBlank(message = "处理结果内容不能为空")
    private String logContent;

    @Schema(description = "附件列表")
    private List<Attachment> attachments;
    /**
     * 附件对象
     */
    @Schema(description = "附件对象")
    @Data
    public static class Attachment {

        @Schema(description = "文件名", example = "工单处理报告.pdf")
        private String fileName;

        @Schema(description = "文件URL", example = "https://example.com/files/report.pdf")
        private String fileUrl;

        @Schema(description = "文件类型", example = "pdf")
        private String fileType;

        @Schema(description = "文件分类", example = "工单处理")
        private String fileCategory;

        @Schema(description = "上传目的", example = "工单处理结果证明")
        private String uploadPurpose;
    }
} 