package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.CourseAttachmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 课程附件管理 Controller
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/components/ManagementCourse.vue
 * 
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 课程附件管理")
@RestController
@RequestMapping("/publicbiz/digital-asset/course/attachment")
@Validated
public class CourseAttachmentController {

    @Resource
    private CourseAttachmentService courseAttachmentService;

    @GetMapping("/list/{courseId}")
    @Operation(summary = "获取课程附件列表")
    @Parameter(name = "courseId", description = "课程ID", required = true)
    public CommonResult<List<CourseAttachmentRespVO>> listCourseAttachment(@PathVariable("courseId") Long courseId) {
        List<CourseAttachmentRespVO> list = courseAttachmentService.listCourseAttachment(courseId);
        return CommonResult.success(list);
    }

    @PostMapping("/add")
    @Operation(summary = "添加课程附件")
    public CommonResult<Long> addCourseAttachment(@Valid @RequestBody CourseAttachmentSaveReqVO reqVO) {
        Long attachmentId = courseAttachmentService.addCourseAttachment(reqVO);
        return CommonResult.success(attachmentId);
    }

    @DeleteMapping("/remove/{id}")
    @Operation(summary = "移除课程附件")
    @Parameter(name = "id", description = "附件ID", required = true)
    public CommonResult<Boolean> removeCourseAttachment(@PathVariable("id") Long id) {
        courseAttachmentService.removeCourseAttachment(id);
        return CommonResult.success(true);
    }
}
