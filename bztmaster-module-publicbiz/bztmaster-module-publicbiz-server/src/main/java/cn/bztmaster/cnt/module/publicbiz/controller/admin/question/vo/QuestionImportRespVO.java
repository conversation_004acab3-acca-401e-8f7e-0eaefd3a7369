package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 考题 - 批量导入响应 VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "考题 - 批量导入响应 VO")
public class QuestionImportRespVO {
    
    @Schema(description = "总记录数")
    private Integer totalCount;
    
    @Schema(description = "成功导入数")
    private Integer successCount;
    
    @Schema(description = "失败数")
    private Integer failCount;
    
    @Schema(description = "失败详情列表")
    private List<QuestionImportFailItemVO> failList;

    /**
     * 导入失败项
     */
    @Data
    @Schema(description = "导入失败项")
    public static class QuestionImportFailItemVO {
        
        @Schema(description = "行号")
        private Integer rowNum;
        
        @Schema(description = "题干")
        private String title;
        
        @Schema(description = "失败原因")
        private String reason;
    }

}
