<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.business.BusinessMapper">
    <select id="selectPage" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessDO">
        SELECT id, tenant_id, name, customer_id, customer_name, business_type, total_price, expected_deal_date, business_stage, description, owner_user_id, owner_user_name, creator, create_time, updater, update_time, deleted
        FROM publicbiz_business
        WHERE deleted = 0
        <if test="reqVO.id != null">
            AND id = #{reqVO.id}
        </if>
        <if test="reqVO.tenantId != null">
            AND tenant_id = #{reqVO.tenantId}
        </if>
        <if test="reqVO.name != null and reqVO.name != ''">
            AND name LIKE CONCAT('%', #{reqVO.name}, '%')
        </if>
        <if test="reqVO.customerId != null">
            AND customer_id = #{reqVO.customerId}
        </if>
        <if test="reqVO.customerName != null and reqVO.customerName != ''">
            AND customer_name LIKE CONCAT('%', #{reqVO.customerName}, '%')
        </if>
        <if test="reqVO.businessType != null and reqVO.businessType != ''">
            AND business_type = #{reqVO.businessType}
        </if>
        <if test="reqVO.totalPrice != null">
            AND total_price = #{reqVO.totalPrice}
        </if>
        <if test="reqVO.businessStage != null and reqVO.businessStage != ''">
            AND business_stage = #{reqVO.businessStage}
        </if>
        <if test="reqVO.ownerUserId != null">
            AND owner_user_id = #{reqVO.ownerUserId}
        </if>
        <if test="reqVO.ownerUserName != null and reqVO.ownerUserName != ''">
            AND owner_user_name LIKE CONCAT('%', #{reqVO.ownerUserName}, '%')
        </if>
        <if test="reqVO.creator != null and reqVO.creator != ''">
            AND creator = #{reqVO.creator}
        </if>
        <if test="reqVO.updater != null and reqVO.updater != ''">
            AND updater = #{reqVO.updater}
        </if>
        <if test="reqVO.deleted != null">
            AND deleted = #{reqVO.deleted}
        </if>
        ORDER BY id DESC
    </select>
</mapper>