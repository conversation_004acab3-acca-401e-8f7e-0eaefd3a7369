package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "就业服务-服务套餐分页 Request VO")
public class ServicePackagePageReqVO extends PageParam {
    @Schema(description = "套餐名称或ID关键词")
    private String keyword;

    @Schema(description = "服务分类")
    private String category;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "套餐类型")
    private String packageType;

    @Schema(description = "审核状态")
    private String auditStatus;

    @Schema(description = "所属机构ID")
    private Long agencyId;
} 