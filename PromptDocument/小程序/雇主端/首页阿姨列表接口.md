# 阿姨表结构
    CREATE TABLE `publicbiz_practitioner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `aunt_oneid` varchar(36) NOT NULL DEFAULT '' COMMENT '阿姨OneID GUID',
  `name` varchar(50) NOT NULL COMMENT '阿姨姓名',
  `phone` varchar(20) NOT NULL COMMENT '手机号，用于登录',
  `id_card` varchar(18) NOT NULL COMMENT '身份证号',
  `hometown` varchar(100) DEFAULT NULL COMMENT '籍贯，如：四川成都',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别：male-男/female-女',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `service_type` varchar(50) NOT NULL COMMENT '主要服务类型：月嫂/育儿嫂/保洁/护工',
  `experience_years` int(11) NOT NULL COMMENT '从业年限',
  `platform_status` varchar(20) NOT NULL DEFAULT 'cooperating' COMMENT '平台状态：cooperating-合作中/terminated-已解约',
  `rating` decimal(2,1) NOT NULL DEFAULT '4.5' COMMENT '评级，1.0-5.0',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
  `agency_name` varchar(200) DEFAULT NULL COMMENT '所属机构名称',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-正常/inactive-停用/pending-待审核',
  `current_status` varchar(50) DEFAULT NULL COMMENT '当前状态：服务中/待岗/休假中',
  `current_order_id` varchar(50) DEFAULT NULL COMMENT '当前服务订单ID',
  `total_orders` int(11) NOT NULL DEFAULT '0' COMMENT '累计服务单数',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计收入',
  `customer_satisfaction` decimal(3,1) DEFAULT NULL COMMENT '客户满意度评分',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='阿姨基本信息表';

# 机构表
    CREATE TABLE `publicbiz_agency` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `agency_no` varchar(50) NOT NULL COMMENT '机构编号',
  `agency_name` varchar(200) NOT NULL COMMENT '机构全称',
  `agency_short_name` varchar(100) DEFAULT NULL COMMENT '机构简称',
  `agency_type` varchar(30) NOT NULL COMMENT '机构类型：cooperation-合作/competitor-竞争对手/other-其他',
  `legal_representative` varchar(50) DEFAULT NULL COMMENT '法人代表',
  `unified_social_credit_code` varchar(50) DEFAULT NULL COMMENT '统一社会信用代码',
  `establishment_date` date DEFAULT NULL COMMENT '成立日期',
  `registered_address` varchar(500) DEFAULT NULL COMMENT '注册地址',
  `operating_address` varchar(500) DEFAULT NULL COMMENT '经营地址',
  `business_scope` text COMMENT '经营范围',
  `applicant_name` varchar(50) DEFAULT NULL COMMENT '申请人姓名',
  `applicant_phone` varchar(20) DEFAULT NULL COMMENT '申请人电话',
  `application_time` datetime DEFAULT NULL COMMENT '申请时间',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `agency_address` varchar(500) DEFAULT NULL COMMENT '机构地址',
  `province_code` varchar(50) DEFAULT NULL COMMENT '省份code',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city_code` varchar(50) DEFAULT NULL COMMENT '城市code',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `district_code` varchar(50) DEFAULT NULL COMMENT '区县code',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `street_code` varchar(100) DEFAULT NULL COMMENT '街道',
  `street` varchar(100) DEFAULT NULL COMMENT '街道',
  `detail_address` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `location_accuracy` varchar(20) DEFAULT NULL COMMENT '位置精度：high-高精度/medium-中等精度/low-低精度',
  `cooperation_status` varchar(20) NOT NULL DEFAULT 'cooperating' COMMENT '合作状态：cooperating-合作中/suspended-已暂停/terminated-已终止',
  `contract_no` varchar(50) DEFAULT NULL COMMENT '合同编号',
  `contract_start_date` date DEFAULT NULL COMMENT '合同开始日期',
  `contract_end_date` date DEFAULT NULL COMMENT '合同结束日期',
  `commission_rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例',
  `review_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-待审核/approved-已通过/rejected-已拒绝',
  `reviewer` varchar(64) DEFAULT NULL COMMENT '审核人',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_remark` text COMMENT '审核备注',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-正常/inactive-停用/pending-待审核',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agency_no` (`agency_no`),
  UNIQUE KEY `uk_unified_social_credit_code` (`unified_social_credit_code`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_agency_name` (`agency_name`),
  KEY `idx_agency_type` (`agency_type`),
  KEY `idx_legal_representative` (`legal_representative`),
  KEY `idx_establishment_date` (`establishment_date`),
  KEY `idx_province_code` (`province_code`),
  KEY `idx_city_code` (`city_code`),
  KEY `idx_longitude_latitude` (`longitude`,`latitude`),
  KEY `idx_applicant_name` (`applicant_name`),
  KEY `idx_application_time` (`application_time`),
  KEY `idx_cooperation_status` (`cooperation_status`),
  KEY `idx_contract_no` (`contract_no`),
  KEY `idx_review_status` (`review_status`),
  KEY `idx_reviewer` (`reviewer`),
  KEY `idx_review_time` (`review_time`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=1006 DEFAULT CHARSET=utf8mb4 COMMENT='机构主表信息';


# 雇主首页阿姨列表接口文档

## 接口概述

获取雇主首页金牌阿姨和金牌月嫂列表数据，用于展示在首页的金牌阿姨和金牌月嫂区域。

## 接口信息

- **接口名称**: 获取首页金牌阿姨列表
- **请求方式**: GET
- **接口路径**: `/publicbiz/employer/aunt/home-list`
- **接口描述**: 获取首页金牌阿姨和金牌月嫂数据，按评级排序获取前8名，前4名分配给金牌阿姨，后4名分配给金牌月嫂

## 请求参数

### 查询参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | int | 否 | 8 | 返回数据条数，最大8条 |

### 请求示例

```http
GET /publicbiz/employer/aunt/home-list?limit=8
```

## 响应参数

### 响应结构

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "goldAunts": [
      {
        "id": 1,
        "name": "王阿姨",
        "avatar": "https://example.com/avatar1.jpg",
        "agencyName": "金牌家政",
        "serviceType": "保洁",
        "rating": 4.9,
        "experienceYears": 5
      }
    ],
    "maternityAunts": [
      {
        "id": 2,
        "name": "李阿姨",
        "avatar": "https://example.com/avatar2.jpg",
        "agencyName": "专业月嫂",
        "serviceType": "月嫂",
        "rating": 4.8,
        "experienceYears": 8
      }
    ]
  }
}
```

### 响应字段说明

#### 金牌阿姨列表 (goldAunts)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 阿姨ID |
| name | string | 阿姨姓名 |
| avatar | string | 头像URL |
| agencyName | string | 所属机构信息的简称（用于显示在阿姨名称下方） |
| serviceType | string | 服务类型：月嫂/育儿嫂/保洁/护工 |
| rating | decimal | 评级，1.0-5.0 |
| experienceYears | int | 从业年限 |

#### 金牌月嫂列表 (maternityAunts)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 阿姨ID |
| name | string | 阿姨姓名 |
| avatar | string | 头像URL |
| agencyName | string | 所属机构名称（用于显示在阿姨名称下方） |
| serviceType | string | 服务类型：月嫂/育儿嫂/保洁/护工 |
| rating | decimal | 评级，1.0-5.0 |
| experienceYears | int | 从业年限 |

## 业务逻辑

### 数据筛选条件

1. **基础筛选条件**：
   - `platform_status = 'cooperating'` (合作中)
   - `deleted = 0` (未删除)
   - `status = 'active'` (正常状态)

2. **数据获取逻辑**：
   - 按 `rating` 降序排序获取前8名阿姨
   - 前4名分配给金牌阿姨列表
   - 后4名分配给金牌月嫂列表
   - 不按 `service_type` 进行过滤
3. 注意接口返回的机构名称是机构表的 简称信息 对应表publicbiz_agency的 agency_short_name字段