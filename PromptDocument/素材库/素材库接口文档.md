# 素材库接口文档

## 分类管理

### 1. 分类列表

**接口地址：** `GET /system/material/category/list`

**请求方式：** GET

**请求参数：** 无

**响应参数：**
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "分类名称",
      "sort": 1,
      "createTime": "2024-01-01 10:00:00"
    }
  ],
  "msg": "操作成功"
}
```

**请求示例：**
```bash
curl -X GET "http://localhost:8080/system/material/category/list"
```

### 2. 新增分类

**接口地址：** `POST /system/material/category/create`

**请求方式：** POST

**请求参数：**
```json
{
  "name": "分类名称",
  "sort": 1
}
```

**参数说明：**
- `name` (String, 必填): 分类名称
- `sort` (Integer, 可选): 排序值，默认为0

**响应参数：**
```json
{
  "code": 0,
  "data": 1,
  "msg": "操作成功"
}
```

**请求示例：**
```bash
curl -X POST "http://localhost:8080/system/material/category/create" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "图片分类",
    "sort": 1
  }'
```

### 3. 编辑分类

**接口地址：** `POST /system/material/category/update`

**请求方式：** POST

**请求参数：**
```json
{
  "id": 1,
  "name": "分类名称",
  "sort": 1
}
```

**参数说明：**
- `id` (Long, 必填): 分类ID
- `name` (String, 必填): 分类名称
- `sort` (Integer, 可选): 排序值

**响应参数：**
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

**请求示例：**
```bash
curl -X POST "http://localhost:8080/system/material/category/update" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "更新后的分类名称",
    "sort": 2
  }'
```

### 4. 删除分类

**接口地址：** `POST /system/material/category/delete`

**请求方式：** POST

**请求参数：**
```json
{
  "id": 1
}
```

**参数说明：**
- `id` (Long, 必填): 要删除的分类ID

**响应参数：**
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

**请求示例：**
```bash
curl -X POST "http://localhost:8080/system/material/category/delete" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1
  }'
```

## 前端接口绑定示例

### Vue 3 + Axios

```javascript
// api/material.js
import request from '@/utils/request'

// 分类管理接口
export const materialCategoryApi = {
  // 获取分类列表
  getCategoryList() {
    return request({
      url: '/system/material/category/list',
      method: 'get'
    })
  },

  // 新增分类
  createCategory(data) {
    return request({
      url: '/system/material/category/create',
      method: 'post',
      data
    })
  },

  // 编辑分类
  updateCategory(data) {
    return request({
      url: '/system/material/category/update',
      method: 'post',
      data
    })
  },

  // 删除分类
  deleteCategory(id) {
    return request({
      url: '/system/material/category/delete',
      method: 'post',
      data: { id }
    })
  }
}
```

### React + Axios

```javascript
// api/material.js
import axios from 'axios'

// 分类管理接口
export const materialCategoryApi = {
  // 获取分类列表
  getCategoryList: () => {
    return axios.get('/system/material/category/list')
  },

  // 新增分类
  createCategory: (data) => {
    return axios.post('/system/material/category/create', data)
  },

  // 编辑分类
  updateCategory: (data) => {
    return axios.post('/system/material/category/update', data)
  },

  // 删除分类
  deleteCategory: (id) => {
    return axios.post('/system/material/category/delete', { id })
  }
}
```

### 使用示例

```javascript
// Vue 3 组件中使用
import { materialCategoryApi } from '@/api/material'

export default {
  data() {
    return {
      categoryList: []
    }
  },
  methods: {
    // 获取分类列表
    async getCategoryList() {
      try {
        const res = await materialCategoryApi.getCategoryList()
        this.categoryList = res.data
      } catch (error) {
        console.error('获取分类列表失败:', error)
      }
    },

    // 编辑分类
    async updateCategory(categoryData) {
      try {
        await materialCategoryApi.updateCategory(categoryData)
        this.$message.success('编辑成功')
        this.getCategoryList() // 刷新列表
      } catch (error) {
        console.error('编辑分类失败:', error)
      }
    },

    // 删除分类
    async deleteCategory(id) {
      try {
        await materialCategoryApi.deleteCategory(id)
        this.$message.success('删除成功')
        this.getCategoryList() // 刷新列表
      } catch (error) {
        console.error('删除分类失败:', error)
      }
    }
  }
}
```

## 注意事项

1. **请求头设置：** 所有请求都需要设置 `Content-Type: application/json`
2. **权限验证：** 接口可能需要登录权限，请确保携带有效的认证信息
3. **错误处理：** 建议在前端统一处理接口错误，显示友好的错误提示
4. **数据验证：** 前端提交数据前应进行必要的验证，确保数据格式正确
