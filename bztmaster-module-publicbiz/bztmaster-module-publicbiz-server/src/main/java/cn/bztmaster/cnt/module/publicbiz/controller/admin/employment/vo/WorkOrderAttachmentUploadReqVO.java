package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 工单附件上传请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单附件上传请求 VO")
@Data
public class WorkOrderAttachmentUploadReqVO {

    @Schema(description = "工单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "WO202312010001")
    @NotBlank(message = "工单编号不能为空")
    private String workOrderNo;

    @Schema(description = "文件名", requiredMode = Schema.RequiredMode.REQUIRED, example = "投诉照片.jpg")
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    @Schema(description = "文件URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com/file.jpg")
    @NotBlank(message = "文件URL不能为空")
    private String fileUrl;

    @Schema(description = "文件大小(字节)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024000")
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    @Schema(description = "文件分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "evidence")
    @NotBlank(message = "文件分类不能为空")
    private String fileCategory;

    @Schema(description = "上传人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "上传人ID不能为空")
    private Long uploaderId;

    @Schema(description = "上传人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotBlank(message = "上传人姓名不能为空")
    private String uploaderName;

    @Schema(description = "上传目的", requiredMode = Schema.RequiredMode.REQUIRED, example = "工单附件")
    @NotBlank(message = "上传目的不能为空")
    private String uploadPurpose;
} 