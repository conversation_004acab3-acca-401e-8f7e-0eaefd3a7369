# 旗下阿姨管理模块接口文档生成提示词

## 1. 模块概述

请根据以下数据库表结构和前端页面功能，为**旗下阿姨管理模块**生成完整的RESTful API接口文档。

### 数据库表结构：

- **主表**：`publicbiz_practitioner` - 阿姨基本信息表
- **关联表1**：`publicbiz_practitioner_qualification` - 阿姨资质文件表
- **关联表2**：`publicbiz_practitioner_service_record` - 阿姨服务记录表
- **关联表3**：`publicbiz_practitioner_rating_record` - 阿姨评级记录表

### 前端页面功能：

- 阿姨列表展示（支持分页、筛选、状态切换）
- 阿姨新增/编辑（抽屉形式）
- 阿姨详情查看
- 阿姨状态管理（合作中/已解约）
- 资质文件上传管理
- 简历海报生成
- 解约功能

---

## 2. 接口需求详细说明

### 2.1 阿姨列表查询接口

**功能**：分页查询阿姨列表，支持多条件筛选

**前端调用场景**：

- 页面初始化加载阿姨列表
- 切换标签页时重新加载
- 搜索筛选时重新加载

**查询参数要求**：

- `keyword`：阿姨姓名或手机号模糊搜索
- `serviceType`：服务类型筛选（月嫂/育儿嫂/保洁/护工）
- `platformStatus`：平台状态筛选（cooperating-合作中/terminated-已解约）
- `rating`：评级筛选（五星/四星/三星及以下）
- `agencyId`：机构ID筛选
- `pageNum`：当前页码
- `pageSize`：每页数量

**返回数据要求**：

- 包含阿姨基本信息（id、name、phone、serviceType、rating等）
- 包含当前状态和当前订单信息
- 包含累计单数统计
- 包含分页信息（total、pageNum、pageSize）

### 2.2 阿姨详情查询接口

**功能**：根据ID查询阿姨详细信息

**前端调用场景**：

- 点击"查看"按钮查看阿姨详情
- 编辑阿姨时加载详情数据

**返回数据要求**：

- 包含阿姨所有字段信息
- 包含关联的资质文件列表
- 包含最近的服务记录
- 包含评级历史记录

### 2.3 阿姨新增接口

**功能**：创建新的阿姨信息

**前端调用场景**：

- 点击"+新增阿姨"按钮提交表单

**请求参数要求**：

- 基本信息：name、phone、idCard、hometown、age、gender
- 服务信息：serviceType、experienceYears、platformStatus、rating
- 机构信息：agencyId、agencyName
- 资质文件：qualifications（数组，包含文件类型、文件名、文件URL）

**特殊处理要求**：

- 手机号和身份证号需要唯一性验证
- 资质文件需要同时插入到关联表
- 支持图片和PDF文件上传

### 2.4 阿姨信息更新接口

**功能**：更新现有阿姨信息

**前端调用场景**：

- 编辑阿姨后提交表单

**请求参数要求**：

- 与新增接口相同的参数结构
- 支持部分字段更新
- 资质文件的增删改操作

### 2.5 阿姨状态更新接口

**功能**：更新阿姨平台状态（合作中/已解约）

**前端调用场景**：

- 点击操作列的"解约"按钮

**请求参数要求**：

- `id`：阿姨ID
- `platformStatus`：目标状态
- `reason`：解约原因（可选）

### 2.6 阿姨删除接口

**功能**：删除阿姨（软删除）

**前端调用场景**：

- 点击操作列的"删除"按钮

### 2.7 导出阿姨列表接口

**功能**：导出阿姨列表为Excel文件

**前端调用场景**：

- 点击"导出列表"按钮

**请求参数要求**：

- 与列表查询相同的筛选参数
- 支持选择导出字段

**返回数据要求**：

- 返回Excel文件下载链接
- 支持大数据量分批导出

### 2.8 生成简历海报接口

**功能**：生成阿姨的简历海报

**前端调用场景**：

- 点击"生成简历海报"按钮

**请求参数要求**：

- `id`：阿姨ID
- `templateId`：海报模板ID（可选）

**返回数据要求**：

- 返回海报图片URL
- 支持多种海报模板

### 2.9 资质文件上传接口

**功能**：上传阿姨的资质文件

**前端调用场景**：

- 表单中的文件上传组件

**要求**：

- 支持身份证正反面、健康证、专业技能证书、其他附件
- 支持图片和PDF格式
- 支持多文件上传
- 返回文件URL

### 2.10 阿姨评级更新接口

**功能**：更新阿姨评级

**前端调用场景**：

- 管理员手动调整评级

**请求参数要求**：

- `id`：阿姨ID
- `newRating`：新评级
- `ratingReason`：评级原因
- `ratingType`：评级类型（platform-平台评级）

---

## 3. 数据模型要求

### 3.1 阿姨主表字段映射

请确保以下字段正确映射：

- `id` → 阿姨ID
- `name` → 阿姨姓名
- `phone` → 手机号
- `id_card` → 身份证号
- `hometown` → 籍贯
- `age` → 年龄
- `gender` → 性别
- `avatar` → 头像URL
- `service_type` → 主要服务类型
- `experience_years` → 从业年限
- `platform_status` → 平台状态
- `rating` → 评级
- `agency_id` → 所属机构ID
- `agency_name` → 所属机构名称
- `status` → 状态
- `current_status` → 当前状态
- `current_order_id` → 当前服务订单ID
- `total_orders` → 累计服务单数
- `total_income` → 累计收入
- `customer_satisfaction` → 客户满意度评分

### 3.2 关联表处理

- 资质文件数据存储在 `publicbiz_practitioner_qualification` 表
- 服务记录数据存储在 `publicbiz_practitioner_service_record` 表
- 评级记录数据存储在 `publicbiz_practitioner_rating_record` 表
- 需要处理一对多关系

---

## 4. 接口规范要求

### 4.1 统一响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01 12:00:00"
}
```

### 4.2 分页响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20
  }
}
```

### 4.3 错误处理

- 参数验证错误：400
- 权限不足：403
- 资源不存在：404
- 服务器错误：500

### 4.4 接口路径规范

- 列表查询：`GET /publicbiz/employment/practitioner/list`
- 详情查询：`GET /publicbiz/employment/practitioner/{id}`
- 新增阿姨：`POST /publicbiz/employment/practitioner`
- 更新阿姨：`PUT /publicbiz/employment/practitioner/{id}`
- 删除阿姨：`DELETE /publicbiz/employment/practitioner/{id}`
- 状态更新：`PUT /publicbiz/employment/practitioner/{id}/status`
- 导出列表：`POST /publicbiz/employment/practitioner/export`
- 生成简历海报：`POST /publicbiz/employment/practitioner/{id}/resume-poster`
- 评级更新：`PUT /publicbiz/employment/practitioner/{id}/rating`
- 文件上传(使用现有文件上传接口)：`POST /infra/file/upload`

---

## 5. 特殊要求

### 5.1 多租户支持

- 所有接口需要支持 `tenant_id` 字段
- 数据隔离，只能操作当前租户的数据

### 5.2 软删除支持

- 删除操作使用软删除（设置 `deleted = 1`）
- 查询时需要过滤已删除的数据

### 5.3 操作日志

- 记录创建人、更新人、创建时间、更新时间
- 支持操作审计
- 要介入操作日志模块

### 5.4 数据验证

- 手机号不能重复
- 身份证号不能重复
- 手机号格式验证
- 身份证号格式验证
- 必填字段验证

### 5.5 性能优化

- 列表查询支持索引优化
- 关联查询使用JOIN或分步查询
- 文件上传支持CDN

---

## 6. 前端页面功能分析

### 6.1 列表页面功能

**搜索筛选区域**：

- 输入框：阿姨姓名/手机号搜索
- 下拉框：服务类型筛选
- 下拉框：平台状态筛选
- 下拉框：评级筛选
- 按钮：查询、重置

**操作按钮区域**：

- 导出列表按钮
- 新增阿姨按钮

**表格展示**：

- 阿姨ID/姓名列
- 综合评级列（带星级图标）
- 服务类型列
- 累计单数列
- 当前状态列（服务中/待岗/休假中）
- 平台状态列（带颜色标签）
- 操作列（查看、编辑、生成简历海报、解约）

**分页功能**：

- 显示总条数
- 分页导航

### 6.2 新增/编辑页面功能

**基本信息**：

- 姓名（必填）
- 手机号（必填，用于登录）
- 身份证号（必填）
- 籍贯（必填）

**服务信息**：

- 主要服务类型（必填）
- 从业年限（必填）
- 平台状态（必填）
- 评级（必填，1.0-5.0）

**资质文件管理**：

- 身份证正反面（支持图片和PDF）
- 健康证（支持图片和PDF）
- 专业技能证书（可多选，支持图片和PDF）
- 其他附件（支持图片、PDF、Word文档）

### 6.3 详情查看功能

**基本信息展示**：

- 阿姨个人资料
- 服务信息
- 评级信息

**服务记录**：

- 历史服务订单
- 客户评价
- 收入统计

**资质文件**：

- 各类证书展示
- 文件下载功能

---

## 7. 数据库表字段详细说明

### 7.1 主表字段 (publicbiz_practitioner)

```sql
-- 基本信息字段
id: 主键，自增
name: 阿姨姓名，VARCHAR(50)，必填
phone: 手机号，VARCHAR(20)，必填，唯一
id_card: 身份证号，VARCHAR(18)，必填，唯一
hometown: 籍贯，VARCHAR(100)
age: 年龄，INT
gender: 性别，VARCHAR(10)
avatar: 头像URL，VARCHAR(500)

-- 服务信息字段
service_type: 主要服务类型，VARCHAR(50)，必填
experience_years: 从业年限，INT，必填
platform_status: 平台状态，VARCHAR(20)，默认'cooperating'
rating: 评级，DECIMAL(2,1)，默认4.5

-- 机构关联字段
agency_id: 所属机构ID，BIGINT
agency_name: 所属机构名称，VARCHAR(200)

-- 状态字段
status: 状态，VARCHAR(20)，默认'active'
current_status: 当前状态，VARCHAR(50)
current_order_id: 当前服务订单ID，VARCHAR(50)

-- 统计字段
total_orders: 累计服务单数，INT，默认0
total_income: 累计收入，DECIMAL(10,2)，默认0.00
customer_satisfaction: 客户满意度评分，DECIMAL(3,1)
```

### 7.2 资质文件表字段 (publicbiz_practitioner_qualification)

```sql
practitioner_id: 阿姨ID，BIGINT，关联主表
file_type: 文件类型，VARCHAR(50)，必填
file_name: 文件名，VARCHAR(200)，必填
file_url: 文件URL，VARCHAR(500)，必填
file_size: 文件大小，BIGINT
file_extension: 文件扩展名，VARCHAR(20)
sort_order: 排序，INT，默认0
status: 状态，TINYINT(1)，默认1
```

### 7.3 服务记录表字段 (publicbiz_practitioner_service_record)

```sql
practitioner_id: 阿姨ID，BIGINT，关联主表
order_id: 订单ID，VARCHAR(50)，必填
customer_id: 客户ID，BIGINT
customer_name: 客户姓名，VARCHAR(50)
service_type: 服务类型，VARCHAR(50)，必填
service_start_time: 服务开始时间，DATETIME
service_end_time: 服务结束时间，DATETIME
service_duration: 服务时长，VARCHAR(50)
service_address: 服务地址，VARCHAR(500)
service_amount: 服务金额，DECIMAL(10,2)，必填
practitioner_income: 阿姨收入，DECIMAL(10,2)
platform_income: 平台收入，DECIMAL(10,2)
order_status: 订单状态，VARCHAR(20)，必填
customer_rating: 客户评分，DECIMAL(2,1)
customer_comment: 客户评价，TEXT
remark: 备注，TEXT
```

### 7.4 评级记录表字段 (publicbiz_practitioner_rating_record)

```sql
practitioner_id: 阿姨ID，BIGINT，关联主表
rating_type: 评级类型，VARCHAR(20)，必填
old_rating: 原评级，DECIMAL(2,1)
new_rating: 新评级，DECIMAL(2,1)，必填
rating_change: 评级变化，DECIMAL(2,1)
rating_reason: 评级原因，VARCHAR(500)
evaluator_id: 评价人ID，BIGINT
evaluator_name: 评价人姓名，VARCHAR(50)
evaluator_type: 评价人类型，VARCHAR(20)
related_order_id: 关联订单ID，VARCHAR(50)
remark: 备注，TEXT
```

---

## 8. 接口实现建议

### 8.1 查询接口实现

- 使用LEFT JOIN关联查询资质文件和服务记录
- 支持动态SQL条件拼接
- 使用分页插件实现分页
- 缓存常用查询结果

### 8.2 新增/更新接口实现

- 使用事务确保数据一致性
- 先插入主表，再插入关联表
- 支持批量操作资质文件
- 文件上传使用异步处理

### 8.3 状态管理接口实现

- 支持状态变更记录
- 记录状态变更日志
- 支持状态变更通知

### 8.4 文件上传接口实现

- 支持多种文件格式
- 自动生成缩略图
- 支持文件压缩
- 返回CDN链接

### 8.5 导出功能实现

- 支持大数据量分批导出
- 使用异步任务处理
- 支持多种导出格式

### 8.6 简历海报生成实现

- 使用模板引擎生成海报
- 支持多种海报模板
- 自动生成海报图片
- 返回海报下载链接

---

## 9. 测试用例建议

### 9.1 功能测试

- 阿姨CRUD操作测试
- 文件上传功能测试
- 状态变更测试
- 导出功能测试
- 海报生成测试

### 9.2 性能测试

- 大量数据查询性能
- 文件上传并发测试
- 导出功能性能测试
- 数据库连接池测试

### 9.3 安全测试

- 参数验证测试
- SQL注入防护测试
- 权限控制测试
- 文件上传安全测试

---

## 10. 数据字典

### 10.1 服务类型 (service_type)

- 月嫂
- 育儿嫂
- 保洁
- 护工

### 10.2 平台状态 (platform_status)

- cooperating: 合作中
- terminated: 已解约

### 10.3 阿姨状态 (practitioner_status)

- active: 正常
- inactive: 停用
- pending: 待审核

### 10.4 当前状态 (current_status)

- 服务中
- 待岗
- 休假中

### 10.5 文件类型 (file_type)

- id_card: 身份证
- health_cert: 健康证
- skill_cert: 专业技能证书
- other: 其他附件

### 10.6 评级类型 (rating_type)

- customer: 客户评价
- platform: 平台评级
- system: 系统评级

### 10.7 订单状态 (order_status)

- pending: 待确认
- confirmed: 已确认
- in_progress: 服务中
- completed: 已完成
- cancelled: 已取消

---

请根据以上详细说明生成完整的接口文档，确保文档的准确性和实用性。文档应包含所有接口的详细说明、请求/响应示例、错误码说明、数据库操作SQL示例和接口测试用例。
