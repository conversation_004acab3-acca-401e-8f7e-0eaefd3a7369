package cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo;

import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 管理后台 - 场地管理新增/修改 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 场地管理新增/修改 Request VO")
@Data
public class SiteManagementSaveReqVO {

    @Schema(description = "场地ID", example = "1")
    private Long id;

    @Schema(description = "场地名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "总部A座101教室")
    @NotEmpty(message = "场地名称不能为空")
    @Size(max = 30, message = "场地名称长度不能超过30个字符")
    @DiffLogField(name = "场地名称")
    private String name;

    @Schema(description = "所属校区", requiredMode = Schema.RequiredMode.REQUIRED, example = "总部校区")
    @NotEmpty(message = "所属校区不能为空")
    @Size(max = 50, message = "所属校区长度不能超过50个字符")
    @DiffLogField(name = "所属校区")
    private String campus;

    @Schema(description = "所属校区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "总部校区")
    @NotEmpty(message = "所属校区名称不能为空")
    @Size(max = 50, message = "所属校区名称长度不能超过50个字符")
    @DiffLogField(name = "所属校区名称")
    private String campusName;

    @Schema(description = "场地类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "培训教室")
    @NotEmpty(message = "场地类型不能为空")
    @DiffLogField(name = "场地类型")
    private String type;

    @Schema(description = "具体位置", requiredMode = Schema.RequiredMode.REQUIRED, example = "A座1楼101室")
    @NotEmpty(message = "具体位置不能为空")
    @Size(max = 30, message = "具体位置长度不能超过30个字符")
    @DiffLogField(name = "具体位置")
    private String location;

    @Schema(description = "座位类型配置", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "座位类型配置不能为空")
    @Valid
    @DiffLogField(name = "座位类型配置")
    private List<SeatTypeVO> seatTypes;

    @Schema(description = "设备配置", example = "投影仪、音响设备、空调、白板")
    @Size(max = 200, message = "设备配置长度不能超过200个字符")
    @DiffLogField(name = "设备配置")
    private String equipment;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "可用")
    @NotEmpty(message = "状态不能为空")
    @DiffLogField(name = "状态")
    private String status;

    @Schema(description = "场地描述", example = "标准培训教室，适合各类技能培训")
    @Size(max = 200, message = "场地描述长度不能超过200个字符")
    @DiffLogField(name = "场地描述")
    private String description;

    @Schema(description = "负责人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张老师")
    @NotEmpty(message = "负责人姓名不能为空")
    @Size(max = 10, message = "负责人姓名长度不能超过10个字符")
    @DiffLogField(name = "负责人姓名")
    private String manager;

    @Schema(description = "负责人电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "13812345678")
    @NotEmpty(message = "负责人电话不能为空")
    @Size(max = 20, message = "负责人电话长度不能超过20个字符")
    @DiffLogField(name = "负责人电话")
    private String managerPhone;

    /**
     * 座位类型VO
     */
    @Schema(description = "座位类型配置")
    @Data
    public static class SeatTypeVO {

        @Schema(description = "座位类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "普通座")
        @NotEmpty(message = "座位类型名称不能为空")
        private String name;

        @Schema(description = "座位数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "50")
        @NotNull(message = "座位数量不能为空")
        private Integer count;

        @Schema(description = "备注", example = "标准课桌椅")
        private String remark;

    }

}
