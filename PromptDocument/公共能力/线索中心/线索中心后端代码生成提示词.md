\---

description: 

globs: 

alwaysApply: false

\---

请结合`PromptDocument\公共能力\线索中心\线索中心接口文档提示词.md`接口文档直接帮我生成后端代码，

\## 1. 自动生成方案说明

### 步骤一：分析页面
- 读取 `D:\Project\chuanneng\chuanneng-hr-service-vue\src\views\infra\clueCenter\` 下的所有页面，分析页面涉及的业务功能、表单字段、表格展示、操作按钮等。



\### 步骤二：页面设计的相关数据表

-- 1. 线索管理表
DROP TABLE IF EXISTS `publicbiz_lead_info`;
CREATE TABLE `lead_info` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
    `lead_id` VARCHAR(32) NOT NULL COMMENT '线索ID，系统生成的唯一标识',
    `customer_name` VARCHAR(100) NOT NULL COMMENT '客户姓名',
    `customer_phone` VARCHAR(20) NOT NULL COMMENT '联系电话，11位手机号',
    `lead_source` VARCHAR(50) NOT NULL DEFAULT '其他' COMMENT '线索来源：官网注册、市场活动、公众号文章、视频号、抖音、入驻商家、小红书、微博、知乎、百度推广、微信朋友圈、QQ群/微信群、线下展会、合作伙伴推荐、老客户推荐、电话营销、短信营销、邮件营销、其他',
    `business_module` VARCHAR(50) NOT NULL COMMENT '业务模块：高校业务、家政业务、培训业务、认证业务',
    `lead_status` VARCHAR(20) NOT NULL DEFAULT '未处理' COMMENT '线索状态：未处理、跟进中、已转化、无意向',
    `create_method` VARCHAR(20) NOT NULL DEFAULT '手动创建' COMMENT '创建方式：手动创建、系统导入、API接入',
    `current_owner` VARCHAR(64) COMMENT '当前跟进人，可为空表示未分配',
    `remark` TEXT COMMENT '备注信息，最大500字符',
    `tenant_id` BIGINT COMMENT '租户ID',
    `creator` VARCHAR(64) COMMENT '创建人',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` VARCHAR(64) COMMENT '更新人',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
    
    -- 索引
    UNIQUE KEY `uk_lead_id` (`lead_id`),
    KEY `idx_customer_phone` (`customer_phone`),
    KEY `idx_lead_source` (`lead_source`),
    KEY `idx_business_module` (`business_module`),
    KEY `idx_lead_status` (`lead_status`),
    KEY `idx_current_owner` (`current_owner`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索管理表';

-- 2. 线索跟进记录表
DROP TABLE IF EXISTS `publicbiz_lead_follow_up_log`;
CREATE TABLE `lead_follow_up_log` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
    `lead_id` VARCHAR(32) NOT NULL COMMENT '关联的线索ID',
    `follow_up_content` TEXT NOT NULL COMMENT '跟进内容详情',
    `tenant_id` BIGINT COMMENT '租户ID',
    `creator` VARCHAR(64) COMMENT '创建人',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` VARCHAR(64) COMMENT '更新人',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
    
    -- 索引
    KEY `idx_lead_id` (`lead_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索跟进记录表';





\### 步骤三：确定后端接口与分层结构

\- 每个API和页面功能，生成一个对应的后端Controller（RESTful风格）。

\- 每个业务实体，生成VO/DTO、Service接口与实现、Mapper接口（如涉及数据库）、Convert类、枚举、API接口等。

\- 目录结构严格参考 bztmaster-module-system项目，每一层都需要，具体目录格式如下：

bztmaster-module-publicbiz/
├── bztmaster-module-publicbiz-api/
│   ├── pom.xml
│   ├── .flattened-pom.xml
│   └── src/
│       └── main/
│           └── java/
│               └── cn/
│                   └── bztmaster/
│                       └── cnt/
│                           └── module/
│                               └── publicbiz/
│                                   └── api/
│                                       └── leads/
│                                           ├── LeadsApi.java
│                                           └── dto/
│                                               ├── LeadsRespDTO.java
│                                               ├── LeadsSaveReqDTO.java
│                                               ├── LeadsPageReqDTO.java
│                                               └── ...（其它DTO/VO/ReqVO等）
│                                   └── enums/
│                                       └── ...（如有枚举）
├── bztmaster-module-publicbiz-server/
│   ├── pom.xml
│   ├── .flattened-pom.xml
│   ├── Dockerfile
│   └── src/
│       ├── main/
│       │   ├── java/
│       │   │   └── cn/
│       │   │       └── bztmaster/
│       │   │           └── cnt/
│       │   │               └── module/
│       │   │                   └── publicbiz/
│       │   │                       ├── controller/
│       │   │                       │   └── admin/
│       │   │                       │       └── leads/
│       │   │                       │           ├── LeadsController.java
│       │   │                       │           └── vo/
│       │   │                       │               ├── LeadsRespVO.java
│       │   │                       │               ├── LeadsSaveReqVO.java
│       │   │                       │               ├── LeadsPageReqVO.java
│       │   │                       │               └── ...（其它VO/ReqVO等）
│       │   │                       ├── dal/
│       │   │                       │   ├── mysql/
│       │   │                       │   │   └── leads/
│       │   │                       │   │       └── LeadsMapper.java
│       │   │                       │   ├── dataobject/
│       │   │                       │   │   └── leads/
│       │   │                       │   │       └── LeadsDO.java
│       │   │                       ├── service/
│       │   │                       │   └── leads/
│       │   │                       │       ├── impl/
│       │   │                       │       │   └── LeadsServiceImpl.java
│       │   │                       │       └── LeadsService.java
│       │   │                       ├── convert/
│       │   │                       │   └── leads/
│       │   │                       │       └── LeadsConvert.java
│       │   │                       └── ...（其它如 util、framework、job、mq 等可选）
│       │   └── resources/
│       │       └── mapper/
│       │           └── LeadsMapper.xml
│       │       └── application.yaml
│       │       └── ...（其它配置文件）
│       └── test/
│           └── java/
│               └── ...（测试代码结构）
├── pom.xml
└── .flattened-pom.xml



\### 步骤四：自动生成代码

\- Controller：每个API生成一个对应的Controller方法，路径、方法、参数与前端一致，带有Swagger注解。

\- VO/DTO：根据前端参数和表单字段生成请求VO、响应VO，带有参数校验注解。

\- Service/Impl：生成接口和实现，包含CRUD和具体的业务方法，方法注释详细。

\- Mapper/DO：如涉及数据库，生成MyBatis Mapper接口和DO实体，字段与VO/DTO一致。

\- Convert：生成VO与DO、DTO的转换类。

\- Enums：如有枚举字段，自动生成枚举类。

\- API接口：如有远程调用需求，生成Feign接口。

\- 所有类和方法都带有详细注释，对应DTO、VO与DO对象也生成详细注释，便于初学者理解。

\- 前端与后端对应的字段信息一定要确保能对应一致。

  

\### 步骤五：自动补全与TODO

\- 对于前端未明确的字段类型、业务逻辑，自动推断或以// TODO注释标记，便于后续补充。

\- 生成后的代码直接更新到指定的项目bztmaster-module-publicbiz模块下对应的目录中。 



