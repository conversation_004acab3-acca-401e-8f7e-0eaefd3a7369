package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "就业服务-阿姨评级记录 Response VO")
public class PractitionerRatingRecordRespVO {
    @Schema(description = "评级记录ID")
    private Long id;

    @Schema(description = "阿姨ID")
    private Long practitionerId;

    @Schema(description = "评级类型")
    private String ratingType;

    @Schema(description = "原评级")
    private BigDecimal oldRating;

    @Schema(description = "新评级")
    private BigDecimal newRating;

    @Schema(description = "评级变化")
    private BigDecimal ratingChange;

    @Schema(description = "评级原因")
    private String ratingReason;

    @Schema(description = "评价人ID")
    private Long evaluatorId;

    @Schema(description = "评价人姓名")
    private String evaluatorName;

    @Schema(description = "评价人类型")
    private String evaluatorType;

    @Schema(description = "关联订单ID")
    private String relatedOrderId;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
} 