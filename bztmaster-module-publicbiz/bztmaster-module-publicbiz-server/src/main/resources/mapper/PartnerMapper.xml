<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.partner.PartnerMapper">

    <!-- 新增合作伙伴 -->
    <insert id="insert" parameterType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO publicbiz_partner (
            name, short_name, type, biz, status, risk, owner, owner_name, legal_person, foundation_date, credit_code, register_address, business_address, main_business, contact_name, contact_phone, rating, cooperation_mode, contract_no, contract_start, contract_end, deposit, renew_date, account_name, settlement_cycle, bank_name, bank_account, qualification_file, invoice_type, invoice_name, tax_id, org_code, invoice_address, invoice_phone, invoice_bank, invoice_bank_account, invoice_email, invoice_contact, invoice_qualification_file, invoice_remark, create_time, update_time, creator, updater, deleted, tenant_id
        ) VALUES (
            #{name}, #{shortName}, #{type}, #{biz}, #{status}, #{risk}, #{owner}, #{ownerName}, #{legalPerson}, #{foundationDate}, #{creditCode}, #{registerAddress}, #{businessAddress}, #{mainBusiness}, #{contactName}, #{contactPhone}, #{rating}, #{cooperationMode}, #{contractNo}, #{contractStart}, #{contractEnd}, #{deposit}, #{renewDate}, #{accountName}, #{settlementCycle}, #{bankName}, #{bankAccount}, #{qualificationFile}, #{invoiceType}, #{invoiceName}, #{taxId}, #{orgCode}, #{invoiceAddress}, #{invoicePhone}, #{invoiceBank}, #{invoiceBankAccount}, #{invoiceEmail}, #{invoiceContact}, #{invoiceQualificationFile}, #{invoiceRemark}, NOW(), NOW(), #{creator}, #{updater}, #{deleted}, #{tenantId}
        )
    </insert>

    <!-- 更新合作伙伴 -->
    <update id="update" parameterType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO">
        UPDATE publicbiz_partner
        SET name = #{name},
            short_name = #{shortName},
            type = #{type},
            biz = #{biz},
            status = #{status},
            risk = #{risk},
            owner = #{owner},
            owner_name = #{ownerName},
            legal_person = #{legalPerson},
            foundation_date = #{foundationDate},
            credit_code = #{creditCode},
            register_address = #{registerAddress},
            business_address = #{businessAddress},
            main_business = #{mainBusiness},
            contact_name = #{contactName},
            contact_phone = #{contactPhone},
            rating = #{rating},
            cooperation_mode = #{cooperationMode},
            contract_no = #{contractNo},
            contract_start = #{contractStart},
            contract_end = #{contractEnd},
            deposit = #{deposit},
            renew_date = #{renewDate},
            account_name = #{accountName},
            settlement_cycle = #{settlementCycle},
            bank_name = #{bankName},
            bank_account = #{bankAccount},
            qualification_file = #{qualificationFile},
            invoice_type = #{invoiceType},
            invoice_name = #{invoiceName},
            tax_id = #{taxId},
            org_code = #{orgCode},
            invoice_address = #{invoiceAddress},
            invoice_phone = #{invoicePhone},
            invoice_bank = #{invoiceBank},
            invoice_bank_account = #{invoiceBankAccount},
            invoice_email = #{invoiceEmail},
            invoice_contact = #{invoiceContact},
            invoice_qualification_file = #{invoiceQualificationFile},
            invoice_remark = #{invoiceRemark},
            update_time = NOW(),
            updater = #{updater},
            deleted = #{deleted},
            tenant_id = #{tenantId}
        WHERE id = #{id}
    </update>

    <!-- 逻辑删除合作伙伴 -->
    <update id="deleteById">
        UPDATE publicbiz_partner
        SET deleted = 1, update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 根据ID查询合作伙伴 -->
    <select id="selectById" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO">
        SELECT *, owner_name as ownerName FROM publicbiz_partner WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 分页查询合作伙伴 -->
    <select id="selectPage" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO">
        SELECT *, owner_name as ownerName FROM publicbiz_partner
        WHERE deleted = 0
        <if test="type != null and type != ''">AND type = #{type}</if>
        <if test="biz != null and biz != ''">AND biz = #{biz}</if>
        <if test="status != null and status != ''">AND status = #{status}</if>
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') OR owner_name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY id DESC
        LIMIT #{size} OFFSET #{offset}
    </select>

    <!-- 查询总数 -->
    <select id="count" resultType="int">
        SELECT COUNT(1) FROM publicbiz_partner
        WHERE deleted = 0
        <if test="type != null and type != ''">AND type = #{type}</if>
        <if test="biz != null and biz != ''">AND biz = #{biz}</if>
        <if test="status != null and status != ''">AND status = #{status}</if>
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') OR owner_name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
    </select>

    <!-- TODO: 统计卡片等其它SQL -->

</mapper> 