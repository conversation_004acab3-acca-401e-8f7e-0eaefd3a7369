package cn.bztmaster.cnt.module.publicbiz.convert.aunt;

import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.*;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 阿姨注册 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AuntRegisterConvert {

    AuntRegisterConvert INSTANCE = Mappers.getMapper(AuntRegisterConvert.class);

    // VO -> DTO
    AuntRegisterSubmitReqDTO convert(AuntRegisterSubmitReqVO bean);
    AuntRegisterResubmitReqDTO convert(AuntRegisterResubmitReqVO bean);

    // DTO -> VO
    AuntRegisterSubmitRespVO convert(AuntRegisterSubmitRespDTO bean);
    AuntRegisterStatusRespVO convert(AuntRegisterStatusRespDTO bean);
    AuntRegisterDetailRespVO convert(AuntRegisterDetailRespDTO bean);
    AgencyListRespVO convert(AgencyListRespDTO bean);

    // List转换
    List<AgencyListRespVO> convertList(List<AgencyListRespDTO> list);
}
