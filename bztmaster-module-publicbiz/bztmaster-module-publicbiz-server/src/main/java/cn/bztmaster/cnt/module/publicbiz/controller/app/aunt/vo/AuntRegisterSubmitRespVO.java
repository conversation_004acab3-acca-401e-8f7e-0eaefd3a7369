package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 阿姨注册提交响应 VO
 *
 * <AUTHOR>
 */
@Data
public class AuntRegisterSubmitRespVO {

    /**
     * 申请ID
     */
    private String applicationId;

    /**
     * 阿姨OneID
     */
    private String auntOneId;

    /**
     * 申请状态
     */
    private String status;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 预计审核时间
     */
    private String estimatedReviewTime;
}
