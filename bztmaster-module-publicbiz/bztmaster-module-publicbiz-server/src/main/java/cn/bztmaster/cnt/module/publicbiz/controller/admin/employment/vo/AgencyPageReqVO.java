package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 机构分页查询请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构分页查询请求 VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AgencyPageReqVO extends PageParam {

    @Schema(description = "关键词（机构名称或机构ID）", example = "测试")
    private String keyword;

    @Schema(description = "合作状态", example = "cooperating")
    private String cooperationStatus;

    @Schema(description = "审核状态", example = "pending")
    private String reviewStatus;

    @Schema(description = "所属区县", example = "高新区")
    private String district;

    @Schema(description = "机构编码", example = "AG001")
    private String agencyNo;
} 