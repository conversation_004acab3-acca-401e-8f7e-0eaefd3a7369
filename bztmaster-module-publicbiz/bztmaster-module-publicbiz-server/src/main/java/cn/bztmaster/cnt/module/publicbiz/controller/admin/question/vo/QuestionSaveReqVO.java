package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 考题保存 Request VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "资源中心 - 考题保存 Request VO")
public class QuestionSaveReqVO {

    /**
     * 考题ID（编辑时必填）
     */
    @Schema(description = "考题ID", example = "1")
    private Long id;

    /**
     * 一级分类名称
     */
    @Schema(description = "一级分类名称", example = "职业技能等级认定")
    @NotBlank(message = "一级分类名称不能为空")
    @DiffLogField(name = "一级分类名称")
    private String level1Name;

    /**
     * 一级分类代码
     */
    @Schema(description = "一级分类代码", example = "ZY001")
    @NotBlank(message = "一级分类代码不能为空")
    @DiffLogField(name = "一级分类代码")
    private String level1Code;

    /**
     * 二级分类名称
     */
    @Schema(description = "二级分类名称", example = "家政服务类")
    @NotBlank(message = "二级分类名称不能为空")
    @DiffLogField(name = "二级分类名称")
    private String level2Name;

    /**
     * 二级分类代码
     */
    @Schema(description = "二级分类代码", example = "JZ001")
    @NotBlank(message = "二级分类代码不能为空")
    @DiffLogField(name = "二级分类代码")
    private String level2Code;

    /**
     * 三级分类名称
     */
    @Schema(description = "三级分类名称", example = "家政服务员")
    @NotBlank(message = "三级分类名称不能为空")
    @DiffLogField(name = "三级分类名称")
    private String level3Name;

    /**
     * 三级分类代码
     */
    @Schema(description = "三级分类代码", example = "JZFW001")
    @NotBlank(message = "三级分类代码不能为空")
    @DiffLogField(name = "三级分类代码")
    private String level3Code;

    /**
     * 认定点名称
     */
    @Schema(description = "认定点名称", example = "职业道德基础")
    @NotBlank(message = "认定点名称不能为空")
    @DiffLogField(name = "认定点名称")
    private String certName;

    /**
     * 认定点代码
     */
    @Schema(description = "认定点代码", example = "KP001")
    @NotBlank(message = "认定点代码不能为空")
    @DiffLogField(name = "认定点代码")
    private String certCode;

    /**
     * 题干内容
     */
    @Schema(description = "题干内容", example = "家政服务员职业道德的核心要求是什么？")
    @NotBlank(message = "题干内容不能为空")
    @DiffLogField(name = "题干内容")
    private String title;

    /**
     * 题型
     */
    @Schema(description = "题型", example = "单选题")
    @NotBlank(message = "题型不能为空")
    @DiffLogField(name = "题型")
    private String type;

    /**
     * 参考答案
     */
    @Schema(description = "参考答案", example = "A")
    @NotBlank(message = "参考答案不能为空")
    @DiffLogField(name = "参考答案")
    private String answer;

    /**
     * 业务模块
     */
    @Schema(description = "业务模块", example = "家政业务")
    @NotBlank(message = "业务模块不能为空")
    private String biz;

    /**
     * 业务模块名称
     */
    @Schema(description = "业务模块名称", example = "家政服务业务")
    private String bizName;

    /**
     * 难度等级：1-简单，2-中等，3-困难
     */
    @Schema(description = "难度等级", example = "1")
    private Integer difficulty;

    /**
     * 题目分值
     */
    @Schema(description = "题目分值", example = "2.0")
    private BigDecimal score;

    /**
     * 答题时间限制（秒）
     */
    @Schema(description = "答题时间限制（秒）", example = "60")
    private Integer timeLimit;

    /**
     * 题目解析
     */
    @Schema(description = "题目解析", example = "家政服务员应当具备良好的职业道德...")
    private String explanation;

    /**
     * 关键词
     */
    @Schema(description = "关键词", example = "职业道德,家政服务")
    private String keywords;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 选项列表
     */
    @Schema(description = "选项列表")
    @Valid
    private List<QuestionOptionVO> options;

}
