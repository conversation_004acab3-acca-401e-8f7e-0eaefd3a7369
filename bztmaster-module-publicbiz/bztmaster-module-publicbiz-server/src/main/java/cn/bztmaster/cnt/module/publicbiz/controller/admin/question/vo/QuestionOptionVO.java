package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 考题选项 VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "考题选项 VO")
public class QuestionOptionVO {

    /**
     * 选项标识：A、B、C、D等
     */
    @Schema(description = "选项标识", example = "A")
    private String optionKey;

    /**
     * 选项内容
     */
    @Schema(description = "选项内容", example = "诚实守信、尊重客户")
    @NotBlank(message = "选项内容不能为空")
    private String optionContent;

    /**
     * 是否正确答案
     */
    @Schema(description = "是否正确答案", example = "true")
    private Boolean isCorrect;

    /**
     * 选项类型：choice-选择项，matchLeft-匹配左列，matchRight-匹配右列
     */
    @Schema(description = "选项类型", example = "choice")
    private String optionType;

    /**
     * 排序序号
     */
    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;

    /**
     * 匹配目标，用于匹配题记录对应关系
     */
    @Schema(description = "匹配目标", example = "B")
    private String matchTarget;

}
