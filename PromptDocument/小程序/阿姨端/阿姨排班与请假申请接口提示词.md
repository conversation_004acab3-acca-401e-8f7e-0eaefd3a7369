# 阿姨排班与请假申请接口提示词

## 概述
本文档为阿姨端排班管理和请假申请功能的后端接口开发提供详细的需求说明和技术规范。基于前端页面展示的业务逻辑，定义了完整的API接口体系。

## 一、考勤统计查询接口

### 1.1 获取阿姨月度考勤统计信息

**接口名称：** 获取阿姨月度考勤统计信息  
**接口路径：** `/publicbiz/aunt/attendance/summary`  
**请求方式：** GET  
**接口描述：** 根据阿姨oneId和年月查询对应月份的考勤统计数据，包括实际出勤天数、调休天数、请假天数等

**请求参数：**
- `oneId` (string, 必填): 阿姨的唯一标识ID
- `year` (int, 必填): 查询年份，如：2024
- `month` (int, 必填): 查询月份，如：2

**响应数据结构：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "workedDays": 20,        // 已工作天数
    "totalWorkDays": 20,     // 总工作日天数（根据月份动态计算）
    "leaveDays": 1,          // 请假天数
    "adjustDays": 0,         // 调休天数
    "attendanceDays": 2,     // 出勤天数
    "year": 2024,            // 查询年份
    "month": 2               // 查询月份
  }
}
```

**业务逻辑说明：**
- 根据传入的年月参数，动态计算该月份的实际工作日总数
- 工作日计算规则：周一到周五为工作日，需考虑法定节假日
- 统计该阿姨在该月份的实际出勤记录
- 统计该阿姨在该月份的请假申请记录（已批准状态）
- 统计该阿姨在该月份的调休申请记录（已批准状态）
- 支持月份切换，实时更新工作日统计数据

---

## 二、请假/调休申请接口

### 2.1 提交请假或调休申请

**接口名称：** 提交请假或调休申请  
**接口路径：** `/publicbiz/aunt/attendance/apply`  
**请求方式：** POST  
**接口描述：** 根据申请类型（请假/调休）生成不同的工单申请数据

**请求参数：**
```json
{
  "oneId": "aunt_001",           // 阿姨oneId
  "applyType": "LEAVE",          // 申请类型：LEAVE-请假，ADJUST-调休
  "startDate": "2025-07-01",     // 开始日期
  "startTime": "09:00",          // 开始时间
  "endDate": "2025-07-03",       // 结束日期
  "endTime": "18:00",            // 结束时间
  "reason": "家中有急事需要处理",   // 申请事由
  "duration": 3                  // 申请时长（天）
}
```

**响应数据结构：**
```json
{
  "code": 0,
  "msg": "申请提交成功",
  "data": {
    "applyId": "apply_20250701001",  // 申请单号
    "status": "PENDING",             // 申请状态：PENDING-审批中
    "createTime": "2025-07-01 10:00:00"
  }
}
```

**业务逻辑说明：**
- 根据申请类型生成不同的工单模板
- 请假类型：生成请假申请工单，需要上级审批
- 调休类型：生成调休申请工单，可能需要协调排班
- 自动计算申请时长（天）
- 设置申请状态为"审批中"
- 生成唯一的申请单号
- 验证申请时间是否与现有排班冲突

---

## 三、历史申请查询接口

### 3.1 获取阿姨历史请假/调休申请列表

**接口名称：** 获取阿姨历史请假/调休申请列表  
**接口路径：** `/publicbiz/aunt/attendance/history`  
**请求方式：** GET  
**接口描述：** 根据阿姨oneId查询相关的历史请假/调休申请数据

**请求参数：**
- `oneId` (string, 必填): 阿姨的唯一标识ID
- `pageNo` (int, 可选): 页码，默认1
- `pageSize` (int, 可选): 每页数量，默认20
- `applyType` (string, 可选): 申请类型筛选：LEAVE-请假，ADJUST-调休，ALL-全部
- `status` (string, 可选): 状态筛选：PENDING-审批中，APPROVED-已批准，REJECTED-已驳回，ALL-全部

**响应数据结构：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 3,
    "list": [
      {
        "applyId": "apply_20250701001",      // 申请单号
        "applyType": "LEAVE",                // 申请类型：LEAVE-请假，ADJUST-调休
        "applyTypeText": "请假申请",          // 申请类型文本
        "startDate": "2025-07-01",           // 开始日期
        "startTime": "09:00",                // 开始时间
        "endDate": "2025-07-03",             // 结束日期
        "endTime": "18:00",                  // 结束时间
        "duration": 3,                       // 申请时长（天）
        "reason": "家中有急事需要处理",        // 申请理由
        "status": "PENDING",                 // 申请状态
        "statusText": "审批中",               // 状态文本
        "createTime": "2025-07-01 10:00:00", // 申请时间
        "approveTime": null,                 // 审批时间
        "approveRemark": null                // 审批备注
      }
    ]
  }
}
```

**业务逻辑说明：**
- 支持分页查询，默认按申请时间倒序排列
- 支持按申请类型和状态进行筛选
- 状态包括：审批中、已批准、已驳回
- 返回申请类型和状态的中文文本，便于前端显示
- 包含完整的申请信息，用于历史记录查看
- 支持模糊搜索申请理由

---

## 四、申请状态更新接口

### 4.1 更新请假/调休申请状态

**接口名称：** 更新请假/调休申请状态  
**接口路径：** `/publicbiz/aunt/attendance/approve`  
**请求方式：** POST  
**接口描述：** 审批人员更新申请状态（批准/驳回）

**请求参数：**
```json
{
  "applyId": "apply_20250701001",  // 申请单号
  "action": "APPROVE",             // 操作：APPROVE-批准，REJECT-驳回
  "remark": "同意申请",             // 审批备注
  "approverId": "admin_001"        // 审批人ID
}
```

**响应数据结构：**
```json
{
  "code": 0,
  "msg": "审批成功",
  "data": {
    "applyId": "apply_20250701001",
    "status": "APPROVED",
    "approveTime": "2025-07-01 14:00:00"
  }
}
```

**业务逻辑说明：**
- 支持批量审批操作
- 审批通过后自动更新考勤统计
- 审批驳回需要填写驳回原因
- 记录审批操作日志
- 审批完成后发送通知给申请人

---

## 五、排班管理接口

### 5.1 获取阿姨排班信息

**接口名称：** 获取阿姨排班信息  
**接口路径：** `/publicbiz/aunt/schedule/info`  
**请求方式：** GET  
**接口描述：** 查询阿姨的排班安排信息

**请求参数：**
- `oneId` (string, 必填): 阿姨的唯一标识ID
- `startDate` (string, 可选): 开始日期，默认当前日期
- `endDate` (string, 可选): 结束日期，默认当前日期后7天

**响应数据结构：**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "auntInfo": {
      "oneId": "aunt_001",
      "name": "张阿姨",
      "phone": "13800138000"
    },
    "scheduleList": [
      {
        "date": "2025-07-01",
        "dayOfWeek": "星期二",
        "isWorkday": true,
        "startTime": "09:00",
        "endTime": "18:00",
        "workHours": 8,
        "status": "CONFIRMED",  // CONFIRMED-已确认，PENDING-待确认，CANCELLED-已取消
        "clientInfo": {
          "clientId": "client_001",
          "clientName": "李女士",
          "address": "北京市朝阳区xxx小区"
        }
      }
    ]
  }
}
```

### 5.2 更新排班状态

**接口名称：** 更新排班状态  
**接口路径：** `/publicbiz/aunt/schedule/update`  
**请求方式：** POST  
**接口描述：** 阿姨确认或取消排班安排

**请求参数：**
```json
{
  "scheduleId": "schedule_001",    // 排班ID
  "oneId": "aunt_001",            // 阿姨oneId
  "action": "CONFIRM",             // 操作：CONFIRM-确认，CANCEL-取消
  "remark": "确认排班"              // 备注信息
}
```

---

## 六、数据库表结构建议

家政阿姨服务任务表：publicbiz_domestic_task  用于任务排班
阿姨服务打卡记录表：service_punch_record     用于阿姨记录打卡。
任务工单表：publicbiz_work_order            用于阿姨申请调休/请假
工单附件表：publicbiz_work_order_attachment
工单处理日志表：publicbiz_work_order_log    阿姨提交申请和管理人员审批后都要生成相关记录信息
```

---

## 七、接口调用流程

### 7.1 考勤统计查询流程
1. 前端传入年月参数
2. 后端计算该月份工作日总数
3. 查询阿姨出勤记录
4. 查询请假/调休申请记录
5. 汇总统计数据返回

### 7.2 请假申请流程
1. 前端提交申请信息
2. 后端验证申请时间有效性
3. 检查与现有排班冲突
4. 生成申请工单
5. 发送审批通知

### 7.3 审批流程
1. 审批人员查看申请详情
2. 进行审批操作（批准/驳回）
3. 更新申请状态
4. 更新考勤统计
5. 发送结果通知

---

## 八、注意事项

1. **数据一致性**：请假/调休申请审批通过后，需要同步更新考勤统计数据
2. **时间冲突检查**：申请时间不能与现有排班安排冲突
3. **权限控制**：只有指定审批人员可以处理申请
4. **日志记录**：所有关键操作需要记录操作日志
5. **通知机制**：申请提交、审批完成等关键节点需要发送通知
6. **数据缓存**：考勤统计数据可以适当缓存，提高查询性能

---

## 九、错误码定义

- `0`: 成功
- `1001`: 参数错误
- `1002`: 阿姨信息不存在
- `1003`: 申请时间冲突
- `1004`: 申请单号已存在
- `1005`: 无权限操作
- `1006`: 申请状态不允许修改
- `1007`: 排班信息不存在
- `1008`: 工作日配置错误

---

---
*本文档为后端接口开发提供参考，具体实现时可根据实际业务需求进行调整。*
