package cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 管理后台 - 场地预约 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 场地预约 Response VO")
@Data
public class SiteAppointmentRespVO {

    @Schema(description = "预约ID", example = "1")
    private Long id;

    @Schema(description = "场地ID", example = "1")
    private Long siteId;

    @Schema(description = "场地名称", example = "总部A座101教室")
    private String siteName;

    @Schema(description = "场地所属校区名称", example = "总部校区")
    private String siteCampusName;

    @Schema(description = "活动名称", example = "家政服务员技能培训")
    private String activityName;

    @Schema(description = "活动类型", example = "培训")
    private String activityType;

    @Schema(description = "开始日期", example = "2025-08-15")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2025-08-15")
    private LocalDate endDate;

    @Schema(description = "开始时间", example = "09:00")
    private LocalTime startTime;

    @Schema(description = "结束时间", example = "17:00")
    private LocalTime endTime;

    @Schema(description = "时间段描述", example = "09:00-17:00")
    private String timeRange;

    @Schema(description = "预计人数", example = "35")
    private Integer peopleCount;

    @Schema(description = "联系人姓名", example = "张老师")
    private String contactName;

    @Schema(description = "联系电话", example = "13812345678")
    private String contactPhone;

    @Schema(description = "预约状态", example = "已确认")
    private String status;

    @Schema(description = "备注信息", example = "需要投影设备和音响")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
