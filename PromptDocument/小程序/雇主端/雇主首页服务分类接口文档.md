# 雇主首页服务分类接口文档

## 接口概述

该接口用于获取雇主首页的服务分类数据，包括新增服务卡片区域和服务分类区域的数据。

## 接口信息

- **接口名称**: 获取服务分类列表
- **请求方式**: GET
- **接口路径**: `/publicbiz/employer/service/categories`
- **接口描述**: 获取雇主首页服务分类数据，包括新增服务卡片和服务分类列表

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| 无 | - | - | 无需传递参数 |

## 响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "serviceCards": [
      {
        "id": 1,
        "name": "钟点保洁",
        "pic_url": "https://example.com/cleaning.png",
        "sort": 1,
        "type": "cleaning"
      },
      {
        "id": 2,
        "name": "金牌月嫂",
        "pic_url": "https://example.com/maternity.png",
        "sort": 2,
        "type": "maternity"
      },
      {
        "id": 3,
        "name": "护工看护",
        "pic_url": "https://example.com/care.png",
        "sort": 3,
        "type": "care"
      }
    ],
    "serviceCategories": [
      {
        "id": 4,
        "name": "日常保洁",
        "pic_url": "https://example.com/daily-cleaning.png",
        "sort": 4
      },
      {
        "id": 5,
        "name": "深度保洁",
        "pic_url": "https://example.com/deep-cleaning.png",
        "sort": 5
      },
      {
        "id": 6,
        "name": "月嫂服务",
        "pic_url": "https://example.com/maternity-service.png",
        "sort": 6
      },
      {
        "id": 7,
        "name": "护工服务",
        "pic_url": "https://example.com/care-service.png",
        "sort": 7
      },
      {
        "id": 8,
        "name": "育儿嫂",
        "pic_url": "https://example.com/babysitter.png",
        "sort": 8
      },
      {
        "id": 9,
        "name": "更多服务",
        "pic_url": "https://example.com/more-service.png",
        "sort": 999
      }
    ]
  }
}
```

### 响应字段说明

#### serviceCards (新增服务卡片)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 分类编号 |
| name | string | 分类名称 |
| pic_url | string | 移动端分类图片URL |
| sort | int | 分类排序 |
| type | string | 分类类型标识 |

#### serviceCategories (服务分类)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 分类编号 |
| name | string | 分类名称 |
| pic_url | string | 移动端分类图片URL |
| sort | int | 分类排序 |

### 错误响应

```json
{
  "code": 500,
  "message": "服务器内部错误",
  "data": null
}
```

## 业务逻辑说明

1. **新增服务卡片区域**：
   - 获取 `publicbiz_service_category` 表中排序前3的分类数据
   - 按照 `sort` 字段升序排列
   - 只返回 `status = 0`（启用状态）且 `deleted = 0`（未删除）的分类

2. **服务分类区域**：
   - 获取除新增服务卡片外的其他分类数据
   - 按照 `sort` 字段升序排列
   - 只返回 `status = 0`（启用状态）且 `deleted = 0`（未删除）的分类
   - 最后一个分类固定为"更多服务"卡片

3. **数据过滤条件**：
   - `status = 0`：启用状态
   - `deleted = 0`：未删除
   - `parent_id = 0`：顶级分类（可根据实际业务需求调整）




## 注意事项

1. 新增服务卡片的3个分类是固定的，从数据库中按排序获取前3个
2. 服务分类区域显示除新增服务卡片外的其他分类
3. "更多服务"卡片是固定的最后一个分类，用于跳转到更多服务页面
4. 所有返回的分类必须是启用状态且未删除
5. 图片URL需要确保可访问性，建议使用CDN加速
