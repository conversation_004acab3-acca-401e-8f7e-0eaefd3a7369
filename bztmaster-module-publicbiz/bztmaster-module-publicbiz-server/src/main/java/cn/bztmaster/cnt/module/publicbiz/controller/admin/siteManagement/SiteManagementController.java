package cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.siteManagement.SiteManagementConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteManagementDO;
import cn.bztmaster.cnt.module.publicbiz.service.siteManagement.SiteManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 场地管理
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 场地管理")
@RestController
@RequestMapping("/publicbiz/site-management")
@Validated
@Slf4j
public class SiteManagementController {

    @Resource
    private SiteManagementService siteManagementService;

    @PostMapping("/create")
    @Operation(summary = "创建场地")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-management:create')")
    public CommonResult<Long> createSiteManagement(@Valid @RequestBody SiteManagementSaveReqVO createReqVO) {
        return success(siteManagementService.createSiteManagement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新场地")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-management:update')")
    public CommonResult<Boolean> updateSiteManagement(@Valid @RequestBody SiteManagementSaveReqVO updateReqVO) {
        siteManagementService.updateSiteManagement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除场地")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-management:delete')")
    public CommonResult<Boolean> deleteSiteManagement(@PathVariable("id") Long id) {
        siteManagementService.deleteSiteManagement(id);
        return success(true);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获得场地详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-management:query')")
    public CommonResult<SiteManagementRespVO> getSiteManagement(@PathVariable("id") Long id) {
        SiteManagementDO siteManagement = siteManagementService.getSiteManagement(id);
        return success(SiteManagementConvert.INSTANCE.convert(siteManagement));
    }

    @GetMapping("/page")
    @Operation(summary = "获得场地分页")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-management:query')")
    public CommonResult<PageResult<SiteManagementRespVO>> getSiteManagementPage(@Valid SiteManagementPageReqVO pageReqVO) {
        PageResult<SiteManagementDO> pageResult = siteManagementService.getSiteManagementPage(pageReqVO);
        return success(SiteManagementConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list")
    @Operation(summary = "获得场地列表")
    public CommonResult<List<SiteManagementRespVO>> getSiteManagementList(@Valid SiteManagementListReqVO listReqVO) {
        List<SiteManagementDO> list = siteManagementService.getSiteManagementList(listReqVO);
        return success(SiteManagementConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/statistics")
    @Operation(summary = "获得场地统计")
    @Parameter(name = "campus", description = "校区筛选", example = "总部校区")
    @Parameter(name = "campusName", description = "校区名称筛选", example = "总部校区")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-management:query')")
    public CommonResult<SiteManagementStatisticsRespVO> getSiteManagementStatistics(
            @RequestParam(value = "campus", required = false) String campus,
            @RequestParam(value = "campusName", required = false) String campusName) {
        return success(siteManagementService.getSiteManagementStatistics(campus, campusName));
    }

}
