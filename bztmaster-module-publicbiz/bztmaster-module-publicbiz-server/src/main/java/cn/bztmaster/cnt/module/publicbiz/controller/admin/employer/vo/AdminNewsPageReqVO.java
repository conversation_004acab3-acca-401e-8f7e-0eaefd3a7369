package cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 管理后台 - 资讯分页查询请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 资讯分页查询请求 VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminNewsPageReqVO extends PageParam {

    @Schema(description = "资讯标题，支持模糊查询", example = "行业动态")
    private String newsTitle;

    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    @Schema(description = "状态：draft-草稿/published-已发布/offline-已下架", example = "published")
    private String status;

    @Schema(description = "内容来源：manual-手动编写/material-关联素材库", example = "manual")
    private String contentSource;

    @Schema(description = "创建时间开始，格式：yyyy-MM-dd HH:mm:ss", example = "2024-01-01 00:00:00")
    private LocalDateTime startTime;

    @Schema(description = "创建时间结束，格式：yyyy-MM-dd HH:mm:ss", example = "2024-12-31 23:59:59")
    private LocalDateTime endTime;

} 