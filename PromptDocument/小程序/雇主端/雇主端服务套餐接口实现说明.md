# 雇主端服务套餐接口实现说明

## 概述

本文档描述了在 `bztmaster-module-publicbiz` 模块下实现的雇主端服务套餐页面接口，严格按照附件接口文档的业务逻辑进行实现。

## 实现功能

### 1. 核心接口

**接口地址**: `GET /publicbiz/employer/service-category/packagelists`

**功能**: 根据分类ID获取服务套餐列表，支持搜索、排序、分页

### 2. 主要特性

#### 2.1 搜索功能
- ✅ 套餐名称模糊搜索
- ✅ 分类ID筛选
- ✅ 关键词搜索时忽略分类ID参数
- ✅ 搜索不区分大小写，支持中文、英文、数字

#### 2.2 排序功能
- ✅ **距离优先**: 使用Haversine公式计算距离，按距离升序
- ✅ **价格优先**: 按价格升序，相同价格按创建时间倒序
- ✅ **满意度优先**: 按满意度评分降序，相同满意度按距离排序
- ✅ **综合排序**: 按综合评分公式计算，权重分配为机构评分40% + 距离25% + 满意度25% + 价格10%

#### 2.3 距离计算
- ✅ 使用Haversine公式计算球面距离
- ✅ 基于用户位置和机构位置计算
- ✅ 单位为公里
- ✅ 位置获取失败时使用默认位置（成都）

#### 2.4 数据过滤
- ✅ 只返回 `status='active'` 且 `audit_status='approved'` 的套餐
- ✅ 特色标签按 `sort_order` 排序，最多返回5个
- ✅ 无评价时默认评分0分

### 3. 项目结构

```
bztmaster-module-publicbiz-server/
├── src/main/java/cn/bztmaster/cnt/module/publicbiz/
│   ├── controller/employer/
│   │   ├── ServicePackageController.java          # 控制器
│   │   └── vo/
│   │       ├── ServicePackageQueryReqVO.java      # 查询请求对象
│   │       ├── ServicePackageRespVO.java          # 响应对象
│   │       └── ServicePackagePageRespVO.java      # 分页响应对象
│   ├── service/
│   │   ├── ServicePackageService.java             # 服务接口
│   │   └── impl/ServicePackageServiceImpl.java    # 服务实现
│   ├── convert/
│   │   └── ServicePackageConvert.java             # 对象转换
│   ├── dal/
│   │   ├── mysql/
│   │   │   ├── ServicePackageMapper.java          # 套餐Mapper
│   │   │   ├── AgencyMapper.java                  # 机构Mapper
│   │   │   ├── PackageFeatureMapper.java          # 特色标签Mapper
│   │   │   └── AuntReviewMapper.java              # 阿姨评价Mapper
│   │   └── dataobject/
│   │       ├── ServicePackageDO.java              # 套餐DO
│   │       ├── AgencyDO.java                      # 机构DO
│   │       ├── PackageFeatureDO.java              # 特色标签DO
│   │       └── AuntReviewDO.java                  # 阿姨评价DO
│   ├── util/
│   │   └── DistanceUtil.java                      # 距离计算工具
│   └── exception/
│       └── ServicePackageException.java           # 异常处理
└── src/main/resources/mapper/publicbiz/
    ├── ServicePackageMapper.xml                   # 套餐Mapper XML
    ├── AgencyMapper.xml                           # 机构Mapper XML
    ├── PackageFeatureMapper.xml                   # 特色标签Mapper XML
    └── AuntReviewMapper.xml                       # 阿姨评价Mapper XML
```

### 4. 核心业务逻辑

#### 4.1 综合排序算法

严格按照文档公式实现：

```
综合评分 = 机构评分权重×40% + 距离权重×25% + 满意度权重×25% + 价格权重×10%
```

**各维度权重分数计算**:
- **机构评分权重分数** = `(机构所有套餐的综合评分 / 5.0) × 100`
- **距离权重分数** = `(1 - 实际距离/最大距离) × 100`（距离越近分数越高）
- **满意度权重分数** = `(该套餐满意度评分 / 5.0) × 100`
- **价格权重分数** = `(1 - 套餐价格/同类套餐最高价格) × 100`（价格越低分数越高）

#### 4.2 满意度计算

- 根据 `publicbiz_aunt_review` 表的 `rating` 字段计算平均评分
- 无评价时默认评分为0分
- 满意度优先排序时按 `(套餐综合评分 / 5.0) × 100` 计算

#### 4.3 距离计算

- 使用Haversine公式计算球面距离
- 在SQL层面实现距离计算，提高性能
- 位置无效时使用默认位置（成都：104.0665°E, 30.5728°N）

### 5. 缓存机制

#### 5.1 缓存策略
- ✅ 机构信息缓存：`@Cacheable(value = "agency", key = "#packageList.![agencyId]")`
- ✅ 特色标签缓存：`@Cacheable(value = "package_features", key = "#packageList.![id]")`
- ✅ 搜索结果根据关键词和位置进行缓存

#### 5.2 缓存配置
- 使用Spring Cache注解实现缓存
- 支持Redis缓存（需要配置Redis）
- 缓存过期时间可配置

### 6. 错误处理

#### 6.1 异常处理机制
- ✅ 参数验证失败：返回400错误码
- ✅ 位置信息无效：使用默认位置并记录警告日志
- ✅ 网络请求失败：返回友好的错误提示
- ✅ 数据为空：返回空列表
- ✅ 系统异常：记录错误日志并返回通用错误信息

#### 6.2 自定义异常
- `ServicePackageException`: 服务套餐专用异常类
- 支持不同错误码和错误信息
- 提供静态方法创建常见异常

### 7. 安全考虑

#### 7.1 参数验证
- ✅ 经纬度范围验证（经度：-180到180，纬度：-90到90）
- ✅ 分页参数验证
- ✅ SQL注入防护（使用MyBatis参数绑定）

#### 7.2 数据安全
- ✅ XSS防护（特色标签内容过滤）
- ✅ 图片URL安全验证
- ✅ 搜索关键词安全过滤

### 8. 性能优化

#### 8.1 数据库优化
- ✅ 使用索引优化查询性能
- ✅ 分页查询使用LIMIT优化
- ✅ 距离计算在SQL层面实现
- ✅ 批量查询减少数据库访问次数

#### 8.2 应用层优化
- ✅ 缓存机制减少重复查询
- ✅ 批量处理减少循环操作
- ✅ 异步处理非关键操作

### 9. 接口响应格式

严格按照文档中的JSON格式返回数据：

```json
{
  "code": 1,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 4,
        "name": "月度保洁服务 | 30天",
        "category": "日常保洁",
        "categoryId": 2,
        "thumbnail": "/static/保洁服务图片.png",
        "price": 2800.00,
        "originalPrice": 3200.00,
        "unit": "天",
        "serviceDuration": "30天 | 每日2小时",
        "packageType": "long-term",
        "serviceDescription": "专业的日常保洁服务，每日2小时，持续30天",
        "status": "active",
        "auditStatus": "approved",
        "agencyId": 1001,
        "agencyName": "金牌家政",
        "agencyLongitude": 116.397128,
        "agencyLatitude": 39.916527,
        "features": ["日常保洁", "固定时间", "专业阿姨"],
        "distance": 0.5,
        "satisfactionRate": 4.8,
        "serviceTimes": 2,
        "validityPeriod": 90,
        "validityPeriodUnit": "day",
        "serviceIntervalType": "weekly",
        "serviceIntervalValue": 1,
        "singleDurationHours": 2,
        "serviceTime": "9:00-13:00",
        "serviceTimeStart": "09:00:00",
        "serviceTimeEnd": "13:00:00",
        "restDayType": "sunday"
      }
    ],
    "total": 2,
    "page": 1,
    "pageSize": 20
  }
}
```

### 10. 测试建议

#### 10.1 功能测试
- 测试不同排序方式的正确性
- 测试搜索功能的准确性
- 测试分页功能的完整性
- 测试距离计算的准确性

#### 10.2 性能测试
- 测试大量数据下的查询性能
- 测试缓存机制的有效性
- 测试并发访问的稳定性

#### 10.3 异常测试
- 测试参数验证的完整性
- 测试异常处理的正确性
- 测试边界条件的处理

### 11. 部署说明

#### 11.1 依赖要求
- Spring Boot 2.x+
- MyBatis Plus
- Redis（可选，用于缓存）
- MySQL 5.7+

#### 11.2 配置要求
- 数据库连接配置
- Redis缓存配置（可选）
- 日志配置
- 跨域配置

#### 11.3 启动说明
1. 确保数据库表结构已创建
2. 配置数据库连接信息
3. 启动应用服务
4. 访问接口进行测试

## 总结

本实现严格按照附件接口文档的要求，完整实现了雇主端服务套餐页面的所有功能，包括搜索、排序、分页、距离计算、满意度计算等核心业务逻辑。代码结构清晰，性能优化到位，错误处理完善，可以满足生产环境的使用需求。 