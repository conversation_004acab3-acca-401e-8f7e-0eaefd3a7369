# 根据机构ID获取机构资质文件接口文档

## 接口概述

**接口名称：** 获取机构资质文件列表  
**接口描述：** 根据机构ID获取该机构的所有资质文件信息，包括营业执照、资质证书、合同文件等  
**接口版本：** v1.0  
**开发状态：** 已完成  

## 接口信息

### 基本信息
- **请求方式：** GET
- **接口路径：** `/publicbiz/agency/qualifications`
- **Content-Type：** application/x-www-form-urlencoded
- **是否需要登录：** 否（@PermitAll）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| agencyId | Long | 是 | 机构ID | 1001 |

### 请求示例

```http
GET /publicbiz/agency/qualifications?agencyId=1001
```

## 响应格式

### 响应结构

```json
{
  "code": 0,
  "data": {
    "agencyId": 1001,
    "agencyName": "某某家政服务有限公司",
    "qualifications": [
      {
        "id": 1,
        "fileType": "business_license",
        "fileTypeName": "营业执照",
        "fileName": "营业执照.jpg",
        "fileUrl": "https://example.com/files/business_license.jpg",
        "fileSize": 1024000,
        "fileExtension": "jpg",
        "sortOrder": 1,
        "status": 1,
        "statusName": "有效"
      }
    ]
  },
  "msg": "操作成功"
}
```

### 响应字段说明

#### 顶层字段
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| code | Integer | 响应状态码，0表示成功 | 0 |
| data | Object | 响应数据 | - |
| msg | String | 响应消息 | "操作成功" |

#### data字段
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| agencyId | Long | 机构ID | 1001 |
| agencyName | String | 机构名称 | "某某家政服务有限公司" |
| qualifications | Array | 资质文件列表 | - |

#### qualifications数组元素字段
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| id | Long | 文件ID | 1 |
| fileType | String | 文件类型编码 | "business_license" |
| fileTypeName | String | 文件类型名称 | "营业执照" |
| fileName | String | 文件名 | "营业执照.jpg" |
| fileUrl | String | 文件URL | "https://example.com/files/business_license.jpg" |
| fileSize | Long | 文件大小（字节） | 1024000 |
| fileExtension | String | 文件扩展名 | "jpg" |
| sortOrder | Integer | 排序值，数字越小越靠前 | 1 |
| status | Integer | 状态值 | 1 |
| statusName | String | 状态名称 | "有效" |

### 文件类型说明

| 文件类型编码 | 文件类型名称 | 说明 |
|-------------|-------------|------|
| business_license | 营业执照 | 企业营业执照 |
| qualification_cert | 资质证书 | 相关行业资质证书 |
| contract | 合同文件 | 合作协议等合同文件 |
| other | 其他 | 其他类型文件 |

### 状态说明

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 无效 | 文件无效，不显示 |
| 1 | 有效 | 文件有效，正常显示 |

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 0 | 操作成功 | 请求成功 |
| ********** | 机构资质文件不存在 | 指定的机构不存在或没有资质文件 |

## 完整响应示例

### 成功响应示例

```json
{
  "code": 0,
  "data": {
    "agencyId": 1001,
    "agencyName": "成都某某家政服务有限公司",
    "qualifications": [
      {
        "id": 1,
        "fileType": "business_license",
        "fileTypeName": "营业执照",
        "fileName": "营业执照.jpg",
        "fileUrl": "https://cdn.example.com/files/business_license_1001.jpg",
        "fileSize": 2048576,
        "fileExtension": "jpg",
        "sortOrder": 1,
        "status": 1,
        "statusName": "有效"
      },
      {
        "id": 2,
        "fileType": "qualification_cert",
        "fileTypeName": "资质证书",
        "fileName": "家政服务资质证书.pdf",
        "fileUrl": "https://cdn.example.com/files/qualification_cert_1001.pdf",
        "fileSize": 1048576,
        "fileExtension": "pdf",
        "sortOrder": 2,
        "status": 1,
        "statusName": "有效"
      },
      {
        "id": 3,
        "fileType": "contract",
        "fileTypeName": "合同文件",
        "fileName": "合作协议.pdf",
        "fileUrl": "https://cdn.example.com/files/contract_1001.pdf",
        "fileSize": 512000,
        "fileExtension": "pdf",
        "sortOrder": 3,
        "status": 1,
        "statusName": "有效"
      }
    ]
  },
  "msg": "操作成功"
}
```

### 错误响应示例

```json
{
  "code": **********,
  "data": null,
  "msg": "机构资质文件不存在"
}
```

## 注意事项

1. **文件访问权限：** 返回的文件URL可能需要特定的访问权限，请确保前端有权限访问这些文件
2. **文件大小限制：** 建议对文件大小进行限制，避免过大的文件影响加载速度
3. **文件格式支持：** 目前支持常见的图片格式（jpg、png、gif）和文档格式（pdf、doc、docx）
4. **排序规则：** 文件按 `sortOrder` 字段升序排列，相同排序值的按ID升序排列
5. **状态过滤：** 只返回状态为"有效"的文件，无效文件不会返回
6. **错误处理：** 请妥善处理网络错误和业务错误，给用户友好的提示信息

## 更新日志

| 版本 | 更新日期 | 更新内容 |
|------|----------|----------|
| v1.0 | 2024-01-18 | 初始版本，支持基本的机构资质文件查询功能 |

## 联系方式

如有问题，请联系后端开发团队或查看相关技术文档。
