package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer;

import lombok.Data;

/**
 * 用户标签详情 DTO
 * 用于JOIN查询结果
 *
 * <AUTHOR>
 */
@Data
public class TalentUserTagDetailDO {

    /**
     * 用户标签关联ID
     */
    private Long userTagId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 标签类型ID
     */
    private Long tagTypeId;

    /**
     * 标签编码
     */
    private String tagCode;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签描述
     */
    private String tagDescription;

    /**
     * 标签类型编码
     */
    private String typeCode;

    /**
     * 标签类型名称
     */
    private String typeName;

    /**
     * 标签类型描述
     */
    private String typeDescription;
}
