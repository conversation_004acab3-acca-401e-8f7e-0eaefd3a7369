# 套餐详情接口文档

## 1. 获取服务套餐详情

### 接口地址
```
GET /publicbiz/employer/service-package/detail
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 服务套餐ID |

### 请求示例
```javascript
uni.request({
  url: '/publicbiz/employer/service-package/detail',
  data: { 
    id: 1
  },
  success: (res) => {
    console.log('服务套餐详情:', res.data)
  }
})
```

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 服务套餐详情数据 |

### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "月度保洁服务 | 30天",
    "category": "日常保洁",
    "thumbnail": "https://example.com/package1.png",
    "price": 2800.00,
    "originalPrice": 3200.00,
    "unit": "月",
    "serviceDuration": "30天",
    "packageType": "long-term",
    "serviceDescription": "专业保洁服务，每日2小时，30天服务周期",
    "serviceDetails": "<p>详细服务内容...</p>",
    "serviceProcess": "<p>服务流程...</p>",
    "purchaseNotice": "购买须知...",
    "status": "active",
    "advanceBookingDays": 1,
    "timeSelectionMode": "fixed",
    "appointmentMode": "start-date",
    "serviceStartTime": "within-3-days",
    "addressSetting": "fixed",
    "maxBookingDays": 30,
    "cancellationPolicy": "取消政策...",
    "auditStatus": "approved",
    "agencyId": 1001,
    "agencyName": "金牌家政服务有限公司",
    "categoryId": 1,
    "serviceTimeStart": "09:00:00",
    "serviceTimeEnd": "11:00:00",
    "restDayType": "sunday",
    "serviceTimespan": "9:00-11:00",
    "serviceTimes": 30,
    "validityPeriod": 30,
    "validityPeriodUnit": "day",
    "serviceIntervalType": "daily",
    "serviceIntervalValue": 1,
    "singleDurationHours": 2,
    "createTime": "2024-01-01 10:00:00",
    "updateTime": "2024-01-01 10:00:00",
    // 前端展示所需字段
    "title": "月度保洁服务 | 30天",
    "duration": "30天 | 每日2小时",
    "image": "https://example.com/package1.png",
    "tag": "长周期套餐",
    "currentPrice": "2800.00",
    "originalPrice": "3200.00",
    "serviceCount": "已服务 129次",
    "specification": "月嫂:三星级",
    // 服务图片数组，支持多张图片轮播
    "images": [
      "https://example.com/package1.png",
      "https://example.com/package2.png",
      "https://example.com/package3.png"
    ]
  }
}
```

### 数据库查询
```sql
-- 查询套餐基本信息
SELECT 
  sp.id, sp.name, sp.category, sp.thumbnail, sp.price, sp.original_price, sp.unit,
  sp.service_duration, sp.package_type, sp.service_description, sp.service_details,
  sp.service_process, sp.purchase_notice, sp.status, sp.advance_booking_days,
  sp.time_selection_mode, sp.appointment_mode, sp.service_start_time,
  sp.address_setting, sp.max_booking_days, sp.cancellation_policy,
  sp.audit_status, sp.agency_id, sp.agency_name, sp.category_id,
  sp.service_time_start, sp.service_time_end, sp.rest_day_type,
  sp.service_timespan, sp.service_times, sp.validity_period, sp.validity_period_unit,
  sp.service_interval_type, sp.service_interval_value, sp.single_duration_hours,
  sp.create_time, sp.update_time
FROM publicbiz_service_package sp
WHERE sp.id = ? AND sp.deleted = 0 AND sp.status = 'active'
  AND sp.audit_status = 'approved'

-- 查询套餐轮播图
SELECT 
  pc.image_url, pc.sort_order
FROM publicbiz_package_carousel pc
WHERE pc.package_id = ? AND pc.deleted = 0 AND pc.status = 1
ORDER BY pc.sort_order ASC
```

## 2. 获取套餐评价列表

### 接口地址
```
GET /publicbiz/employer/service-package/reviews
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| packageId | Long | 是 | 服务套餐ID |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页数量，默认10 |

### 请求示例
```javascript
uni.request({
  url: '/publicbiz/employer/service-package/reviews',
  data: { 
    packageId: 1,
    page: 1,
    size: 10
  },
  success: (res) => {
    console.log('套餐评价列表:', res.data)
  }
})
```

### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 50,
    "pages": 5,
    "current": 1,
    "size": 10,
    "records": [
      {
        "id": 1,
        "orderId": 1001,
        "auntId": "aunt-001",
        "reviewerId": 2001,
        "reviewerName": "张女士",
        "reviewerAvatar": "https://example.com/avatar1.png",
        "rating": 5.0,
        "reviewTags": ["专业", "细心", "守时"],
        "reviewContent": "服务很专业，月嫂阿姨很有经验，对宝宝照顾得很周到，推荐！",
        "reviewImages": [
          "https://example.com/review1.png",
          "https://example.com/review2.png"
        ],
        "reviewType": "service",
        "isAnonymous": 0,
        "isRecommend": 1,
        "likeCount": 12,
        "replyContent": "感谢您的评价，我们会继续努力提供更好的服务！",
        "replyTime": "2024-01-16 10:00:00",
        "status": 1,
        "agencyId": 1001,
        "servicePackageId": 1,
        "createTime": "2024-01-15 15:30:00",
        // 前端展示所需字段
        "name": "张女士",
        "avatar": "https://example.com/avatar1.png",
        "date": "2024-01-15",
        "content": "服务很专业，月嫂阿姨很有经验，对宝宝照顾得很周到，推荐！"
      }
    ]
  }
}
```

### 数据库查询
```sql
SELECT 
  ar.id, ar.order_id, ar.aunt_id, ar.reviewer_id, ar.reviewer_name, 
  ar.reviewer_avatar, ar.rating, ar.review_tags, ar.review_content, 
  ar.review_images, ar.review_type, ar.is_anonymous, ar.is_recommend, 
  ar.like_count, ar.reply_content, ar.reply_time, ar.status, 
  ar.agency_id, ar.service_package_id, ar.create_time
FROM publicbiz_aunt_review ar
WHERE ar.service_package_id = ? AND ar.deleted = 0 AND ar.status = 1
ORDER BY ar.create_time DESC
LIMIT ? OFFSET ?
```

## 3. 获取套餐所属机构详情

### 接口地址
```
GET /publicbiz/employer/service-package/agency
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| packageId | Long | 是 | 服务套餐ID |

### 请求示例
```javascript
uni.request({
  url: '/publicbiz/employer/service-package/agency',
  data: { 
    packageId: 1
  },
  success: (res) => {
    console.log('机构详情:', res.data)
  }
})
```

### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1001,
    "agencyNo": "AG20241201001",
    "name": "金牌家政服务有限公司",
    "shortName": "金牌家政",
    "agencyType": "cooperation",
    "legalRepresentative": "张三",
    "unifiedSocialCreditCode": "91410100MA12345678",
    "establishmentDate": "2016-01-01",
    "registeredAddress": "河南省郑州市二七区大学路128号",
    "operatingAddress": "河南省郑州市二七区大学路128号",
    "businessScope": "家政服务、保洁服务、月嫂服务等",
    "contactPerson": "李四",
    "contactPhone": "0371-8888-8888",
    "contactEmail": "<EMAIL>",
    "agencyAddress": "郑州市二七区大学路128号",
    "province": "河南省",
    "city": "郑州市",
    "district": "二七区",
    "street": "大学路街道",
    "detailAddress": "128号",
    "longitude": 113.6401,
    "latitude": 34.7236,
    "locationAccuracy": "high",
    "cooperationStatus": "cooperating",
    "contractNo": "HT20241201001",
    "contractStartDate": "2024-01-01",
    "contractEndDate": "2024-12-31",
    "commissionRate": 15.00,
    "reviewStatus": "approved",
    "status": "active",
    "remark": "优质家政服务机构",
    "createTime": "2024-01-01 10:00:00",
    "updateTime": "2024-01-01 10:00:00",
    // 前端展示所需字段
    "logo": "https://example.com/logo.png",
    "banner": "https://example.com/banner.png",
    "rating": 4.5,
    "reviewCount": "2.3万",
    "servedFamilies": "12.5万+",
    "establishedYears": "8年",
    "address": "郑州市二七区大学路128号",
    "phone": "0371-8888-8888",
    // 门头照片
    "storefrontImages": [
      "https://example.com/storefront1.png",
      "https://example.com/storefront2.png",
      "https://example.com/storefront3.png",
      "https://example.com/storefront4.png",
      "https://example.com/storefront5.png"
    ],
    // 资质证照
    "certificates": [
      {
        "name": "营业执照",
        "image": "https://example.com/cert1.png"
      },
      {
        "name": "经营许可证",
        "image": "https://example.com/cert2.png"
      },
      {
        "name": "服务资质证书",
        "image": "https://example.com/cert3.png"
      }
    ]
  }
}
```

### 数据库查询
```sql
-- 查询机构基本信息
SELECT 
  a.id, a.agency_no, a.agency_name, a.agency_short_name, a.agency_type,
  a.legal_representative, a.unified_social_credit_code, a.establishment_date,
  a.registered_address, a.operating_address, a.business_scope,
  a.contact_person, a.contact_phone, a.contact_email,
  a.agency_address, a.province, a.city, a.district, a.street, a.detail_address,
  a.longitude, a.latitude, a.location_accuracy,
  a.cooperation_status, a.contract_no, a.contract_start_date, a.contract_end_date,
  a.commission_rate, a.review_status, a.status, a.remark,
  a.create_time, a.update_time
FROM publicbiz_agency a
INNER JOIN publicbiz_service_package sp ON a.id = sp.agency_id
WHERE sp.id = ? AND a.deleted = 0 AND a.status = 'active'

-- 查询机构资质文件
SELECT 
  aq.file_type, aq.file_name, aq.file_url, aq.sort_order
FROM publicbiz_agency_qualification aq
INNER JOIN publicbiz_service_package sp ON aq.agency_id = sp.agency_id
WHERE sp.id = ? AND aq.deleted = 0 AND aq.status = 1
ORDER BY aq.sort_order ASC
```

## 4. 获取相关推荐套餐

### 接口地址
```
GET /publicbiz/employer/service-package/recommendations
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| packageId | Long | 是 | 当前套餐ID |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页数量，默认10 |

### 请求示例
```javascript
uni.request({
  url: '/publicbiz/employer/service-package/recommendations',
  data: { 
    packageId: 1,
    page: 1,
    size: 10
  },
  success: (res) => {
    console.log('相关推荐:', res.data)
  }
})
```

### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 20,
    "pages": 2,
    "current": 1,
    "size": 10,
    "records": [
      {
        "id": 2,
        "name": "金牌月嫂服务",
        "category": "月嫂服务",
        "thumbnail": "https://example.com/package2.png",
        "price": 8800.00,
        "originalPrice": 9800.00,
        "unit": "月",
        "serviceDuration": "26天",
        "packageType": "long-term",
        "status": "active",
        "auditStatus": "approved",
        "agencyId": 1001,
        "agencyName": "金牌家政服务有限公司",
        "createTime": "2024-01-01 10:00:00",
        // 前端展示所需字段
        "title": "金牌月嫂服务",
        "price": "8,800",
        "image": "https://example.com/package2.png"
      }
    ]
  }
}
```

### 数据库查询
```sql
-- 查询同机构的其他套餐作为推荐
SELECT 
  sp.id, sp.name, sp.category, sp.thumbnail, sp.price, sp.original_price, 
  sp.unit, sp.service_duration, sp.package_type, sp.status, sp.audit_status,
  sp.agency_id, sp.agency_name, sp.create_time
FROM publicbiz_service_package sp
INNER JOIN publicbiz_service_package current_sp ON sp.agency_id = current_sp.agency_id
WHERE current_sp.id = ? 
  AND sp.id != current_sp.id
  AND sp.deleted = 0 AND sp.status = 'active' AND sp.audit_status = 'approved'
ORDER BY sp.create_time DESC
LIMIT ? OFFSET ?
```

## 5. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 套餐不存在 |
| 500 | 服务器内部错误 |

## 6. 注意事项

1. 所有接口都需要进行用户身份验证
2. 套餐ID必须为有效的数字
3. 分页参数page从1开始，size建议不超过50
4. 时间格式统一使用ISO 8601格式：YYYY-MM-DD HH:mm:ss
5. 图片URL需要完整的可访问地址
6. 评价标签使用JSON数组格式存储
7. 金额字段使用decimal类型，保留2位小数
8. 轮播图按sort_order字段排序，数字越小越靠前
9. 机构资质文件按sort_order字段排序
10. 评价列表按创建时间倒序排列
