# 站内信App接口完整架构说明

## 📋 项目概述

本项目为小程序用户提供了完整的站内信功能，包括站内信的查询、分页、标记已读、发送等操作。整个系统采用微服务架构，支持多种调用方式。

## 🏗️ 系统架构

### 整体架构图
```
小程序 → API网关 → 微服务模块
   ↓         ↓         ↓
   oneId  路由转发   业务处理
```

### 接口层次结构
```
┌─────────────────────────────────────────────────────────────┐
│                    小程序客户端                                │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                    API网关                                    │
│              (路由转发和负载均衡)                               │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                微服务模块                                      │
│  ┌─────────────────┬─────────────────┬─────────────────┐   │
│  │   Controller层   │   Service层     │   Mapper层      │   │
│  │   (HTTP接口)     │   (业务逻辑)     │   (数据访问)     │   │
│  └─────────────────┴─────────────────┴─────────────────┘   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                    数据库                                      │
│              (system_notify_message表)                        │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 技术实现

### 1. 数据库设计

#### 表结构变更
在 `system_notify_message` 表中新增 `one_id` 字段：

```sql
-- 添加one_id字段
ALTER TABLE `system_notify_message`
ADD COLUMN `one_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户唯一标识ID（小程序用户标识）' AFTER `user_id`;

-- 添加索引
ALTER TABLE `system_notify_message`
ADD INDEX `idx_one_id` (`one_id`) USING BTREE;
```

#### 字段说明
- `one_id`: 用户唯一标识ID，用于小程序用户标识
- 类型: `varchar(64)`
- 索引: `idx_one_id` (BTREE)
- 用途: 替代传统的 `user_id` 和 `user_type` 进行用户身份验证

### 2. 代码结构

#### 目录结构
```
bztmaster-module-system/
├── bztmaster-module-system-api/           # API接口定义
│   └── src/main/java/cn/bztmaster/cnt/module/system/api/notify/
│       ├── AppNotifyMessageApi.java       # 站内信管理API接口
│       ├── AppNotifySendApi.java          # 站内信发送API接口
│       ├── AppNotifyMessageFeignClient.java # Feign客户端接口
│       └── AppNotifySendFeignClient.java  # Feign客户端接口
└── bztmaster-module-system-server/        # 服务实现
    └── src/main/java/cn/bztmaster/cnt/module/system/
        ├── controller/app/notify/          # App控制器
        │   ├── AppNotifyMessageController.java
        │   ├── AppNotifySendController.java
        │   └── vo/                        # 视图对象
        │       ├── message/
        │       │   ├── AppNotifyMessagePageReqVO.java
        │       │   └── AppNotifyMessageRespVO.java
        │       └── send/
        │           └── AppNotifySendSingleToUserReqVO.java
        ├── service/notify/                 # 服务层
        │   ├── NotifyMessageService.java   # 接口定义
        │   └── NotifyMessageServiceImpl.java # 接口实现
        ├── dal/                            # 数据访问层
        │   ├── dataobject/notify/
        │   │   └── NotifyMessageDO.java   # 数据对象
        │   └── mysql/notify/
        │       └── NotifyMessageMapper.java # Mapper接口
        └── api/notify/                     # API实现
            ├── AppNotifyMessageApiImpl.java
            └── AppNotifySendApiImpl.java
```

### 3. 核心组件

#### Controller层
- **`AppNotifyMessageController`**: 处理站内信相关的HTTP请求
- **`AppNotifySendController`**: 处理站内信发送相关的HTTP请求

#### Service层
- **`NotifyMessageService`**: 定义站内信业务逻辑接口
- **`NotifyMessageServiceImpl`**: 实现站内信业务逻辑

#### Mapper层
- **`NotifyMessageMapper`**: 数据访问接口，提供基于oneId的数据库操作

#### API层
- **`AppNotifyMessageApi`**: 站内信管理API接口定义
- **`AppNotifySendApi`**: 站内信发送API接口定义

#### Feign客户端
- **`AppNotifyMessageFeignClient`**: 支持RPC调用的Feign客户端
- **`AppNotifySendFeignClient`**: 支持RPC调用的Feign客户端

## 🚀 接口功能

### 1. 站内信管理接口

#### 分页查询站内信
- **接口**: `GET /system/notify-message/page`
- **功能**: 根据oneId分页查询用户的站内信
- **参数**: `AppNotifyMessagePageReqVO`
- **返回**: `PageResult<AppNotifyMessageRespVO>`

#### 获取站内信详情
- **接口**: `GET /system/notify-message/get/{id}`
- **功能**: 根据ID和oneId获取站内信详情
- **参数**: `id`, `oneId`
- **返回**: `AppNotifyMessageRespVO`

#### 标记站内信为已读
- **接口**: `PUT /system/notify-message/update-read`
- **功能**: 标记指定站内信为已读状态
- **参数**: `id`, `oneId`
- **返回**: `Boolean`

#### 标记所有站内信为已读
- **接口**: `PUT /system/notify-message/update-all-read`
- **功能**: 标记用户所有站内信为已读状态
- **参数**: `oneId`
- **返回**: `Boolean`

#### 获取未读站内信列表
- **接口**: `GET /system/notify-message/unread-list`
- **功能**: 获取用户所有未读站内信
- **参数**: `oneId`
- **返回**: `List<AppNotifyMessageRespVO>`

#### 获取未读站内信数量
- **接口**: `GET /system/notify-message/unread-count`
- **功能**: 获取用户未读站内信数量
- **参数**: `oneId`
- **返回**: `Long`

### 2. 站内信发送接口

#### 发送单条站内信
- **接口**: `POST /system/notify/send/send-single`
- **功能**: 发送站内信给指定用户
- **参数**: `AppNotifySendSingleToUserReqVO`
- **返回**: `Long` (站内信ID)

## 🔄 调用方式

### 1. 直接调用（小程序）

小程序通过API网关直接调用微服务接口：

```javascript
// 分页查询站内信
const response = await fetch('/system/notify-message/page', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    oneId: 'wx_123456789',
    pageNo: 1,
    pageSize: 10,
    readStatus: false
  })
});

// 标记站内信为已读
await fetch('/system/notify-message/update-read', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    id: 123,
    oneId: 'wx_123456789'
  })
});
```

### 2. RPC调用（微服务间）

其他微服务通过Feign客户端调用：

```java
@Resource
private AppNotifyMessageFeignClient appNotifyMessageFeignClient;

// 分页查询站内信
CommonResult<PageResult<AppNotifyMessageRespVO>> result = 
    appNotifyMessageFeignClient.getMyNotifyMessagePage(pageReqVO);

// 发送站内信
CommonResult<Long> messageId = 
    appNotifySendFeignClient.sendSingleMessageToUser(sendReqVO);
```

### 3. 网关路由

API网关配置路由规则：

```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: system-notify-message
          uri: lb://bztmaster-module-system
          predicates:
            - Path=/system/notify-message/**
        - id: system-notify-send
          uri: lb://bztmaster-module-system
          predicates:
            - Path=/system/notify/send/**
```

## 📱 小程序集成

### 1. 获取oneId

小程序需要获取用户的唯一标识：

```javascript
// 微信小程序
wx.login({
  success: (res) => {
    const code = res.code;
    // 通过code获取openid作为oneId
    this.oneId = openid;
  }
});

// 支付宝小程序
my.getAuthCode({
  scopes: ['auth_base'],
  success: (res) => {
    const code = res.authCode;
    // 通过code获取userId作为oneId
    this.oneId = userId;
  }
});
```

### 2. 调用接口

使用oneId调用站内信接口：

```javascript
class NotifyMessageService {
  constructor(oneId) {
    this.oneId = oneId;
    this.baseUrl = '/system/notify-message';
  }

  // 分页查询
  async getPage(pageNo = 1, pageSize = 10, readStatus = null) {
    const params = {
      oneId: this.oneId,
      pageNo,
      pageSize,
      readStatus
    };
    
    const response = await fetch(`${this.baseUrl}/page`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params)
    });
    
    return response.json();
  }

  // 标记已读
  async markAsRead(messageId) {
    const response = await fetch(`${this.baseUrl}/update-read`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        id: messageId,
        oneId: this.oneId
      })
    });
    
    return response.json();
  }

  // 获取未读数量
  async getUnreadCount() {
    const response = await fetch(`${this.baseUrl}/unread-count?oneId=${this.oneId}`);
    return response.json();
  }
}
```

## 🔒 安全考虑

### 1. oneId验证

- 确保oneId的唯一性和有效性
- 建议对oneId进行加密或签名验证
- 支持oneId的过期和刷新机制

### 2. 权限控制

- 所有接口都需要传入有效的oneId
- 支持多租户环境下的数据隔离
- 考虑添加接口调用频率限制

### 3. 数据安全

- 敏感信息不直接暴露在接口中
- 支持数据脱敏和权限过滤
- 记录关键操作的审计日志

## 📊 性能优化

### 1. 数据库优化

- 为oneId字段添加索引
- 支持分页查询，避免大量数据返回
- 考虑读写分离和分库分表

### 2. 缓存策略

- 缓存用户未读消息数量
- 缓存热门站内信模板
- 支持Redis集群和本地缓存

### 3. 异步处理

- 站内信发送支持异步处理
- 批量操作支持队列处理
- 支持消息推送和实时通知

## 🚀 部署说明

### 1. 环境要求

- Java 8+
- Spring Boot 2.7+
- Spring Cloud Alibaba 2021+
- MySQL 5.7+ 或 PostgreSQL 10+
- Redis 5.0+

### 2. 配置说明

#### 数据库配置
```yaml
spring:
  datasource:
    url: ***********************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

#### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
```

#### 微服务配置
```yaml
spring:
  application:
    name: bztmaster-module-system
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
```

### 3. 启动顺序

1. 启动Nacos注册中心和配置中心
2. 启动Redis缓存服务
3. 启动MySQL数据库服务
4. 执行数据库变更脚本
5. 启动微服务模块
6. 启动API网关服务

### 4. 健康检查

```bash
# 检查服务状态
curl http://localhost:8080/actuator/health

# 检查接口可用性
curl http://localhost:8080/system/notify-message/unread-count?oneId=test_001
```

## 🔍 监控和日志

### 1. 接口监控

- 接口调用次数和响应时间
- 错误率和异常统计
- 接口性能指标监控

### 2. 业务监控

- 站内信发送成功率
- 用户活跃度和使用情况
- 系统资源使用情况

### 3. 日志记录

- 接口调用日志
- 业务操作日志
- 错误和异常日志

## 📈 扩展计划

### 1. 功能扩展

- 支持站内信模板管理
- 支持批量发送和群发
- 支持站内信分类和标签
- 支持站内信搜索和过滤

### 2. 技术升级

- 支持WebSocket实时推送
- 支持消息队列异步处理
- 支持分布式缓存和存储
- 支持微服务链路追踪

### 3. 业务集成

- 集成第三方消息推送服务
- 支持多渠道消息发送
- 支持消息统计和报表
- 支持智能推荐和个性化

## 📝 总结

站内信App接口系统为小程序用户提供了完整的站内信功能，采用微服务架构设计，支持多种调用方式。系统具有良好的扩展性和维护性，能够满足不同场景下的业务需求。

### 主要特点

1. **架构清晰**: 分层设计，职责明确
2. **接口完整**: 覆盖站内信的所有核心功能
3. **调用灵活**: 支持直接调用和RPC调用
4. **性能优化**: 数据库索引、缓存策略、分页查询
5. **安全可靠**: oneId验证、权限控制、数据隔离
6. **易于扩展**: 模块化设计，支持功能扩展

### 技术亮点

1. **oneId设计**: 创新的用户标识方式，适合小程序场景
2. **微服务架构**: 支持服务拆分和独立部署
3. **Feign集成**: 支持微服务间的RPC调用
4. **数据库优化**: 合理的索引设计和查询优化
5. **接口规范**: 统一的请求响应格式和错误处理

该系统为小程序站内信功能提供了完整的技术解决方案，具有良好的实用价值和推广意义。 