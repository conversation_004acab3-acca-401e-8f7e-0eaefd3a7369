package cn.bztmaster.cnt.module.publicbiz.service.employment.impl;

import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationCreateVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyAuntRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyPackageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyReviewStatsRespVO;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.AgencyConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyQualificationDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.AuntReviewDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyQualificationMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.AuntReviewMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyService;
import cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.AGENCY_NOT_EXISTS;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.AGENCY_AGENCY_NO_EXISTS;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.AGENCY_UNIFIED_SOCIAL_CREDIT_CODE_EXISTS;

/**
 * 机构 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AgencyServiceImpl implements AgencyService {

    @Resource
    private AgencyMapper agencyMapper;

    @Resource
    private AgencyQualificationMapper agencyQualificationMapper;

    @Resource
    private PractitionerMapper practitionerMapper;

    @Resource
    private ServicePackageMapper servicePackageMapper;

    @Resource
    private AuntReviewMapper auntReviewMapper;

    @Resource
    private DomesticOrderMapper domesticOrderMapper;

    @Override
    public PageResult<AgencyRespVO> pageAgency(AgencyPageReqVO reqVO) {
        // 分页查询机构列表
        PageResult<AgencyDO> pageResult = agencyMapper.selectPage(reqVO);
        // 转换为VO
        return AgencyConvert.INSTANCE.convertPage(pageResult);
    }

    @Override
    public AgencyRespVO getAgency(Long id) {
        // 获取机构信息
        AgencyDO agency = getAgencyDO(id);
        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS);
        }

        // 获取资质文件列表
        List<AgencyQualificationDO> qualifications = agencyQualificationMapper.selectListByAgencyId(id);

        // 转换为VO
        AgencyRespVO agencyRespVO = AgencyConvert.INSTANCE.convert(agency);
        agencyRespVO.setQualifications(AgencyConvert.INSTANCE.convertQualificationList(qualifications));

        return agencyRespVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.AGENCY_TYPE, subType = LogRecordConstants.AGENCY_REVIEW_SUB_TYPE, bizNo = "{{#reqVO.id}}", success = LogRecordConstants.AGENCY_REVIEW_SUCCESS)
    public void updateAgency(AgencyUpdateReqVO reqVO) {
        // 校验机构是否存在
        AgencyDO agency = getAgencyDO(reqVO.getId());
        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS);
        }

        // 记录更新前的状态
        String oldReviewStatus = agency.getReviewStatus();
        String oldCooperationStatus = agency.getCooperationStatus();

        // 更新审核状态
        agency.setReviewStatus(reqVO.getReviewStatus());
        agency.setReviewRemark(reqVO.getReviewRemark());
        agency.setReviewer(SecurityFrameworkUtils.getLoginUserNickname()); // 从当前登录用户获取
        agency.setReviewTime(LocalDateTime.now());

        // 根据审核状态更新合作状态
        if ("rejected".equals(reqVO.getReviewStatus())) {
            // 审核状态为已拒绝，合作状态修改为已暂停
            agency.setCooperationStatus("suspended");
        } else if ("approved".equals(reqVO.getReviewStatus())) {
            // 审核状态为已通过，合作状态为合作中
            agency.setCooperationStatus("cooperating");
        }

        // 执行数据库更新
        int updateResult = agencyMapper.updateById(agency);
        if (updateResult <= 0) {
            throw new RuntimeException("更新机构审核状态失败");
        }

        // 设置操作日志上下文 - 必须在方法返回前设置
        LogRecordContext.putVariable("agency", agency);
        LogRecordContext.putVariable("review", reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.AGENCY_TYPE, subType = LogRecordConstants.AGENCY_CREATE_SUB_TYPE, bizNo = "{{#agency.id}}", success = LogRecordConstants.AGENCY_CREATE_SUCCESS)
    public Long createAgency(AgencyCreateReqVO reqVO) {
        // 校验机构编号是否已存在
        AgencyDO existingAgency = getAgencyDOByAgencyNo(reqVO.getAgencyNo());
        if (existingAgency != null) {
            throw ServiceExceptionUtil.exception(AGENCY_AGENCY_NO_EXISTS);
        }

        // 校验统一社会信用代码是否已存在
        if (reqVO.getUnifiedSocialCreditCode() != null) {
            AgencyDO existingByCode = agencyMapper.selectByUnifiedSocialCreditCode(reqVO.getUnifiedSocialCreditCode());
            if (existingByCode != null) {
                throw ServiceExceptionUtil.exception(AGENCY_UNIFIED_SOCIAL_CREDIT_CODE_EXISTS);
            }
        }

        // 转换为DO
        AgencyDO agency = AgencyConvert.INSTANCE.convert(reqVO);

        // 设置默认值
        agency.setCooperationStatus("pending");
        agency.setReviewStatus("pending");
        agency.setStatus("active");

        // 保存机构信息
        agencyMapper.insert(agency);

        // 保存资质文件信息
        if (reqVO.getQualifications() != null && !reqVO.getQualifications().isEmpty()) {
            for (AgencyQualificationCreateVO qualificationVO : reqVO.getQualifications()) {
                AgencyQualificationDO qualification = AgencyConvert.INSTANCE.convert(qualificationVO);
                qualification.setAgencyId(agency.getId());
                agencyQualificationMapper.insert(qualification);
            }
        }

        // 设置操作日志上下文
        LogRecordContext.putVariable("agency", agency);

        return agency.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.AGENCY_TYPE, subType = LogRecordConstants.AGENCY_DELETE_SUB_TYPE, bizNo = "{{#agency.id}}", success = LogRecordConstants.AGENCY_DELETE_SUCCESS)
    public void deleteAgency(Long id) {
        // 校验机构是否存在
        AgencyDO agency = getAgencyDO(id);
        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS);
        }

        // 删除资质文件
        agencyQualificationMapper.deleteByAgencyId(id);

        // 删除机构
        agencyMapper.deleteById(id);

        // 设置操作日志上下文
        LogRecordContext.putVariable("agency", agency);
    }

    @Override
    public AgencyDO getAgencyDO(Long id) {
        return agencyMapper.selectById(id);
    }

    @Override
    public AgencyDO getAgencyDOByAgencyNo(String agencyNo) {
        return agencyMapper.selectByAgencyNo(agencyNo);
    }

    // ========== 机构详情模块方法实现 ==========

    @Override
    public AgencyDetailRespVO getAgencyDetail(Long id) {
        // 查询机构基本信息
        AgencyDO agency = agencyMapper.selectOne(
                new LambdaQueryWrapperX<AgencyDO>()
                        .eq(AgencyDO::getId, id)
                        .eq(AgencyDO::getDeleted, false)
                        .eq(AgencyDO::getStatus, "active"));

        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS);
        }

        // 转换为响应对象
        AgencyDetailRespVO respVO = new AgencyDetailRespVO();
        respVO.setId(agency.getId());
        respVO.setAgencyNo(agency.getAgencyNo());
        respVO.setName(agency.getAgencyName());
        respVO.setShortName(agency.getAgencyShortName());
        respVO.setAgencyType(agency.getAgencyType());
        respVO.setLegalRepresentative(agency.getLegalRepresentative());
        respVO.setUnifiedSocialCreditCode(agency.getUnifiedSocialCreditCode());
        respVO.setEstablishmentDate(
                agency.getEstablishmentDate() != null ? agency.getEstablishmentDate().toString() : null);
        respVO.setRegisteredAddress(agency.getRegisteredAddress());
        respVO.setOperatingAddress(agency.getOperatingAddress());
        respVO.setBusinessScope(agency.getBusinessScope());
        respVO.setContactPerson(agency.getContactPerson());
        respVO.setContactPhone(agency.getContactPhone());
        respVO.setContactEmail(agency.getContactEmail());
        respVO.setAgencyAddress(agency.getAgencyAddress());
        respVO.setProvince(agency.getProvince());
        respVO.setCity(agency.getCity());
        respVO.setDistrict(agency.getDistrict());
        respVO.setStreet(agency.getStreet());
        respVO.setDetailAddress(agency.getDetailAddress());
        respVO.setLongitude(agency.getLongitude());
        respVO.setLatitude(agency.getLatitude());
        respVO.setLocationAccuracy(agency.getLocationAccuracy());
        respVO.setCooperationStatus(agency.getCooperationStatus());
        respVO.setContractNo(agency.getContractNo());
        respVO.setContractStartDate(
                agency.getContractStartDate() != null ? agency.getContractStartDate().toString() : null);
        respVO.setContractEndDate(agency.getContractEndDate() != null ? agency.getContractEndDate().toString() : null);
        respVO.setCommissionRate(agency.getCommissionRate());
        respVO.setReviewStatus(agency.getReviewStatus());
        respVO.setStatus(agency.getStatus());
        respVO.setRemark(agency.getRemark());
        respVO.setCreateTime(agency.getCreateTime());
        respVO.setUpdateTime(agency.getUpdateTime());

        // 计算前端展示字段
        calculateDisplayFields(respVO, agency);

        return respVO;
    }

    @Override
    public PageResult<AgencyAuntRespVO> getAgencyAunts(Long agencyId, Integer page, Integer size) {
        // 查询阿姨列表
        List<PractitionerDO> practitionerList = practitionerMapper.selectList(
                new LambdaQueryWrapperX<PractitionerDO>()
                        .eq(PractitionerDO::getAgencyId, agencyId)
                        .eq(PractitionerDO::getDeleted, false)
                        .eq(PractitionerDO::getStatus, "active")
                        .eq(PractitionerDO::getPlatformStatus, "cooperating")
                        .orderByDesc(PractitionerDO::getRating)
                        .orderByDesc(PractitionerDO::getTotalOrders)
                        .last("LIMIT " + (page - 1) * size + ", " + size));

        // 查询总数
        Long total = practitionerMapper.selectCount(
                new LambdaQueryWrapperX<PractitionerDO>()
                        .eq(PractitionerDO::getAgencyId, agencyId)
                        .eq(PractitionerDO::getDeleted, false)
                        .eq(PractitionerDO::getStatus, "active")
                        .eq(PractitionerDO::getPlatformStatus, "cooperating"));

        // 转换为响应对象
        List<AgencyAuntRespVO> respList = practitionerList.stream()
                .map(this::convertToAuntRespVO)
                .collect(Collectors.toList());

        return new PageResult<>(respList, total);
    }

    @Override
    public PageResult<AgencyPackageRespVO> getAgencyPackages(Long agencyId, Integer page, Integer size) {
        // 查询套餐列表
        List<ServicePackageDO> packageList = servicePackageMapper.selectList(
                new LambdaQueryWrapperX<ServicePackageDO>()
                        .eq(ServicePackageDO::getAgencyId, agencyId)
                        .eq(ServicePackageDO::getDeleted, false)
                        .eq(ServicePackageDO::getStatus, "active")
                        .eq(ServicePackageDO::getAuditStatus, "approved")
                        .orderByDesc(ServicePackageDO::getCreateTime)
                        .last("LIMIT " + (page - 1) * size + ", " + size));

        // 查询总数
        Long total = servicePackageMapper.selectCount(
                new LambdaQueryWrapperX<ServicePackageDO>()
                        .eq(ServicePackageDO::getAgencyId, agencyId)
                        .eq(ServicePackageDO::getDeleted, false)
                        .eq(ServicePackageDO::getStatus, "active")
                        .eq(ServicePackageDO::getAuditStatus, "approved"));

        // 转换为响应对象
        List<AgencyPackageRespVO> respList = packageList.stream()
                .map(this::convertToPackageRespVO)
                .collect(Collectors.toList());

        return new PageResult<>(respList, total);
    }

    @Override
    public AgencyReviewStatsRespVO getAgencyReviewStats(Long agencyId) {
        // 查询评价统计
        List<AuntReviewDO> reviewList = auntReviewMapper.selectList(
                new LambdaQueryWrapperX<AuntReviewDO>()
                        .eq(AuntReviewDO::getAgencyId, agencyId)
                        .eq(AuntReviewDO::getDeleted, false)
                        .eq(AuntReviewDO::getStatus, 1));

        AgencyReviewStatsRespVO respVO = new AgencyReviewStatsRespVO();
        respVO.setTotalReviews(reviewList.size());

        if (!reviewList.isEmpty()) {
            BigDecimal totalRating = reviewList.stream()
                    .map(AuntReviewDO::getRating)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal averageRating = totalRating.divide(BigDecimal.valueOf(reviewList.size()), 1,
                    BigDecimal.ROUND_HALF_UP);
            respVO.setAverageRating(averageRating);
        } else {
            respVO.setAverageRating(BigDecimal.ZERO);
        }

        return respVO;
    }

    /**
     * 计算前端展示字段
     */
    private void calculateDisplayFields(AgencyDetailRespVO respVO, AgencyDO agency) {
        // 设置默认值
        respVO.setLogo("");
        respVO.setBanner("");
        respVO.setRating(new BigDecimal("0"));
        respVO.setReviewCount(0);
        respVO.setServiceCount(0);
        respVO.setEstablishTime(0);

        // 查询机构门头照作为banner
        AgencyQualificationDO doorPhoto = agencyQualificationMapper.selectOne(
                new LambdaQueryWrapperX<AgencyQualificationDO>()
                        .eq(AgencyQualificationDO::getAgencyId, agency.getId())
                        .eq(AgencyQualificationDO::getFileType, "door_photo")
                        .eq(AgencyQualificationDO::getStatus, 1)
                        .eq(AgencyQualificationDO::getDeleted, false)
                        .orderByAsc(AgencyQualificationDO::getSortOrder)
                        .last("LIMIT 1"));

        if (doorPhoto != null) {
            respVO.setBanner(doorPhoto.getFileUrl());
        }

        // 计算成立年限
        if (agency.getEstablishmentDate() != null) {
            try {
                LocalDate now = LocalDate.now();
                int years = now.getYear() - agency.getEstablishmentDate().getYear();
                if (now.getDayOfYear() < agency.getEstablishmentDate().getDayOfYear()) {
                    years--;
                }
                respVO.setEstablishTime(Math.max(0, years));
            } catch (Exception e) {
                log.warn("计算成立年限失败: {}", agency.getEstablishmentDate());
            }
        }

        // 查询评价统计
        AgencyReviewStatsRespVO reviewStats = getAgencyReviewStats(agency.getId());
        respVO.setReviewCount(reviewStats.getTotalReviews());
        respVO.setRating(reviewStats.getAverageRating());

        // 查询服务数量（统计该机构下的订单总数）
        Long serviceCount = domesticOrderMapper.selectCount(
                new LambdaQueryWrapperX<DomesticOrderDO>()
                        .eq(DomesticOrderDO::getAgencyId, agency.getId())
                        .eq(DomesticOrderDO::getDeleted, false));
        respVO.setServiceCount(serviceCount != null ? serviceCount.intValue() : 0);
    }

    /**
     * 转换为阿姨响应对象
     */
    private AgencyAuntRespVO convertToAuntRespVO(PractitionerDO practitioner) {
        AgencyAuntRespVO respVO = new AgencyAuntRespVO();
        respVO.setId(practitioner.getId());
        respVO.setAuntOneid(practitioner.getAuntOneid());
        respVO.setName(practitioner.getName());
        respVO.setPhone(practitioner.getPhone());
        respVO.setIdCard(practitioner.getIdCard());
        respVO.setHometown(practitioner.getHometown());
        respVO.setAge(practitioner.getAge());
        respVO.setGender(practitioner.getGender());
        respVO.setAvatar(practitioner.getAvatar());
        respVO.setServiceType(practitioner.getServiceType());
        respVO.setExperienceYears(practitioner.getExperienceYears());
        respVO.setPlatformStatus(practitioner.getPlatformStatus());
        respVO.setRating(practitioner.getRating());
        respVO.setAgencyId(practitioner.getAgencyId());
        respVO.setAgencyName(practitioner.getAgencyName());
        respVO.setStatus(practitioner.getStatus());
        respVO.setCurrentStatus(practitioner.getCurrentStatus());
        respVO.setCurrentOrderId(practitioner.getCurrentOrderId());
        respVO.setTotalOrders(practitioner.getTotalOrders());
        respVO.setTotalIncome(practitioner.getTotalIncome());
        respVO.setCustomerSatisfaction(practitioner.getCustomerSatisfaction());
        respVO.setCreateTime(convertToLocalDateTime(practitioner.getCreateTime()));
        respVO.setUpdateTime(convertToLocalDateTime(practitioner.getUpdateTime()));

        // 设置标签
        respVO.setTag("金牌" + practitioner.getServiceType());

        return respVO;
    }

    /**
     * 转换为套餐响应对象
     */
    private AgencyPackageRespVO convertToPackageRespVO(ServicePackageDO servicePackage) {
        AgencyPackageRespVO respVO = new AgencyPackageRespVO();
        respVO.setId(servicePackage.getId());
        respVO.setName(servicePackage.getName());
        respVO.setCategory(servicePackage.getCategory());
        respVO.setThumbnail(servicePackage.getThumbnail());
        respVO.setPrice(servicePackage.getPrice());
        respVO.setOriginalPrice(servicePackage.getOriginalPrice());
        respVO.setUnit(servicePackage.getUnit());
        respVO.setServiceDuration(servicePackage.getServiceDuration());
        respVO.setPackageType(servicePackage.getPackageType());
        respVO.setServiceDescription(servicePackage.getServiceDescription());
        respVO.setServiceDetails(servicePackage.getServiceDetails());
        respVO.setServiceProcess(servicePackage.getServiceProcess());
        respVO.setPurchaseNotice(servicePackage.getPurchaseNotice());
        respVO.setStatus(servicePackage.getStatus());
        respVO.setAdvanceBookingDays(servicePackage.getAdvanceBookingDays());
        respVO.setTimeSelectionMode(servicePackage.getTimeSelectionMode());
        respVO.setAppointmentMode(servicePackage.getAppointmentMode());
        respVO.setServiceStartTime(servicePackage.getServiceStartTime());
        respVO.setAddressSetting(servicePackage.getAddressSetting());
        respVO.setMaxBookingDays(servicePackage.getMaxBookingDays());
        respVO.setCancellationPolicy(servicePackage.getCancellationPolicy());
        respVO.setAuditStatus(servicePackage.getAuditStatus());
        respVO.setAgencyId(servicePackage.getAgencyId());
        respVO.setAgencyName(servicePackage.getAgencyName());
        respVO.setCategoryId(servicePackage.getCategoryId());
        respVO.setServiceTimeStart(servicePackage.getServiceTimeStart());
        respVO.setServiceTimeEnd(servicePackage.getServiceTimeEnd());
        respVO.setRestDayType(servicePackage.getRestDayType());
        respVO.setServiceTimespan(servicePackage.getServiceTimespan());
        respVO.setServiceTimes(servicePackage.getServiceTimes());
        respVO.setValidityPeriod(servicePackage.getValidityPeriod());
        respVO.setValidityPeriodUnit(servicePackage.getValidityPeriodUnit());
        respVO.setServiceIntervalType(servicePackage.getServiceIntervalType());
        respVO.setServiceIntervalValue(servicePackage.getServiceIntervalValue());
        respVO.setSingleDurationHours(servicePackage.getSingleDurationHours());
        respVO.setCreateTime(convertToLocalDateTime(servicePackage.getCreateTime()));
        respVO.setUpdateTime(convertToLocalDateTime(servicePackage.getUpdateTime()));

        // 设置前端展示字段
        respVO.setTitle(servicePackage.getName());
        respVO.setDuration(servicePackage.getServiceDuration() + " | " +
                (servicePackage.getSingleDurationHours() != null ? servicePackage.getSingleDurationHours() + "小时"
                        : ""));
        respVO.setImage(servicePackage.getThumbnail());
        respVO.setTag("长周期套餐");

        return respVO;
    }

    /**
     * 将 Date 转换为 LocalDateTime
     */
    private LocalDateTime convertToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
}