package cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Schema(description = "商机中心 - 商机日志新增/更新 Request VO")
public class BusinessLogSaveReqVO {
    private Long id;
    private Long tenantId;
    @NotNull(message = "商机ID不能为空")
    private Long businessId;
    @NotBlank(message = "操作类型不能为空")
    private String action;
    @NotBlank(message = "操作内容不能为空")
    private String content;
    private Date actionTime;
    private Long actionUserId;
    private String actionUserName;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Boolean deleted;
} 