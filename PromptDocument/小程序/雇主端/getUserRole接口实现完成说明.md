# getUserRole 接口实现完成说明

## 概述

已根据接口文档成功实现了 `getUserRole` 接口，该接口用于小程序启动时获取用户角色信息，支持微信登录验证和身份判断。

## 实现的功能

### 1. 微信登录验证
- 调用微信官方接口 [code2session](https://api.weixin.qq.com/sns/jscode2session?appid=APPID&secret=SECRET&js_code=CODE&grant_type=authorization_code) 获取用户openid
- 支持错误处理和异常捕获
- 完整的日志记录

### 2. 用户身份查询
- 根据openid查询用户最后登录身份记录
- 支持多种身份类型：EMPLOYER(雇主)、AUNT(阿姨)、AGENCY(机构)
- 用户状态验证和权限检查

### 3. 令牌生成
- 生成访问令牌和刷新令牌
- 支持后续接口认证

## 创建的文件列表

### 1. 配置相关文件

#### 1.1 微信小程序配置类
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/framework/config/WxMiniProgramConfig.java`
- **功能**: 微信小程序AppID、AppSecret等配置

#### 1.2 RestTemplate配置类
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/framework/config/RestTemplateConfig.java`
- **功能**: HTTP客户端配置，用于调用微信接口

#### 1.3 微信配置文件
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/resources/application-wx.yml`
- **功能**: 微信小程序配置信息

### 2. VO 文件

#### 2.1 微信登录响应VO
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/vo/WxCode2SessionRespVO.java`
- **功能**: 微信登录接口响应数据封装

#### 2.2 获取用户角色请求VO
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/vo/GetUserRoleReqVO.java`
- **功能**: 获取用户角色接口请求参数

#### 2.3 用户信息响应VO
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/vo/UserInfoRespVO.java`
- **功能**: 用户基本信息响应数据

#### 2.4 获取用户角色响应VO
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/vo/GetUserRoleRespVO.java`
- **功能**: 获取用户角色接口响应数据

### 3. 服务类

#### 3.1 微信小程序服务接口
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/service/employer/WxMiniProgramService.java`
- **功能**: 微信小程序服务接口定义

#### 3.2 微信小程序服务实现类
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/service/employer/impl/WxMiniProgramServiceImpl.java`
- **功能**: 微信登录接口调用实现

### 4. 控制器更新

#### 4.1 控制器接口新增
- **修改文件**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/MpUserLastLoginIdentityController.java`
- **新增接口**: `getUserRole` - 获取用户角色

#### 4.2 HTTP测试文件更新
- **修改文件**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/MpUserLastLoginIdentityController.http`
- **新增测试**: getUserRole接口测试用例

## 接口详情

### 接口信息
- **接口名称**: 获取用户角色
- **接口路径**: `/publicbiz/employer/mpuser/getUserRole`
- **请求方法**: `POST`
- **接口描述**: 根据微信登录code获取用户角色信息

### 请求参数
```json
{
  "code": "微信登录临时code"
}
```

### 响应格式
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "role": "employer",
    "userInfo": {
      "id": "user_123456",
      "openid": "wx_openid_123456",
      "nickname": "用户昵称",
      "avatar": "https://example.com/avatar.jpg",
      "mobile": "13800138000",
      "role": "employer",
      "status": "active",
      "createTime": "2024-01-01 12:00:00",
      "updateTime": "2024-01-01 12:00:00"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_123456"
  }
}
```

### 响应码说明
| 响应码 | 说明 | 处理建议 |
|--------|------|----------|
| 0 | 成功 | 正常处理 |
| 1002 | 微信登录失败 | 重新获取code |
| 1003 | 用户不存在 | 引导用户注册 |
| 1004 | 用户被禁用 | 提示用户联系客服 |
| 5000 | 服务器内部错误 | 稍后重试 |

## 实现流程

### 1. 微信登录验证
```java
// 调用微信接口获取openid
WxCode2SessionRespVO wxResult = wxMiniProgramService.code2Session(reqVO.getCode());
if (!wxResult.isSuccess()) {
    return CommonResult.error(1002, "微信登录失败: " + wxResult.getErrmsg());
}
```

### 2. 用户身份查询
```java
// 根据openid查询用户最后登录身份记录
MpUserLastLoginIdentityRespVO lastLoginIdentity = mpUserLastLoginIdentityService
        .getMpUserLastLoginIdentityByOpenid(wxResult.getOpenid());

if (lastLoginIdentity == null) {
    return CommonResult.error(1003, "用户不存在，请先注册");
}
```

### 3. 用户状态验证
```java
// 检查用户状态
if (lastLoginIdentity.getLoginStatus() != null && lastLoginIdentity.getLoginStatus() == 0) {
    return CommonResult.error(1004, "用户被禁用，请联系客服");
}
```

### 4. 构建响应数据
```java
// 构建用户信息
UserInfoRespVO userInfo = new UserInfoRespVO();
userInfo.setId(String.valueOf(lastLoginIdentity.getId()));
userInfo.setOpenid(lastLoginIdentity.getOpenid());
userInfo.setNickname(lastLoginIdentity.getIdentityName());
userInfo.setRole(lastLoginIdentity.getIdentityType());
// ... 其他字段设置

// 生成访问令牌
String token = "token_" + System.currentTimeMillis();
String refreshToken = "refresh_token_" + System.currentTimeMillis();

// 构建响应数据
GetUserRoleRespVO response = new GetUserRoleRespVO();
response.setRole(lastLoginIdentity.getIdentityType());
response.setUserInfo(userInfo);
response.setToken(token);
response.setRefreshToken(refreshToken);
```

## 配置信息

### 微信小程序配置
- **AppID**: wxf0cb5e5168f30b6a
- **AppSecret**: c4add048842fa0d7149d82f51f2d1852
- **接口URL**: https://api.weixin.qq.com/sns/jscode2session

### 配置文件位置
- **主配置**: `application-wx.yml`
- **配置类**: `WxMiniProgramConfig.java`

## 安全考虑

1. **接口安全**
   - 使用HTTPS协议
   - 验证微信登录code的有效性
   - 防止重复请求和恶意调用

2. **数据安全**
   - 敏感信息加密存储
   - 用户状态验证
   - 角色权限校验

3. **令牌管理**
   - 生成访问令牌和刷新令牌
   - 支持令牌刷新机制

## 测试用例

### 正常流程测试
```bash
curl -X POST https://api.example.com/publicbiz/employer/mpuser/getUserRole \
  -H "Content-Type: application/json" \
  -d '{"code": "wx_login_code_123456"}'
```

### 异常情况测试
- 参数错误：缺少code参数
- 微信登录失败：无效的code
- 用户不存在：openid对应的用户不存在
- 用户被禁用：用户状态异常

## 后续优化建议

1. **JWT令牌实现**
   - 使用JWT生成标准的访问令牌
   - 设置合理的过期时间
   - 实现令牌刷新机制

2. **用户信息完善**
   - 从用户表获取完整的用户信息
   - 支持头像、手机号等字段

3. **缓存优化**
   - 使用Redis缓存用户信息
   - 减少数据库查询次数

4. **监控告警**
   - 监控接口调用频率和响应时间
   - 设置异常响应码告警

5. **性能优化**
   - 接口响应时间控制在200ms以内
   - 数据库查询优化

## 使用示例

### 小程序端调用
```javascript
// 获取微信登录code
uni.login({
  success: (res) => {
    if (res.code) {
      // 调用后端接口获取用户角色
      uni.request({
        url: 'https://api.example.com/publicbiz/employer/mpuser/getUserRole',
        method: 'POST',
        data: {
          code: res.code
        },
        success: (response) => {
          if (response.data.code === 0) {
            const userRole = response.data.data.role;
            // 根据角色跳转到不同页面
            if (userRole === 'employer') {
              uni.switchTab({
                url: '/pages/index/index'
              });
            } else if (userRole === 'aunt') {
              uni.switchTab({
                url: '/pages/aunt-workbench/index'
              });
            }
          }
        }
      });
    }
  }
});
```

## 注意事项

1. 确保微信小程序AppID和AppSecret配置正确
2. 微信登录code只能使用一次，需要及时处理
3. 用户不存在时需要引导用户进行注册
4. 令牌生成目前是简化实现，生产环境建议使用JWT
5. 所有异常情况都有相应的错误码和提示信息 