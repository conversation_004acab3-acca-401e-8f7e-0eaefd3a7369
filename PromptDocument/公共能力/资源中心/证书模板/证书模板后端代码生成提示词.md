请结合`PromptDocument\公共能力\证书模板\证书模板接口文档.md`接口文档直接帮我生成后端代码，

## 1. 自动生成方案说明

### 步骤一：熟悉相关表结构
- 读取 `PromptDocument/公共能力/资源中心/证书模板/证书模板数据库表结构.md`


### 步骤二：确定后端接口与分层结构

- 每个API和页面功能，生成一个对应的后端Controller（RESTful风格）。
- 但是每个接口都必须带有动词前缀，例如page、get、list、create、update、delete。
- 每个业务实体，生成VO/DTO、Service接口与实现、Mapper接口（如涉及数据库）、Convert类、枚举、API接口等。
- 目录结构严格参考 bztmaster-module-system项目，每一层都需要，具体目录格式如下：

bztmaster-module-publicbiz/
├── bztmaster-module-publicbiz-api/
│   ├── pom.xml
│   ├── .flattened-pom.xml
│   └── src/
│       └── main/
│           └── java/
│               └── cn/
│                   └── bztmaster/
│                       └── cnt/
│                           └── module/
│                               └── publicbiz/
│                                   └── api/
│                                       └── certificateTemplate/
│                                           ├── CertificateTemplateApi.java
│                                           └── dto/
│                                               ├── CertificateTemplateRespDTO.java
│                                               ├── CertificateTemplateSaveReqDTO.java
│                                               ├── CertificateTemplatePageReqDTO.java
│                                               └── ...（其它DTO/VO/ReqVO等）
│                                   └── enums/
│                                       └── ...（如有枚举）
├── bztmaster-module-publicbiz-server/
│   ├── pom.xml
│   ├── .flattened-pom.xml
│   ├── Dockerfile
│   └── src/
│       ├── main/
│       │   ├── java/
│       │   │   └── cn/
│       │   │       └── bztmaster/
│       │   │           └── cnt/
│       │   │               └── module/
│       │   │                   └── publicbiz/
│       │   │                       ├── controller/
│       │   │                       │   └── admin/
│       │   │                       │       └── certificateTemplate/
│       │   │                       │           ├── CertificateTemplateController.java
│       │   │                       │           └── vo/
│       │   │                       │               ├── CertificateTemplateRespVO.java
│       │   │                       │               ├── CertificateTemplateSaveReqVO.java
│       │   │                       │               ├── CertificateTemplatePageReqVO.java
│       │   │                       │               └── ...（其它VO/ReqVO等）
│       │   │                       ├── dal/
│       │   │                       │   ├── mysql/
│       │   │                       │   │   └── certificateTemplate/
│       │   │                       │   │       └── CertificateTemplateMapper.java
│       │   │                       │   ├── dataobject/
│       │   │                       │   │   └── certificateTemplate/
│       │   │                       │   │       └── CertificateTemplateDO.java
│       │   │                       ├── service/
│       │   │                       │   └── certificateTemplate/
│       │   │                       │       ├── impl/
│       │   │                       │       │   └── CertificateTemplateServiceImpl.java
│       │   │                       │       └── CertificateTemplateService.java
│       │   │                       ├── convert/
│       │   │                       │   └── certificateTemplate/
│       │   │                       │       └── CertificateTemplateConvert.java
│       │   │                       └── ...（其它如 util、framework、job、mq 等可选）
│       │   └── resources/
│       │       └── mapper/
│       │           └── CertificateTemplateMapper.xml
│       │       └── application.yaml
│       │       └── ...（其它配置文件）
│       └── test/
│           └── java/
│               └── ...（测试代码结构）
├── pom.xml
└── .flattened-pom.xml



### 步骤三：自动生成代码

- Controller：每个API生成一个对应的Controller方法，路径、方法、参数与前端一致，带有Swagger注解。
- VO/DTO：根据前端参数和表单字段生成请求VO、响应VO，带有参数校验注解。
- Service/Impl：生成接口和实现，包含CRUD和具体的业务方法，方法注释详细。
- Mapper/DO：如涉及数据库，生成MyBatis Mapper接口和DO实体，字段与VO/DTO一致。
- Convert：生成VO与DO、DTO的转换类。
- Enums：如有枚举字段，自动生成枚举类。
- API接口：如有远程调用需求，生成Feign接口。
- 所有类和方法都带有详细注释，对应DTO、VO与DO对象也生成详细注释，便于初学者理解。
- 前端与后端对应的字段信息一定要确保能对应一致。  

### 步骤四：自动补全与TODO

- 对于前端未明确的字段类型、业务逻辑，自动推断或以// TODO注释标记，便于后续补充。
- 生成后的代码直接更新到指定的项目bztmaster-module-publicbiz模块下对应的目录中。