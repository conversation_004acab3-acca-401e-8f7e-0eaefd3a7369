package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数字资产课程 - 分页查询 Request VO
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/DigitalAsset.vue
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数字资产课程 - 分页查询 Request VO")
public class DigitalAssetCoursePageReqVO extends PageParam {

    @Schema(description = "课程分类", example = "家政技能")
    private String category;

    @Schema(description = "课程状态", example = "已上架")
    private String status;

    @Schema(description = "授课方式", example = "线上授课")
    private String teachType;

    @Schema(description = "业务板块", example = "家政服务")
    private String businessModule;

    @Schema(description = "收款商户ID", example = "1001")
    private Long merchant;

    @Schema(description = "课程名称关键词", example = "月嫂")
    private String keyword;
}
