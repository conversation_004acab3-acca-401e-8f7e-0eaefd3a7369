package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.teacher.TeacherCertService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;
import lombok.Data;

@RestController
@RequestMapping("/publicbiz/teacher/cert")
@Tag(name = "师资库-讲师资质管理")
public class TeacherCertController {
    @Resource
    private TeacherCertService teacherCertService;

    @PostMapping("/create")
    @Operation(summary = "新增讲师资质")
    public CommonResult<Long> create(@RequestBody TeacherCertSaveReqVO reqVO) {
        return CommonResult.success(teacherCertService.createTeacherCert(reqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除讲师资质", description = "根据资质ID删除讲师资质文件（逻辑删除）")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        // 调用服务层逻辑删除讲师资质
        teacherCertService.deleteTeacherCert(id);
        return CommonResult.success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "讲师资质文件列表")
    public CommonResult<List<TeacherCertRespVO>> list(@RequestParam("teacher_id") Long teacherId) {
        return CommonResult.success(teacherCertService.getTeacherCertList(teacherId));
    }

    // VO for id请求
    @Data
    public static class IdReqVO {
        private Long id;
    }
} 