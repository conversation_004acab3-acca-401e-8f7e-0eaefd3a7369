#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
套餐主表测试数据生成脚本
每个机构按照套餐分类，每个分类各新增3条测试数据
总计：5个机构 × 9个分类 × 3条数据 = 135条记录
"""

import datetime

# 机构数据
agencies = [
    {"id": 1001, "name": "测试机构A"},
    {"id": 1002, "name": "测试机构B"},
    {"id": 1003, "name": "测试机构C"},
    {"id": 1004, "name": "测试机构D"},
    {"id": 1005, "name": "测试机构E"}
]

# 套餐分类数据
categories = [
    {"id": 86, "name": "宠物照顾"},
    {"id": 87, "name": "保姆"},
    {"id": 88, "name": "月嫂服务"},
    {"id": 89, "name": "日常保洁"},
    {"id": 90, "name": "深度保洁"},
    {"id": 91, "name": "收纳整理"},
    {"id": 92, "name": "育儿服务"},
    {"id": 93, "name": "护工看护"},
    {"id": 94, "name": "做饭保姆"}
]

# 套餐模板数据
package_templates = {
    "宠物照顾": [
        {
            "name_suffix": "日常护理套餐",
            "price": 299.00,
            "original_price": 399.00,
            "unit": "次",
            "service_duration": "2小时",
            "package_type": "count-card",
            "task_split_rule": "按次服务",
            "service_description": "专业宠物护理服务，包括洗澡、梳毛、剪指甲等基础护理项目",
            "service_details": "<p>服务内容：</p><ul><li>宠物洗澡清洁</li><li>毛发梳理护理</li><li>指甲修剪</li><li>耳道清洁</li></ul>",
            "service_process": "<p>服务流程：</p><ol><li>预约确认</li><li>上门服务</li><li>专业护理</li><li>服务完成</li></ol>",
            "purchase_notice": "请提前24小时预约，服务前请确保宠物健康状况良好",
            "advance_booking_days": 1,
            "time_selection_mode": "fixed",
            "appointment_mode": "start-date",
            "service_start_time": "within-3-days",
            "address_setting": "fixed",
            "max_booking_days": 30,
            "cancellation_policy": "服务前24小时可免费取消",
            "service_time_start": "09:00:00",
            "service_time_end": "17:00:00",
            "rest_day_type": "sunday",
            "service_timespan": "2小时",
            "service_times": 1,
            "validity_period": 90,
            "validity_period_unit": "day",
            "service_interval_type": "flexible",
            "service_interval_value": 1,
            "single_duration_hours": 2
        },
        {
            "name_suffix": "寄养服务套餐",
            "price": 599.00,
            "original_price": 699.00,
            "unit": "天",
            "service_duration": "24小时",
            "package_type": "long-term",
            "task_split_rule": "按天服务",
            "service_description": "专业宠物寄养服务，提供安全舒适的寄养环境，24小时专人看护",
            "service_details": "<p>服务内容：</p><ul><li>24小时专人看护</li><li>定时喂食喂水</li><li>环境清洁消毒</li><li>健康监测</li></ul>",
            "service_process": "<p>服务流程：</p><ol><li>预约确认</li><li>宠物交接</li><li>专业寄养</li><li>安全送回</li></ol>",
            "purchase_notice": "请提供宠物疫苗证明，服务期间请保持电话畅通",
            "advance_booking_days": 3,
            "time_selection_mode": "flexible",
            "appointment_mode": "start-date",
            "service_start_time": "within-3-days",
            "address_setting": "fixed",
            "max_booking_days": 30,
            "cancellation_policy": "服务前48小时可免费取消",
            "service_time_start": "00:00:00",
            "service_time_end": "23:59:59",
            "rest_day_type": "none",
            "service_timespan": "24小时",
            "service_times": 1,
            "validity_period": 30,
            "validity_period_unit": "day",
            "service_interval_type": "daily",
            "service_interval_value": 1,
            "single_duration_hours": 24
        },
        {
            "name_suffix": "训练套餐",
            "price": 899.00,
            "original_price": 999.00,
            "unit": "次",
            "service_duration": "1小时",
            "package_type": "count-card",
            "task_split_rule": "按次训练",
            "service_description": "专业宠物训练服务，帮助宠物养成良好的行为习惯，提高宠物素质",
            "service_details": "<p>服务内容：</p><ul><li>基础指令训练</li><li>行为习惯矫正</li><li>社交能力培养</li><li>训练成果巩固</li></ul>",
            "service_process": "<p>服务流程：</p><ol><li>预约确认</li><li>训练评估</li><li>专业训练</li><li>成果展示</li></ol>",
            "purchase_notice": "训练期间需要主人配合，请保持耐心和一致性",
            "advance_booking_days": 1,
            "time_selection_mode": "fixed",
            "appointment_mode": "start-date",
            "service_start_time": "within-3-days",
            "address_setting": "fixed",
            "max_booking_days": 30,
            "cancellation_policy": "服务前24小时可免费取消",
            "service_time_start": "09:00:00",
            "service_time_end": "18:00:00",
            "rest_day_type": "sunday",
            "service_timespan": "1小时",
            "service_times": 1,
            "validity_period": 180,
            "validity_period_unit": "day",
            "service_interval_type": "weekly",
            "service_interval_value": 2,
            "single_duration_hours": 1
        }
    ],
    "保姆": [
        {
            "name_suffix": "日常保姆服务套餐",
            "price": 399.00,
            "original_price": 499.00,
            "unit": "天",
            "service_duration": "8小时",
            "package_type": "long-term",
            "task_split_rule": "按天服务",
            "service_description": "专业保姆服务，提供日常家务、烹饪、清洁等全方位家庭服务",
            "service_details": "<p>服务内容：</p><ul><li>日常家务整理</li><li>烹饪营养餐食</li><li>家居清洁维护</li><li>衣物清洗熨烫</li></ul>",
            "service_process": "<p>服务流程：</p><ol><li>预约确认</li><li>上门服务</li><li>专业服务</li><li>服务完成</li></ol>",
            "purchase_notice": "请提前预约，服务期间请保持电话畅通",
            "advance_booking_days": 1,
            "time_selection_mode": "fixed",
            "appointment_mode": "start-date",
            "service_start_time": "within-3-days",
            "address_setting": "fixed",
            "max_booking_days": 30,
            "cancellation_policy": "服务前24小时可免费取消",
            "service_time_start": "08:00:00",
            "service_time_end": "18:00:00",
            "rest_day_type": "sunday",
            "service_timespan": "8小时",
            "service_times": 1,
            "validity_period": 30,
            "validity_period_unit": "day",
            "service_interval_type": "daily",
            "service_interval_value": 1,
            "single_duration_hours": 8
        },
        {
            "name_suffix": "住家保姆套餐",
            "price": 599.00,
            "original_price": 699.00,
            "unit": "天",
            "service_duration": "24小时",
            "package_type": "long-term",
            "task_split_rule": "按天服务",
            "service_description": "住家保姆服务，24小时贴心照顾，提供全天候家庭服务",
            "service_details": "<p>服务内容：</p><ul><li>24小时住家服务</li><li>全天候家庭照顾</li><li>营养餐食制作</li><li>家居环境维护</li></ul>",
            "service_process": "<p>服务流程：</p><ol><li>预约确认</li><li>保姆入住</li><li>全天服务</li><li>定期评估</li></ol>",
            "purchase_notice": "住家服务需要提供住宿条件，请确保环境安全舒适",
            "advance_booking_days": 3,
            "time_selection_mode": "flexible",
            "appointment_mode": "start-date",
            "service_start_time": "within-3-days",
            "address_setting": "fixed",
            "max_booking_days": 30,
            "cancellation_policy": "服务前48小时可免费取消",
            "service_time_start": "00:00:00",
            "service_time_end": "23:59:59",
            "rest_day_type": "sunday",
            "service_timespan": "24小时",
            "service_times": 1,
            "validity_period": 90,
            "validity_period_unit": "day",
            "service_interval_type": "daily",
            "service_interval_value": 1,
            "single_duration_hours": 24
        },
        {
            "name_suffix": "临时保姆套餐",
            "price": 199.00,
            "original_price": 299.00,
            "unit": "次",
            "service_duration": "4小时",
            "package_type": "count-card",
            "task_split_rule": "按次服务",
            "service_description": "临时保姆服务，灵活安排时间，满足临时家庭服务需求",
            "service_details": "<p>服务内容：</p><ul><li>临时家务整理</li><li>简单餐食制作</li><li>基础清洁服务</li><li>临时看护</li></ul>",
            "service_process": "<p>服务流程：</p><ol><li>预约确认</li><li>上门服务</li><li>临时服务</li><li>服务完成</li></ol>",
            "purchase_notice": "临时服务请提前预约，服务时间灵活可调",
            "advance_booking_days": 1,
            "time_selection_mode": "flexible",
            "appointment_mode": "start-date",
            "service_start_time": "within-3-days",
            "address_setting": "changeable",
            "max_booking_days": 30,
            "cancellation_policy": "服务前12小时可免费取消",
            "service_time_start": "08:00:00",
            "service_time_end": "20:00:00",
            "rest_day_type": "none",
            "service_timespan": "4小时",
            "service_times": 1,
            "validity_period": 60,
            "validity_period_unit": "day",
            "service_interval_type": "flexible",
            "service_interval_value": 1,
            "single_duration_hours": 4
        }
    ]
}

# 生成SQL插入语句
def generate_sql():
    sql_lines = []
    sql_lines.append("-- 套餐主表完整测试数据插入脚本")
    sql_lines.append("-- 每个机构按照套餐分类，每个分类各新增3条测试数据")
    sql_lines.append("-- 总计：5个机构 × 9个分类 × 3条数据 = 135条记录")
    sql_lines.append("")
    
    sql_lines.append("INSERT INTO `publicbiz_service_package` (")
    sql_lines.append("    `tenant_id`, `creator`, `create_time`, `updater`, `update_time`, `deleted`,")
    sql_lines.append("    `name`, `category`, `thumbnail`, `price`, `original_price`, `unit`,")
    sql_lines.append("    `service_duration`, `package_type`, `task_split_rule`, `service_description`,")
    sql_lines.append("    `service_details`, `service_process`, `purchase_notice`, `status`,")
    sql_lines.append("    `advance_booking_days`, `time_selection_mode`, `appointment_mode`,")
    sql_lines.append("    `service_start_time`, `address_setting`, `max_booking_days`,")
    sql_lines.append("    `cancellation_policy`, `audit_status`, `agency_id`, `agency_name`,")
    sql_lines.append("    `reject_reason`, `category_id`, `service_time_start`, `service_time_end`,")
    sql_lines.append("    `rest_day_type`, `service_timespan`, `service_times`, `validity_period`,")
    sql_lines.append("    `validity_period_unit`, `service_interval_type`, `service_interval_value`,")
    sql_lines.append("    `single_duration_hours`")
    sql_lines.append(") VALUES")
    sql_lines.append("")
    
    values = []
    
    for agency in agencies:
        agency_id = agency["id"]
        agency_name = agency["name"]
        agency_suffix = agency_name[-1]  # 取机构名称最后一个字符作为后缀
        
        for category in categories:
            category_id = category["id"]
            category_name = category["name"]
            
            # 为每个分类生成3条数据
            for i in range(1, 4):
                if category_name in package_templates:
                    template = package_templates[category_name][i-1]
                    
                    # 生成套餐名称
                    package_name = template['name_suffix'] + agency_suffix + str(i)
                    
                    # 构建SQL值
                                         value = "({1}, 'admin', NOW(), 'admin', NOW(), 0, '" + package_name + "', '" + category_name + "', " + \
"'https://qiniu.bzmaster.cn/20250730/%E6%97%A5%E5%B8%B8%E4%BF%9D%E6%B4%81250730_1753843668746.jpg', " + \
str(template['price']) + ", " + str(template['original_price']) + ", '" + template['unit'] + "', '" + template['service_duration'] + "', '" + template['package_type'] + "', '" + template['task_split_rule'] + "', " + \
"'" + template['service_description'] + "', '" + template['service_details'] + "', '" + template['service_process'] + "', '" + template['purchase_notice'] + "', 'active', " + str(template['advance_booking_days']) + ", '" + template['time_selection_mode'] + "', '" + template['appointment_mode'] + "', " + \
"'" + template['service_start_time'] + "', '" + template['address_setting'] + "', " + str(template['max_booking_days']) + ", '" + template['cancellation_policy'] + "', 'approved', " + str(agency_id) + ", '" + agency_name + "', " + \
"NULL, " + str(category_id) + ", '" + template['service_time_start'] + "', '" + template['service_time_end'] + "', '" + template['rest_day_type'] + "', '" + template['service_timespan'] + "', " + str(template['service_times']) + ", " + str(template['validity_period']) + ", " + \
"'" + template['validity_period_unit'] + "', '" + template['service_interval_type'] + "', " + str(template['service_interval_value']) + ", " + str(template['single_duration_hours']) + ")"
                    
                    values.append(value)
    
    # 添加所有值到SQL
    sql_lines.append(",\n".join(values))
    sql_lines.append(";")
    
    return "\n".join(sql_lines)

# 生成SQL文件
if __name__ == "__main__":
    sql_content = generate_sql()
    
    # 写入文件
    with open("套餐主表测试数据_完整版.sql", "w", encoding="utf-8") as f:
        f.write(sql_content)
    
    print("套餐主表测试数据生成完成！")
    print("文件：套餐主表测试数据_完整版.sql")
    print("总计生成：5个机构 × 9个分类 × 3条数据 = 135条记录") 