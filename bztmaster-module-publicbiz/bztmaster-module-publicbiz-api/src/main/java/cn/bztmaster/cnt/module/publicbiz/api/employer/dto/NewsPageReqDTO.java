package cn.bztmaster.cnt.module.publicbiz.api.employer.dto;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 资讯分页查询请求 DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NewsPageReqDTO extends PageParam {

    /**
     * 资讯标题，支持模糊查询
     */
    private String newsTitle;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 状态：draft-草稿/published-已发布/offline-已下架
     */
    private String status;

    /**
     * 内容来源：manual-手动编写/material-关联素材库
     */
    private String contentSource;

    /**
     * 创建时间开始，格式：yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime startTime;

    /**
     * 创建时间结束，格式：yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime endTime;

    /**
     * 每页大小，最大100
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer size;
} 