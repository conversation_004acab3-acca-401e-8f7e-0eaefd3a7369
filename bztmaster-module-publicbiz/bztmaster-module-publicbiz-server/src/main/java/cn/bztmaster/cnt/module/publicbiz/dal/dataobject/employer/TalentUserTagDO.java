package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 用户标签关联表 DO
 *
 * <AUTHOR>
 */
@TableName("talent_user_tag")
@KeySequence("talent_user_tag_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TalentUserTagDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long userTagId;

    /**
     * 关联用户ID
     */
    private Long userId;

    /**
     * 标签ID，关联标签库
     */
    private Long tagId;

    /**
     * 标签类型ID
     */
    private Long tagTypeId;
}
