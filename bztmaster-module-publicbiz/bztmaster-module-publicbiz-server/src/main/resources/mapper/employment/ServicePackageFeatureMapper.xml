<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageFeatureMapper">

    <sql id="selectFields">
        id, package_id, feature_name, sort_order, create_time, update_time, creator, updater, deleted, tenant_id
    </sql>

    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageFeatureDO">
        <result property="id" column="id" />
        <result property="packageId" column="package_id" />
        <result property="featureName" column="feature_name" />
        <result property="sortOrder" column="sort_order" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creator" column="creator" />
        <result property="updater" column="updater" />
        <result property="deleted" column="deleted" />
        <result property="tenantId" column="tenant_id" />
    </resultMap>

    <!-- 根据套餐ID列表查询特色标签 -->
    <select id="selectFeatureListByPackageIds" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageFeatureDO">
        SELECT *
        FROM publicbiz_package_feature
        WHERE deleted = 0
            AND package_id IN
        <foreach collection="packageIds" item="packageId" open="(" separator="," close=")">
            #{packageId}
        </foreach>
        ORDER BY sort_order ASC
        LIMIT 5
    </select>

    <!-- 根据套餐ID查询特色标签 -->
    <select id="selectFeatureListByPackageId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageFeatureDO">
        SELECT *
        FROM publicbiz_package_feature
        WHERE deleted = 0
            AND package_id = #{packageId}
        ORDER BY sort_order ASC
        LIMIT 5
    </select>

</mapper> 