package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 机构资质文件更新请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构资质文件更新请求 VO")
@Data
public class AgencyQualificationUpdateVO {

    @Schema(description = "资质文件ID", example = "1")
    @NotNull(message = "资质文件ID不能为空")
    private Long id;

    @Schema(description = "文件名称", example = "营业执照.pdf")
    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    @Schema(description = "文件类型", example = "business_license")
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    @Schema(description = "文件URL", example = "https://example.com/file.pdf")
    @NotBlank(message = "文件URL不能为空")
    private String fileUrl;

    @Schema(description = "文件大小(KB)", example = "1024")
    private Long fileSize;

    @Schema(description = "文件扩展名", example = "pdf")
    private String fileExtension;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "备注", example = "备注信息")
    private String remark;
}
