请根据@线索中心.html原型中的线索管理页面表结构字段信息，帮我自动生成一份MySQL数据表的建表SQL，要求如下：
- 除了页面字段外，**每张表都要自动包含以下公共字段**：
    - id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增'
    - tenant_id BIGINT COMMENT '租户ID'
    - creator VARCHAR(64) COMMENT '创建人'
    - create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
    - updater VARCHAR(64) COMMENT '更新人'
    - update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
    - deleted BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除'
- 页面字段的字段名、类型、长度、是否必填、默认值、备注等要与页面字段完全对应。
- 字段类型要合理选择（如字符串用VARCHAR，数字用INT/BIGINT，日期用DATE/DATETIME，布尔用TINYINT(1)）。
- 如有唯一性、索引、外键、枚举、默认值等需求请自动补全。
- 每个字段都要加详细的中文注释（COMMENT）。
- 表本身要加注释（如：COMMENT='xxx表'）。
- 生成的SQL要符合MySQL 5.7/8.0标准，支持utf8mb4编码。
- 如果有枚举/下拉选项，请在注释中列出所有可选值。
- 生成的SQL要美观、易读、可直接执行。