spring:
  cloud:
    nacos:
      server-addr: 127.0.0.1:8848
      username:
      password:
      discovery:
        namespace: dev
        group: DEFAULT_GROUP
        metadata:
          version: 1.0.0
      config:
        namespace: dev
        group: DEFAULT_GROUP
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
      - de.codecentric.boot.admin.server.config.AdminServerAutoConfiguration
      - de.codecentric.boot.admin.server.cloud.config.AdminServerDiscoveryAutoConfiguration
      - de.codecentric.boot.admin.server.ui.config.AdminServerUiAutoConfiguration
      - de.codecentric.boot.admin.client.config.SpringBootAdminClientAutoConfiguration
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 1
        min-idle: 1
        max-active: 20
        max-wait: 600000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        max-evictable-idle-time-millis: 900000
        validation-query: SELECT 1 FROM DUAL
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: jdbc:mysql://**************:3307/hr-service-platform?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true&rewriteBatchedStatements=true
          username: admin
          password: XoVSeYdfQ3.3
        #          username: sa # SQL Server 连接的示例
        #          password: JSm:g(*%lU4ZAkz06cd52KqT3)i1?H7W # SQL Server 连接的示例
        #          username: SYSDBA # DM 连接的示例
        #          password: SYSDBA # DM 连接的示例
        slave: # 模拟从库，可根据自己需要修改
          lazy: true # 开启懒加载，保证启动速度
          url: jdbc:mysql://**************:3307/hr-service-platform?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true&rewriteBatchedStatements=true
          username: admin
          password: XoVSeYdfQ3.3
  redis:
    host: test-redis.bzmaster.cn
    port: 7018
    database: 1
    password: nPaaL6C8MUdm2ZHf9cfC
  rabbitmq:
    host: 127.0.0.1
    port: 5672
    username: guest
    password: guest
  kafka:
    bootstrap-servers: 127.0.0.1:9092

rocketmq:
  name-server: 127.0.0.1:9876

xxl:
  job:
    enabled: false
    admin:
      addresses: http://127.0.0.1:9090/xxl-job-admin

lock4j:
  acquire-timeout: 3000
  expire: 30000

management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: '*'

spring:
  boot:
    admin:
      client:
        instance:
          service-host-type: IP
      context-path: /admin

logging:
  level:
    cn.bztmaster.cnt.module.infra.dal.mysql: debug
    cn.bztmaster.cnt.module.infra.dal.mysql.logger.ApiErrorLogMapper: INFO
    cn.bztmaster.cnt.module.infra.dal.mysql.file.FileConfigMapper: INFO
    org.springframework.context.support.PostProcessorRegistrationDelegate: ERROR

bztmaster:
  env:
    tag: ${HOSTNAME}
  security:
    mock-enable: true
  access-log:
    enable: true
