package cn.bztmaster.cnt.module.publicbiz.controller.admin.category.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;

@Schema(description = "管理后台 - 服务分类列表查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceCategoryListReqVO extends PageParam {

    @Schema(description = "分类名称", example = "就业服务")
    private String name;

    @Schema(description = "开启状态", example = "0")
    private Integer status;

    @Schema(description = "父分类编号", example = "1")
    private Long parentId;

    @Schema(description = "父分类编号数组", example = "1,2,3")
    private Collection<Long> parentIds;

} 