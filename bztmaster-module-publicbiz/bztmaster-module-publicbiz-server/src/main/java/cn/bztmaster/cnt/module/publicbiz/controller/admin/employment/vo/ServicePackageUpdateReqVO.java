package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.mzt.logapi.starter.annotation.DiffLogField;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "就业服务-服务套餐更新 Request VO")
public class ServicePackageUpdateReqVO {
    @Schema(description = "套餐ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "套餐ID不能为空")
    private Long id;

    @Schema(description = "套餐名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "套餐名称不能为空")
    @DiffLogField(name = "套餐名称")
    private String name;

    @Schema(description = "服务分类", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "服务分类不能为空")
    @DiffLogField(name = "服务分类")
    private String category;

    @Schema(description = "套餐主图URL")
    @DiffLogField(name = "套餐主图")
    private String thumbnail;

    @Schema(description = "套餐价格", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "套餐价格不能为空")
    @DecimalMin(value = "0.01", message = "套餐价格必须大于0")
    @DiffLogField(name = "套餐价格")
    private BigDecimal price;

    @Schema(description = "原价")
    @DiffLogField(name = "原价")
    private BigDecimal originalPrice;

    @Schema(description = "价格单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "价格单位不能为空")
    @DiffLogField(name = "价格单位")
    private String unit;

    @Schema(description = "服务时长")
    @DiffLogField(name = "服务时长")
    private String serviceDuration;

    @Schema(description = "套餐类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "套餐类型不能为空")
    @DiffLogField(name = "套餐类型")
    private String packageType;

    @Schema(description = "任务拆分规则")
    @DiffLogField(name = "任务拆分规则")
    private String taskSplitRule;

    @Schema(description = "服务描述")
    @DiffLogField(name = "服务描述")
    private String serviceDescription;

    @Schema(description = "详细服务内容")
    @DiffLogField(name = "详细服务内容")
    private String serviceDetails;

    @Schema(description = "服务流程")
    @DiffLogField(name = "服务流程")
    private String serviceProcess;

    @Schema(description = "购买须知")
    @DiffLogField(name = "购买须知")
    private String purchaseNotice;

    @Schema(description = "状态")
    @DiffLogField(name = "状态")
    private String status;

    @Schema(description = "审核状态")
    @DiffLogField(name = "审核状态")
    private String auditStatus;

    @Schema(description = "拒绝原因")
    @DiffLogField(name = "拒绝原因")
    private String rejectReason;

    @Schema(description = "所属机构ID")
    @DiffLogField(name = "所属机构ID")
    private Long agencyId;

    @Schema(description = "所属机构名称")
    @DiffLogField(name = "所属机构名称")
    private String agencyName;

    @Schema(description = "预约时间范围")
    @DiffLogField(name = "预约时间范围")
    private Integer advanceBookingDays;

    @Schema(description = "时间选择模式")
    @DiffLogField(name = "时间选择模式")
    private String timeSelectionMode;

    @Schema(description = "预约模式")
    @DiffLogField(name = "预约模式")
    private String appointmentMode;

    @Schema(description = "服务开始时间")
    @DiffLogField(name = "服务开始时间")
    private String serviceStartTime;

    @Schema(description = "地址设置")
    @DiffLogField(name = "地址设置")
    private String addressSetting;

    @Schema(description = "最大预约天数")
    @DiffLogField(name = "最大预约天数")
    private Integer maxBookingDays;

    @Schema(description = "取消政策")
    @DiffLogField(name = "取消政策")
    private String cancellationPolicy;

    @Schema(description = "服务分类ID")
    @DiffLogField(name = "服务分类ID")
    private Long categoryId;

    @Schema(description = "服务开始时间")
    @DiffLogField(name = "服务开始时间")
    private String serviceTimeStart;

    @Schema(description = "服务结束时间")
    @DiffLogField(name = "服务结束时间")
    private String serviceTimeEnd;

    @Schema(description = "休息日类型")
    @DiffLogField(name = "休息日类型")
    private String restDayType;

    @Schema(description = "服务时间")
    @DiffLogField(name = "服务时间")
    private String serviceTimespan;

    @Schema(description = "服务次数")
    @DiffLogField(name = "服务次数")
    private Integer serviceTimes;

    @Schema(description = "有效期")
    @DiffLogField(name = "有效期")
    private Integer validityPeriod;

    @Schema(description = "有效期单位")
    @DiffLogField(name = "有效期单位")
    private String validityPeriodUnit;

    @Schema(description = "服务间隔类型")
    @DiffLogField(name = "服务间隔类型")
    private String serviceIntervalType;

    @Schema(description = "服务间隔数值")
    @DiffLogField(name = "服务间隔数值")
    private Integer serviceIntervalValue;

    @Schema(description = "单次服务时长（小时）")
    @DiffLogField(name = "单次服务时长")
    private Integer singleDurationHours;

    @Schema(description = "轮播图列表")
    // 不参与 _DIFF 处理，由自定义逻辑处理
    private List<ServicePackageCarouselSaveReqVO> carouselList;

    @Schema(description = "特色标签列表")
    // 不参与 _DIFF 处理，由自定义逻辑处理
    private List<ServicePackageFeatureSaveReqVO> featureList;
}