package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工单日志响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单日志响应 VO")
@Data
public class WorkOrderLogRespVO {

    @Schema(description = "主键", example = "1")
    private Long id;

    @Schema(description = "工单编号", example = "WO202312010001")
    private String workOrderNo;

    @Schema(description = "日志类型", example = "accept")
    private String logType;

    @Schema(description = "日志标题", example = "工单接单")
    private String logTitle;

    @Schema(description = "日志内容", example = "接单处理工单")
    private String logContent;

    @Schema(description = "操作人ID", example = "123")
    private Long operatorId;

    @Schema(description = "操作人姓名", example = "张三")
    private String operatorName;

    @Schema(description = "操作人角色", example = "客服")
    private String operatorRole;

    @Schema(description = "关联方类型", example = "雇主")
    private String relatedPartyType;

    @Schema(description = "关联方名称", example = "王先生")
    private String relatedPartyName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建时间字符串", example = "2024-04-12 10:30:00")
    private String createTimeStr;
} 