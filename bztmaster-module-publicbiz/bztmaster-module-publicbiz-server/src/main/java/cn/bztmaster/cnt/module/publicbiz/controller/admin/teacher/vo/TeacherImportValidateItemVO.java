package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "师资库 - 批量导入校验单项 VO")
public class TeacherImportValidateItemVO {
    
    @Schema(description = "行号")
    private Integer rowNum;
    
    @Schema(description = "讲师姓名")
    private String name;
    
    @Schema(description = "讲师类型")
    private String type;
    
    @Schema(description = "业务模块")
    private String biz;
    
    @Schema(description = "关联机构")
    private String org;
    
    @Schema(description = "擅长领域")
    private String field;
    
    @Schema(description = "联系电话")
    private String phone;
    
    @Schema(description = "邮箱地址")
    private String email;
    
    @Schema(description = "个人简介")
    private String description;
    
    @Schema(description = "合作状态")
    private String status;
    
    @Schema(description = "错误信息")
    private String errorMessage;
    
    @Schema(description = "校验状态：VALID-有效，ERROR-错误")
    private String validateStatus;
    
    @Schema(description = "原始数据")
    private TeacherSaveReqVO originalData;
} 