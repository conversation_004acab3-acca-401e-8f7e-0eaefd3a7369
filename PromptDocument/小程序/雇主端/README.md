# 雇主端小程序授权登录功能

## 功能概述

本项目实现了雇主端小程序的授权登录功能，包括：

1. **微信小程序一键授权登录** - 通过微信登录临时code进行快速登录
2. **手机验证码登录** - 通过手机号和验证码进行登录
3. **发送手机验证码** - 发送短信验证码到指定手机号
4. **校验手机验证码** - 验证短信验证码是否正确

## 技术架构

- **后端框架**: Spring Boot + MyBatis Plus
- **数据库**: MySQL
- **认证方式**: OAuth2.0 Token
- **短信服务**: 集成系统短信服务
- **微信服务**: 集成微信小程序登录服务

## 文件结构

```
bztmaster-module-publicbiz/
├── src/main/java/cn/bztmaster/cnt/module/publicbiz/
│   ├── controller/app/auth/
│   │   ├── EmployerAuthController.java          # 控制器
│   │   ├── EmployerAuthController.http          # HTTP测试文件
│   │   └── vo/                                  # 请求响应VO
│   │       ├── EmployerAuthWeixinMiniAppLoginReqVO.java
│   │       ├── EmployerAuthSmsLoginReqVO.java
│   │       ├── EmployerAuthSmsSendReqVO.java
│   │       ├── EmployerAuthSmsValidateReqVO.java
│   │       └── EmployerAuthLoginRespVO.java
│   ├── service/auth/
│   │   ├── EmployerAuthService.java             # 服务接口
│   │   └── impl/
│   │       └── EmployerAuthServiceImpl.java     # 服务实现
│   └── dal/
│       ├── dataobject/employer/
│       │   └── MpUserDO.java                    # 用户实体类
│       └── mysql/employer/
│           └── MpUserMapper.java                # 数据访问层
```

## 接口地址

| 功能 | 接口地址 | 请求方式 |
|------|----------|----------|
| 微信小程序一键授权登录 | `/publicbiz/employer/auth/weixin-mini-app-login` | POST |
| 手机验证码登录 | `/publicbiz/employer/auth/sms-login` | POST |
| 发送手机验证码 | `/publicbiz/employer/auth/send-sms-code` | POST |
| 校验手机验证码 | `/publicbiz/employer/auth/validate-sms-code` | POST |

## 数据库表

使用现有的 `mp_user` 表存储小程序用户信息，表结构如下：

```sql
CREATE TABLE `mp_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `oneid` char(36) NOT NULL COMMENT 'OneID GUID',
  `openid` varchar(100) NOT NULL COMMENT '用户标识',
  `union_id` varchar(100) DEFAULT NULL COMMENT '微信生态唯一标识',
  `subscribe_status` tinyint(4) NOT NULL COMMENT '关注状态',
  `subscribe_time` datetime NOT NULL COMMENT '关注时间',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号（仅小程序使用）',
  `nickname` varchar(64) DEFAULT NULL COMMENT '昵称',
  `head_image_url` varchar(1024) DEFAULT NULL COMMENT '头像地址',
  `unsubscribe_time` datetime DEFAULT NULL COMMENT '取消关注时间',
  `language` varchar(30) DEFAULT NULL COMMENT '语言',
  `country` varchar(30) DEFAULT NULL COMMENT '国家',
  `province` varchar(30) DEFAULT NULL COMMENT '省份',
  `city` varchar(30) DEFAULT NULL COMMENT '城市',
  `remark` varchar(128) DEFAULT NULL COMMENT '备注',
  `tag_ids` varchar(255) DEFAULT NULL COMMENT '标签编号数组',
  `account_id` bigint(20) NOT NULL COMMENT '微信公众号ID',
  `app_id` varchar(128) NOT NULL COMMENT '微信公众号 appid',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信用户表（小程序+公众号）';
```

## 业务流程

### 微信小程序一键授权登录流程

1. 小程序端调用 `wx.login()` 获取临时code
2. 将code发送到后端接口 `/publicbiz/employer/auth/weixin-mini-app-login`
3. 后端调用微信接口获取openid和unionid
4. 根据openid查找用户，不存在则创建新用户
5. 生成OAuth2访问令牌
6. 返回用户信息和访问令牌

### 手机验证码登录流程

1. 用户输入手机号，调用 `/publicbiz/employer/auth/send-sms-code` 发送验证码
2. 用户输入验证码，调用 `/publicbiz/employer/auth/validate-sms-code` 校验验证码
3. 验证码正确后，调用 `/publicbiz/employer/auth/sms-login` 进行登录
4. 根据手机号查找用户，不存在则创建新用户
5. 生成OAuth2访问令牌
6. 返回用户信息和访问令牌

## 配置说明

### 微信小程序配置

需要在配置文件中设置微信小程序的appid和secret：

```yaml
wx:
  miniprogram:
    app-id: your_app_id
    secret: your_app_secret
    code2-session-url: https://api.weixin.qq.com/sns/jscode2session
```

或者使用现有的配置文件 `application-wx.yml`：

```yaml
# 微信小程序配置
wx:
  miniprogram:
    app-id: wxf0cb5e5168f30b6a
    secret: c4add048842fa0d7149d82f51f2d1852
    code2-session-url: https://api.weixin.qq.com/sns/jscode2session
```

### 短信服务配置

短信服务使用系统现有的短信服务，场景配置为 `MEMBER_LOGIN`。

### OAuth2客户端配置

需要在OAuth2客户端表中添加雇主端客户端配置，使用微信小程序的appid作为客户端ID。

**方法1：使用SQL脚本（推荐）**

执行项目中的SQL脚本：
```bash
mysql -h your_host -u your_user -p your_database < bztmaster-module-publicbiz/bztmaster-module-publicbiz-server/src/main/resources/sql/oauth2-client-init.sql
```

**方法2：手动执行SQL**

```sql
INSERT INTO system_oauth2_client (
    client_id, 
    secret, 
    name, 
    logo, 
    description, 
    status, 
    access_token_validity_seconds, 
    refresh_token_validity_seconds, 
    redirect_uris, 
    authorized_grant_types, 
    scopes, 
    auto_approve_scopes, 
    authorities, 
    resource_ids, 
    additional_information, 
    creator, 
    create_time, 
    updater, 
    update_time, 
    deleted
) VALUES (
    'wxf0cb5e5168f30b6a', -- 微信小程序appid作为客户端ID
    'c4add048842fa0d7149d82f51f2d1852', -- 微信小程序secret作为客户端密钥
    '雇主端小程序', 
    'https://example.com/logo.png', 
    '雇主端小程序OAuth2客户端',
    0, -- 状态：0-启用，1-禁用
    7200, -- 访问令牌有效期2小时
    604800, -- 刷新令牌有效期7天
    'https://example.com/callback', 
    'password,refresh_token', 
    'read', 
    'read', 
    'read', -- 权限
    '', -- 资源
    '{"app_type": "miniprogram", "user_type": "employer"}', -- 附加信息
    'system', 
    NOW(), 
    'system', 
    NOW(), 
    0
) ON DUPLICATE KEY UPDATE
    secret = VALUES(secret),
    name = VALUES(name),
    description = VALUES(description),
    status = VALUES(status),
    access_token_validity_seconds = VALUES(access_token_validity_seconds),
    refresh_token_validity_seconds = VALUES(refresh_token_validity_seconds),
    authorized_grant_types = VALUES(authorized_grant_types),
    scopes = VALUES(scopes),
    auto_approve_scopes = VALUES(auto_approve_scopes),
    authorities = VALUES(authorities),
    additional_information = VALUES(additional_information),
    updater = VALUES(updater),
    update_time = VALUES(update_time);
```

**注意**：
- `client_id` 和 `client_secret` 需要与微信小程序的 `app-id` 和 `secret` 保持一致
- 如果客户端已存在，会更新相关信息

## 故障排除

### 常见问题

1. **OAuth2客户端不存在错误**
   ```
   错误信息：创建访问令牌失败: 系统异常
   解决方案：已修复，现在使用默认的OAuth2客户端ID (default)
   ```

2. **服务发现错误**
   ```
   错误信息：Load balancer does not contain an instance for the service xxx-server
   解决方案：启动相关的微服务（infra-server, system-server）
   ```

3. **微信API调用失败**
   ```
   错误信息：微信登录失败: xxx
   解决方案：检查微信小程序配置是否正确
   ```

### 最新修改

1. **OAuth2客户端配置**：现在使用 `OAuth2ClientConstants.CLIENT_ID_DEFAULT` (值为 "default") 而不是微信小程序的appid
2. **登录日志记录**：添加了完整的登录日志记录功能，包括：
   - 登录成功日志
   - 用户IP、用户代理等信息
   - 登录类型（社交登录、短信登录）
3. **用户类型**：使用 `UserTypeEnum.MEMBER` 作为用户类型

### 调试步骤

1. 检查日志中的详细错误信息
2. 确认所有Feign客户端都已正确注入
3. 验证微信小程序配置
4. 检查数据库连接和表结构

## 测试

### 使用HTTP测试文件

项目提供了 `EmployerAuthController.http` 测试文件，可以在IDE中直接运行测试。

### 测试步骤

1. 启动项目
2. 在IDE中打开 `EmployerAuthController.http` 文件
3. 配置环境变量 `baseUrl`
4. 依次执行测试用例

### 测试用例

1. **微信小程序一键授权登录测试**
   - 使用有效的微信登录code
   - 验证返回的用户信息和访问令牌

2. **手机验证码登录测试**
   - 发送验证码到测试手机号
   - 使用验证码进行登录
   - 验证返回的用户信息和访问令牌

## 注意事项

1. **安全性**
   - 所有接口都设置了 `@PermitAll` 注解，允许未认证访问
   - 在生产环境中需要添加适当的访问控制

2. **租户支持**
   - 代码中设置了租户上下文，支持多租户环境
   - 默认租户ID为1

3. **错误处理**
   - 所有接口都有完整的异常处理
   - 返回统一的错误码和错误信息

4. **日志记录**
   - 关键操作都有日志记录
   - 便于问题排查和监控

## 扩展功能

1. **用户信息完善**
   - 可以添加用户信息完善接口
   - 支持用户修改昵称、头像等信息

2. **登录日志**
   - 可以添加登录日志记录
   - 记录用户登录时间、IP等信息

3. **用户状态管理**
   - 可以添加用户状态管理功能
   - 支持禁用/启用用户

4. **第三方登录**
   - 可以扩展支持其他第三方登录方式
   - 如QQ、支付宝等

## 相关文档

- [雇主端小程序授权登录接口文档](./雇主端小程序授权登录接口文档.md)
- [功能开发提示词](./功能开发提示词.md) 