package cn.bztmaster.cnt.module.publicbiz.api.employer.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 资讯保存请求 DTO
 *
 * <AUTHOR>
 */
@Data
public class NewsSaveReqDTO {

    /**
     * 资讯ID（更新时必填）
     */
    private Long id;

    /**
     * 资讯标题
     */
    @NotBlank(message = "资讯标题不能为空")
    @Size(max = 200, message = "资讯标题长度不能超过200字符")
    private String newsTitle;

    /**
     * 资讯摘要
     */
    @NotBlank(message = "资讯摘要不能为空")
    private String newsSummary;

    /**
     * 资讯内容
     */
    @NotBlank(message = "资讯内容不能为空")
    private String newsContent;

    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long categoryId;

    /**
     * 封面图片URL
     */
    @NotBlank(message = "封面图片URL不能为空")
    private String coverImageUrl;

    /**
     * 关联素材文章ID
     */
    private Long materialId;

    /**
     * 作者
     */
    @NotBlank(message = "作者不能为空")
    @Size(max = 50, message = "作者长度不能超过50字符")
    private String author;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 状态：draft-草稿/published-已发布
     */
    @NotBlank(message = "状态不能为空")
    private String status;

    /**
     * 排序值，数字越小越靠前
     */
    private Integer sort;
} 