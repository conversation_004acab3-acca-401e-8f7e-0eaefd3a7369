package cn.bztmaster.cnt.module.publicbiz.api.employer.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 资讯响应 DTO
 *
 * <AUTHOR>
 */
@Data
public class NewsRespDTO {

    /**
     * 资讯ID
     */
    private Long id;

    /**
     * 资讯标题
     */
    private String newsTitle;

    /**
     * 资讯摘要
     */
    private String newsSummary;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 封面图片URL
     */
    private String coverImageUrl;

    /**
     * 关联素材文章ID
     */
    private Long materialId;

    /**
     * 作者
     */
    private String author;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 状态：draft-草稿/published-已发布/offline-已下架
     */
    private String status;

    /**
     * 浏览次数
     */
    private Integer viewCount;

    /**
     * 点赞次数
     */
    private Integer likeCount;

    /**
     * 分享次数
     */
    private Integer shareCount;

    /**
     * 评论数
     */
    private Long commentCount;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 