package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.mzt.logapi.starter.annotation.DiffLogField;

import javax.validation.constraints.NotNull;

/**
 * 机构更新请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构更新请求 VO")
@Data
public class AgencyUpdateReqVO {

    @Schema(description = "机构ID", example = "1")
    @NotNull(message = "机构ID不能为空")
    private Long id;

    @Schema(description = "审核状态", example = "approved")
    @NotNull(message = "审核状态不能为空")
    @DiffLogField(name = "审核状态")
    private String reviewStatus;

    @Schema(description = "审核备注", example = "审核通过，资料齐全")
    @DiffLogField(name = "审核备注")
    private String reviewRemark;
} 