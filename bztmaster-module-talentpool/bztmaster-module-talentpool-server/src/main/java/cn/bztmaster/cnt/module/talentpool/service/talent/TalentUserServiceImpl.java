package cn.bztmaster.cnt.module.talentpool.service.talent;

import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserPageReqDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserRespDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserUpdateReqDTO;
import cn.bztmaster.cnt.module.talentpool.convert.talent.TalentUserConvert;
import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserDO;
import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserTagDO;
import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentTagLibraryDO;
import cn.bztmaster.cnt.module.talentpool.dal.mysql.talent.TalentUserMapper;
import cn.bztmaster.cnt.module.talentpool.dal.mysql.talent.TalentUserTagMapper;
import cn.bztmaster.cnt.module.talentpool.dal.mysql.talent.TalentTagLibraryMapper;
import cn.bztmaster.cnt.module.talentpool.enums.talent.TalentUserStatusEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Objects;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserCreateReqDTO;
import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.*;
import cn.bztmaster.cnt.module.talentpool.dal.mysql.talent.*;
import java.math.BigDecimal;
import cn.bztmaster.cnt.module.talentpool.dal.mysql.talent.TalentTagTypeMapper;
import cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentTagTypeDO;
import java.security.MessageDigest;
import java.util.UUID;
import java.util.ArrayList;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserTagDetailRespDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentCertificateRespDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 人才用户 Service 实现类
 *
 * 实现人才库用户的核心业务逻辑，包含分页、详情、编辑、停用、合并等操作。
 * 合并逻辑严格遵循多级识别（身份证号、手机号、UnionID、人工辅助）规则。
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TalentUserServiceImpl implements TalentUserService {

    @Resource
    private TalentUserMapper talentUserMapper;

    @Resource
    private TalentUserTagMapper talentUserTagMapper;

    @Resource
    private TalentTagLibraryMapper talentTagLibraryMapper;

    @Resource
    private TalentEducationMapper talentEducationMapper;

    @Resource
    private TalentCampusPracticeMapper talentCampusPracticeMapper;

    @Resource
    private TalentInternshipMapper talentInternshipMapper;

    @Resource
    private TalentProjectMapper talentProjectMapper;

    @Resource
    private TalentTrainingMapper talentTrainingMapper;

    @Resource
    private TalentSkillMapper talentSkillMapper;

    @Resource
    private TalentCertificateMapper talentCertificateMapper;

    @Resource
    private TalentJobApplicationMapper talentJobApplicationMapper;

    @Resource
    private TalentEmploymentMapper talentEmploymentMapper;

    @Resource
    private TalentTagTypeMapper talentTagTypeMapper;

    @Resource
    private TalentUserCommentMapper talentUserCommentMapper;

    @Override
    public PageResult<TalentUserRespDTO> getTalentUserPage(TalentUserPageReqDTO reqDTO) {
        PageResult<TalentUserDO> pageResult;
        // 新增：支持orgId和isSelfSupport过滤
        if (reqDTO.getOrgId() != null || reqDTO.getIsSelfSupport() != null) {
            com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TalentUserDO> wrapper = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
            if (reqDTO.getOrgId() != null) {
                wrapper.eq(TalentUserDO::getOrgId, reqDTO.getOrgId());
            }
            if (reqDTO.getIsSelfSupport() != null) {
                wrapper.eq(TalentUserDO::getIsSelfSupport, reqDTO.getIsSelfSupport());
            }
            wrapper.and(reqDTO.getKeyword() != null && !reqDTO.getKeyword().isEmpty(), w -> {
                w.like(TalentUserDO::getName, reqDTO.getKeyword())
                        .or()
                        .like(TalentUserDO::getPhone, reqDTO.getKeyword())
                        .or()
                        .like(TalentUserDO::getIdentityId, reqDTO.getKeyword());
            });
            wrapper.apply(reqDTO.getTag() != null && !reqDTO.getTag().isEmpty(),
                    "user_id IN (SELECT user_id FROM talent_user_tag ut JOIN talent_tag_library tl ON ut.tag_id = tl.tag_id WHERE ut.deleted = 0 AND tl.tag_name LIKE CONCAT('%', {0}, '%'))",
                    reqDTO.getTag());
            wrapper.orderByDesc(TalentUserDO::getUserId);
            pageResult = talentUserMapper.selectPage(reqDTO, wrapper);
            if (reqDTO.getSource() != null && !reqDTO.getSource().isEmpty()) {
                pageResult.getList().removeIf(
                        u -> u.getRegisterSource() == null || !u.getRegisterSource().equals(reqDTO.getSource()));
            }
            if (reqDTO.getStatus() != null && !reqDTO.getStatus().isEmpty()) {
                pageResult.getList().removeIf(u -> u.getStatus() == null || !u.getStatus().equals(reqDTO.getStatus()));
            }
        } else {
            pageResult = talentUserMapper.selectPage(reqDTO);
        }
        List<TalentUserRespDTO> respList = TalentUserConvert.INSTANCE.convertList(pageResult.getList());
        // 查询所有用户ID
        List<Long> userIds = pageResult.getList().stream().map(TalentUserDO::getUserId).collect(Collectors.toList());
        // 查询所有标签
        List<TalentUserTagDO> userTagList = userIds.isEmpty() ? java.util.Collections.emptyList()
                : talentUserTagMapper.selectListByUserIds(userIds);
        List<Long> tagIds = userTagList.stream().map(TalentUserTagDO::getTagId).distinct().collect(Collectors.toList());
        List<TalentTagLibraryDO> tagList = tagIds.isEmpty() ? java.util.Collections.emptyList()
                : talentTagLibraryMapper.selectBatchIds(tagIds);
        // 构建标签ID->标签名映射
        Map<Long, String> tagIdNameMap = tagList.stream()
                .collect(Collectors.toMap(TalentTagLibraryDO::getTagId, TalentTagLibraryDO::getTagName));
        // 构建用户ID->标签名列表映射
        Map<Long, List<String>> userIdTagsMap = new java.util.HashMap<>();
        for (TalentUserTagDO userTag : userTagList) {
            Long userId = userTag.getUserId();
            String tagName = tagIdNameMap.get(userTag.getTagId());
            if (tagName != null) {
                userIdTagsMap.computeIfAbsent(userId, k -> new java.util.ArrayList<>()).add(tagName);
            }
        }
        // 设置到 respList
        for (TalentUserRespDTO dto : respList) {
            dto.setTags(userIdTagsMap.getOrDefault(dto.getId(), java.util.Collections.emptyList()));
        }
        return new PageResult<>(respList, pageResult.getTotal());
    }

    @Override
    public TalentUserRespDTO getTalentUser(Long id) {
        TalentUserDO userDO = talentUserMapper.selectById(id);
        return TalentUserConvert.INSTANCE.convert(userDO);
    }

    @Override
    public TalentUserRespDTO getTalentUserByOneid(String oneid) {
        TalentUserDO userDO = talentUserMapper.selectByOneid(oneid);
        if (userDO == null) {
            return null;
        }
        TalentUserRespDTO respDTO = TalentUserConvert.INSTANCE.convert(userDO);

        // 获取用户标签
        List<TalentUserTagDO> userTags = talentUserTagMapper.selectListByUserId(userDO.getUserId());
        if (!userTags.isEmpty()) {
            List<Long> tagIds = userTags.stream()
                    .map(TalentUserTagDO::getTagId)
                    .collect(Collectors.toList());
            List<TalentTagLibraryDO> tagLibraries = talentTagLibraryMapper.selectBatchIds(tagIds);

            List<String> tagNames = tagLibraries.stream()
                    .map(TalentTagLibraryDO::getTagName)
                    .collect(Collectors.toList());
            respDTO.setTags(tagNames);
        }

        return respDTO;
    }

    @Override
    public List<TalentUserTagDetailRespDTO> getTalentUserTagsByOneid(String oneid) {
        // 1. 根据oneid查询用户信息
        TalentUserDO userDO = talentUserMapper.selectByOneid(oneid);
        if (userDO == null) {
            return new ArrayList<>();
        }

        // 2. 使用多表关联查询获取用户的详细标签信息
        List<Map<String, Object>> tagDetails = talentUserTagMapper.selectUserTagsWithDetails(userDO.getUserId());

        // 3. 转换为DTO对象
        return tagDetails.stream()
                .map(this::convertToTagDetailDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将Map转换为TalentUserTagDetailRespDTO
     */
    private TalentUserTagDetailRespDTO convertToTagDetailDTO(Map<String, Object> tagDetail) {
        TalentUserTagDetailRespDTO dto = new TalentUserTagDetailRespDTO();
        dto.setTagId((Long) tagDetail.get("tagId"));
        dto.setTagTypeId((Long) tagDetail.get("tagTypeId"));
        dto.setTagName((String) tagDetail.get("tagName"));
        dto.setTagCode((String) tagDetail.get("tagCode"));
        dto.setTagTypeName((String) tagDetail.get("tagTypeName"));
        dto.setTagTypeCode((String) tagDetail.get("tagTypeCode"));
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTalentUser(TalentUserUpdateReqDTO reqDTO) {
        // 1. 主表更新
        TalentUserDO userDO = talentUserMapper.selectById(reqDTO.getId());
        if (userDO == null)
            throw new RuntimeException("用户不存在");
        userDO.setName(reqDTO.getName());
        userDO.setPhone(reqDTO.getPhone());
        userDO.setIdentityId(reqDTO.getIdentityId());
        userDO.setEmail(reqDTO.getEmail());
        userDO.setGender(reqDTO.getGender());
        userDO.setBirthDate(reqDTO.getBirthDate());
        userDO.setAvatarUrl(reqDTO.getAvatarUrl());
        userDO.setRegisterSource(reqDTO.getSource());
        // 新增字段
        userDO.setOrgId(reqDTO.getOrgId());
        userDO.setOrgName(reqDTO.getOrgName());
        userDO.setTalentSource(reqDTO.getTalentSource());
        userDO.setIsSelfSupport(reqDTO.getIsSelfSupport());
        userDO.setStatus(reqDTO.getStatus());
        userDO.setCompleteness(reqDTO.getCompleteness() == null ? 0 : reqDTO.getCompleteness().byteValue());
        talentUserMapper.updateById(userDO);
        Long userId = userDO.getUserId();
        // 2. 标签处理（只根据 tags 字段，先删后插）
        talentUserTagMapper.deleteByUserId(userId);
        if (reqDTO.getTags() != null && !reqDTO.getTags().isEmpty()) {
            for (String tagName : reqDTO.getTags()) {
                TalentTagLibraryDO tag = talentTagLibraryMapper.selectOne(
                        new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TalentTagLibraryDO>()
                                .eq(TalentTagLibraryDO::getTagName, tagName)
                                .eq(TalentTagLibraryDO::getDeleted, false));
                if (tag == null) {
                    tag = new TalentTagLibraryDO();
                    tag.setTagName(tagName);
                    tag.setDeleted(false);
                    tag.setTenantId(1L);
                    String[] arr = tagName.split(":", 2);
                    Long tagTypeId = null;
                    if (arr.length == 2) {
                        String typeName = arr[0];
                        TalentTagTypeDO type = talentTagTypeMapper.selectOne(
                                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TalentTagTypeDO>()
                                        .eq(TalentTagTypeDO::getTypeName, typeName)
                                        .eq(TalentTagTypeDO::getDeleted, false));
                        if (type != null)
                            tagTypeId = type.getTagTypeId();
                    }
                    if (tagTypeId == null) {
                        TalentTagTypeDO otherType = talentTagTypeMapper.selectOne(
                                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TalentTagTypeDO>()
                                        .eq(TalentTagTypeDO::getTypeName, "其它")
                                        .eq(TalentTagTypeDO::getDeleted, false));
                        if (otherType != null)
                            tagTypeId = otherType.getTagTypeId();
                        else
                            throw new RuntimeException("未找到标签类型'其它'");
                    }
                    tag.setTagTypeId(tagTypeId);
                    tag.setDescription(arr.length == 2 ? arr[0] : "其它");
                    tag.setTagCode(UUID.randomUUID().toString().replaceAll("-", ""));
                    talentTagLibraryMapper.insert(tag);
                }
                TalentUserTagDO userTag = new TalentUserTagDO();
                userTag.setUserId(userId);
                userTag.setTagId(tag.getTagId());
                userTag.setTagTypeId(tag.getTagTypeId());
                userTag.setDeleted(false);
                userTag.setTenantId(1L);
                talentUserTagMapper.insert(userTag);
            }
        }
        // 3. 教育背景
        talentEducationMapper.deleteByUserId(userId);
        if (reqDTO.getEducationList() != null) {
            for (TalentUserCreateReqDTO.EducationDTO edu : reqDTO.getEducationList()) {
                TalentEducationDO d = new TalentEducationDO();
                d.setUserId(userId);
                d.setInstitution(edu.getInstitution());
                d.setCollegeAddress(edu.getCollegeAddress());
                d.setDegreeType(edu.getDegreeType());
                d.setMajor(edu.getMajor());
                d.setStartDate(edu.getStartDate());
                d.setEndDate(edu.getEndDate());
                d.setAcademicRanking(edu.getAcademicRanking());
                d.setIsInternship(edu.getIsInternship());
                d.setInternshipType(edu.getInternshipType());
                d.setInternshipDuration(edu.getInternshipDuration());
                talentEducationMapper.insert(d);
            }
        }
        // 4. 校园实践
        talentCampusPracticeMapper.deleteByUserId(userId);
        if (reqDTO.getCampusPracticeList() != null) {
            for (TalentUserCreateReqDTO.CampusPracticeDTO cp : reqDTO.getCampusPracticeList()) {
                TalentCampusPracticeDO d = new TalentCampusPracticeDO();
                d.setUserId(userId);
                d.setPracticeName(cp.getPracticeName());
                d.setOrganizer(cp.getOrganizer());
                d.setStartDate(cp.getStartDate());
                d.setEndDate(cp.getEndDate());
                d.setPracticeReport(cp.getPracticeReport());
                talentCampusPracticeMapper.insert(d);
            }
        }
        // 5. 实习经历
        talentInternshipMapper.deleteByUserId(userId);
        if (reqDTO.getInternshipList() != null) {
            for (TalentUserCreateReqDTO.InternshipDTO it : reqDTO.getInternshipList()) {
                TalentInternshipDO d = new TalentInternshipDO();
                d.setUserId(userId);
                d.setCompany(it.getCompany());
                d.setPosition(it.getPosition());
                d.setStartDate(it.getStartDate());
                d.setEndDate(it.getEndDate());
                d.setResponsibilities(it.getResponsibilities());
                talentInternshipMapper.insert(d);
            }
        }
        // 6. 项目经历
        talentProjectMapper.deleteByUserId(userId);
        if (reqDTO.getProjectList() != null) {
            for (TalentUserCreateReqDTO.ProjectDTO pj : reqDTO.getProjectList()) {
                TalentProjectDO d = new TalentProjectDO();
                d.setUserId(userId);
                d.setName(pj.getName());
                d.setDescription(pj.getDescription());
                d.setStartDate(pj.getStartDate());
                d.setEndDate(pj.getEndDate());
                talentProjectMapper.insert(d);
            }
        }
        // 7. 培训经历
        talentTrainingMapper.deleteByUserId(userId);
        if (reqDTO.getTrainingList() != null) {
            for (TalentUserCreateReqDTO.TrainingDTO tr : reqDTO.getTrainingList()) {
                TalentTrainingDO d = new TalentTrainingDO();
                d.setUserId(userId);
                d.setProvider(tr.getProvider());
                d.setCourse(tr.getCourse());
                d.setCompleteDate(tr.getCompleteDate());
                talentTrainingMapper.insert(d);
            }
        }
        // 8. 技能
        talentSkillMapper.deleteByUserId(userId);
        if (reqDTO.getSkillList() != null) {
            for (TalentUserCreateReqDTO.SkillDTO sk : reqDTO.getSkillList()) {
                TalentSkillDO d = new TalentSkillDO();
                d.setUserId(userId);
                d.setName(sk.getName());
                d.setLevel(sk.getLevel());
                talentSkillMapper.insert(d);
            }
        }
        // 9. 证书
        talentCertificateMapper.deleteByUserId(userId);
        if (reqDTO.getCertificateList() != null) {
            for (TalentUserCreateReqDTO.CertificateDTO ce : reqDTO.getCertificateList()) {
                TalentCertificateDO d = new TalentCertificateDO();
                d.setUserId(userId);
                d.setName(ce.getName());
                d.setIssuer(ce.getIssuer());
                d.setIssueDate(ce.getIssueDate());
                d.setSource(ce.getSource());
                d.setStatus(ce.getStatus());
                d.setCertificateNo(ce.getCertificateNo());
                d.setExpiryDate(ce.getExpiryDate());
                d.setCertificateImageUrl(ce.getCertificateImageUrl());
                talentCertificateMapper.insert(d);
            }
        }
        // 10. 求职申请
        talentJobApplicationMapper.deleteByUserId(userId);
        if (reqDTO.getJobApplicationList() != null) {
            for (TalentUserCreateReqDTO.JobApplicationDTO ja : reqDTO.getJobApplicationList()) {
                TalentJobApplicationDO d = new TalentJobApplicationDO();
                d.setUserId(userId);
                d.setCompany(ja.getCompany());
                d.setPosition(ja.getPosition());
                d.setApplyDate(ja.getApplyDate());
                d.setStatus(ja.getStatus());
                talentJobApplicationMapper.insert(d);
            }
        }
        // 11. 工作经历
        talentEmploymentMapper.deleteByUserId(userId);
        if (reqDTO.getEmploymentList() != null) {
            for (TalentUserCreateReqDTO.EmploymentDTO em : reqDTO.getEmploymentList()) {
                TalentEmploymentDO d = new TalentEmploymentDO();
                d.setUserId(userId);
                d.setCompany(em.getCompany());
                d.setPosition(em.getPosition());
                d.setStartDate(em.getStartDate());
                d.setEndDate(em.getEndDate());
                d.setSalary(em.getSalary());
                talentEmploymentMapper.insert(d);
            }
        }
    }

    @Override
    public void disableTalentUser(Long id) {
        talentUserMapper.disable(id);
    }

    @Override
    public void changeTalentUserStatus(Long id, String status) {
        TalentUserDO userDO = new TalentUserDO();
        userDO.setUserId(id);
        userDO.setStatus(status);
        talentUserMapper.updateById(userDO);
    }

    /**
     * 合并人才，严格按照多级识别规则：
     * 1. 一级识别：身份证号（identityId）强制合并
     * 2. 二级识别：手机号（phone）强关联合并
     * 3. 三级识别：UnionID 弱关联提醒（此处仅预留，实际需扩展字段）
     * 4. 四级识别：人工辅助（需人工介入，后台操作）
     *
     * 合并流程：
     * - 若当前用户身份证号已存在于其他用户，则将当前用户所有数据迁移至主用户，并将当前用户状态置为"已合并"或"已禁用"。
     * - 若手机号已被其他用户绑定，提示是否合并，确认后执行合并。
     * - UnionID 仅做后台提醒，不自动合并。
     * - 人工辅助需后台人工操作。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void mergeTalentUser(Long id) {
        TalentUserDO current = talentUserMapper.selectById(id);
        if (current == null)
            return;
        // 1. 一级识别：身份证号强制合并
        if (current.getIdentityId() != null) {
            TalentUserDO main = talentUserMapper.selectByIdentityId(current.getIdentityId());
            if (main != null && !main.getUserId().equals(current.getUserId())) {
                // TODO: 迁移current所有关联数据到main（如订单、标签、子表等）
                // ...
                // 设置current为已合并/已禁用
                current.setStatus(TalentUserStatusEnum.TO_MERGE.getLabel());
                talentUserMapper.updateById(current);
                return;
            }
        }
        // 2. 二级识别：手机号强关联合并
        if (current.getPhone() != null) {
            TalentUserDO main = talentUserMapper.selectByPhone(current.getPhone());
            if (main != null && !main.getUserId().equals(current.getUserId())) {
                // TODO: 需用户确认，确认后迁移数据
                // ...
                current.setStatus(TalentUserStatusEnum.TO_MERGE.getLabel());
                talentUserMapper.updateById(current);
                return;
            }
        }
        // 3. 三级识别：UnionID弱关联提醒（此处仅预留）
        // TODO: UnionID逻辑需扩展字段和后台提醒
        // 4. 四级识别：人工辅助（需人工介入）
        // TODO: 人工辅助合并需后台人工操作
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTalentUser(TalentUserCreateReqDTO reqDTO) {
        // 1. 主表插入
        TalentUserDO userDO = new TalentUserDO();
        userDO.setName(reqDTO.getName());
        userDO.setPhone(reqDTO.getPhone());
        userDO.setIdentityId(reqDTO.getIdentityId());
        userDO.setEmail(reqDTO.getEmail());
        userDO.setGender(reqDTO.getGender());
        if (reqDTO.getBirthDate() != null && !"".equals(reqDTO.getBirthDate())) {
            userDO.setBirthDate(reqDTO.getBirthDate());
        } else {
            userDO.setBirthDate(null);
        }
        userDO.setAvatarUrl(reqDTO.getAvatarUrl());
        userDO.setRegisterSource(reqDTO.getSource());
        // 新增字段
        userDO.setOrgId(reqDTO.getOrgId());
        userDO.setOrgName(reqDTO.getOrgName());
        userDO.setTalentSource(reqDTO.getTalentSource());
        userDO.setIsSelfSupport(reqDTO.getIsSelfSupport());
        // status默认"正常"
        userDO.setStatus(reqDTO.getStatus() != null ? reqDTO.getStatus() : "正常");
        userDO.setCompleteness(reqDTO.getCompleteness() == null ? 0 : reqDTO.getCompleteness().byteValue());
        // oneid生成唯一值，转大写
        userDO.setOneid(genOneId().toUpperCase());
        talentUserMapper.insert(userDO);
        Long userId = userDO.getUserId();
        // 2. 标签处理（只根据 tags 字段，先删后插）
        talentUserTagMapper.deleteByUserId(userId);
        if (reqDTO.getTags() != null && !reqDTO.getTags().isEmpty()) {
            for (String tagName : reqDTO.getTags()) {
                TalentTagLibraryDO tag = talentTagLibraryMapper.selectOne(
                        new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TalentTagLibraryDO>()
                                .eq(TalentTagLibraryDO::getTagName, tagName)
                                .eq(TalentTagLibraryDO::getDeleted, false));
                if (tag == null) {
                    tag = new TalentTagLibraryDO();
                    tag.setTagName(tagName);
                    tag.setDeleted(false);
                    tag.setTenantId(1L);
                    String[] arr = tagName.split(":", 2);
                    Long tagTypeId = null;
                    if (arr.length == 2) {
                        String typeName = arr[0];
                        TalentTagTypeDO type = talentTagTypeMapper.selectOne(
                                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TalentTagTypeDO>()
                                        .eq(TalentTagTypeDO::getTypeName, typeName)
                                        .eq(TalentTagTypeDO::getDeleted, false));
                        if (type != null)
                            tagTypeId = type.getTagTypeId();
                    }
                    if (tagTypeId == null) {
                        TalentTagTypeDO otherType = talentTagTypeMapper.selectOne(
                                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TalentTagTypeDO>()
                                        .eq(TalentTagTypeDO::getTypeName, "其它")
                                        .eq(TalentTagTypeDO::getDeleted, false));
                        if (otherType != null)
                            tagTypeId = otherType.getTagTypeId();
                        else
                            throw new RuntimeException("未找到标签类型'其它'");
                    }
                    tag.setTagTypeId(tagTypeId);
                    tag.setDescription(arr.length == 2 ? arr[0] : "其它");
                    tag.setTagCode(UUID.randomUUID().toString().replaceAll("-", ""));
                    talentTagLibraryMapper.insert(tag);
                }
                TalentUserTagDO userTag = new TalentUserTagDO();
                userTag.setUserId(userId);
                userTag.setTagId(tag.getTagId());
                userTag.setTagTypeId(tag.getTagTypeId());
                userTag.setDeleted(false);
                userTag.setTenantId(1L);
                talentUserTagMapper.insert(userTag);
            }
        }
        // 3. 教育背景
        if (reqDTO.getEducationList() != null) {
            for (TalentUserCreateReqDTO.EducationDTO edu : reqDTO.getEducationList()) {
                TalentEducationDO d = new TalentEducationDO();
                d.setUserId(userId);
                d.setInstitution(edu.getInstitution());
                d.setCollegeAddress(edu.getCollegeAddress());
                d.setDegreeType(edu.getDegreeType());
                d.setMajor(edu.getMajor());
                d.setStartDate(edu.getStartDate());
                d.setEndDate(edu.getEndDate());
                d.setAcademicRanking(edu.getAcademicRanking());
                d.setIsInternship(edu.getIsInternship());
                d.setInternshipType(edu.getInternshipType());
                d.setInternshipDuration(edu.getInternshipDuration());
                talentEducationMapper.insert(d);
            }
        }
        // 4. 校园实践
        if (reqDTO.getCampusPracticeList() != null) {
            for (TalentUserCreateReqDTO.CampusPracticeDTO cp : reqDTO.getCampusPracticeList()) {
                TalentCampusPracticeDO d = new TalentCampusPracticeDO();
                d.setUserId(userId);
                d.setPracticeName(cp.getPracticeName());
                d.setOrganizer(cp.getOrganizer());
                d.setStartDate(cp.getStartDate());
                d.setEndDate(cp.getEndDate());
                d.setPracticeReport(cp.getPracticeReport());
                talentCampusPracticeMapper.insert(d);
            }
        }
        // 5. 实习经历
        if (reqDTO.getInternshipList() != null) {
            for (TalentUserCreateReqDTO.InternshipDTO it : reqDTO.getInternshipList()) {
                TalentInternshipDO d = new TalentInternshipDO();
                d.setUserId(userId);
                d.setCompany(it.getCompany());
                d.setPosition(it.getPosition());
                d.setStartDate(it.getStartDate());
                d.setEndDate(it.getEndDate());
                d.setResponsibilities(it.getResponsibilities());
                talentInternshipMapper.insert(d);
            }
        }
        // 6. 项目经历
        if (reqDTO.getProjectList() != null) {
            for (TalentUserCreateReqDTO.ProjectDTO pj : reqDTO.getProjectList()) {
                TalentProjectDO d = new TalentProjectDO();
                d.setUserId(userId);
                d.setName(pj.getName());
                d.setDescription(pj.getDescription());
                d.setStartDate(pj.getStartDate());
                d.setEndDate(pj.getEndDate());
                talentProjectMapper.insert(d);
            }
        }
        // 7. 培训经历
        if (reqDTO.getTrainingList() != null) {
            for (TalentUserCreateReqDTO.TrainingDTO tr : reqDTO.getTrainingList()) {
                TalentTrainingDO d = new TalentTrainingDO();
                d.setUserId(userId);
                d.setProvider(tr.getProvider());
                d.setCourse(tr.getCourse());
                d.setCompleteDate(tr.getCompleteDate());
                talentTrainingMapper.insert(d);
            }
        }
        // 8. 技能
        if (reqDTO.getSkillList() != null) {
            for (TalentUserCreateReqDTO.SkillDTO sk : reqDTO.getSkillList()) {
                TalentSkillDO d = new TalentSkillDO();
                d.setUserId(userId);
                d.setName(sk.getName());
                d.setLevel(sk.getLevel());
                talentSkillMapper.insert(d);
            }
        }
        // 9. 证书
        if (reqDTO.getCertificateList() != null) {
            for (TalentUserCreateReqDTO.CertificateDTO ce : reqDTO.getCertificateList()) {
                TalentCertificateDO d = new TalentCertificateDO();
                d.setUserId(userId);
                d.setName(ce.getName());
                d.setIssuer(ce.getIssuer());
                d.setIssueDate(ce.getIssueDate());
                d.setSource(ce.getSource());
                d.setStatus(ce.getStatus());
                d.setCertificateNo(ce.getCertificateNo());
                d.setExpiryDate(ce.getExpiryDate());
                d.setCertificateImageUrl(ce.getCertificateImageUrl());
                talentCertificateMapper.insert(d);
            }
        }
        // 10. 求职申请
        if (reqDTO.getJobApplicationList() != null) {
            for (TalentUserCreateReqDTO.JobApplicationDTO ja : reqDTO.getJobApplicationList()) {
                TalentJobApplicationDO d = new TalentJobApplicationDO();
                d.setUserId(userId);
                d.setCompany(ja.getCompany());
                d.setPosition(ja.getPosition());
                d.setApplyDate(ja.getApplyDate());
                d.setStatus(ja.getStatus());
                talentJobApplicationMapper.insert(d);
            }
        }
        // 11. 工作经历
        if (reqDTO.getEmploymentList() != null) {
            for (TalentUserCreateReqDTO.EmploymentDTO em : reqDTO.getEmploymentList()) {
                TalentEmploymentDO d = new TalentEmploymentDO();
                d.setUserId(userId);
                d.setCompany(em.getCompany());
                d.setPosition(em.getPosition());
                d.setStartDate(em.getStartDate());
                d.setEndDate(em.getEndDate());
                d.setSalary(em.getSalary());
                talentEmploymentMapper.insert(d);
            }
        }
        return userId;
    }

    /**
     * 生成唯一oneid，格式为GUID+随机字符串后取MD5
     */
    private String genOneId() {
        try {
            String raw = UUID.randomUUID().toString() + System.nanoTime() + Math.random();
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(raw.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            return UUID.randomUUID().toString().replaceAll("-", "");
        }
    }

    @Override
    public cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO getTalentUserDetail(Long id) {
        cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO resp = new cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO();
        // 主表
        TalentUserDO userDO = talentUserMapper.selectById(id);
        resp.setUser(TalentUserConvert.INSTANCE.convert(userDO));
        // 教育
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.EducationItem> educationList = TalentUserConvert.INSTANCE
                .convertEducationList(talentEducationMapper.selectListByUserId(id));

        resp.setEducationList(educationList);
        // 校园实践
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.CampusPracticeItem> campusPracticeList = TalentUserConvert.INSTANCE
                .convertCampusPracticeList(talentCampusPracticeMapper.selectListByUserId(id));
        resp.setCampusPracticeList(campusPracticeList);
        // 实习
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.InternshipItem> internshipList = TalentUserConvert.INSTANCE
                .convertInternshipList(talentInternshipMapper.selectListByUserId(id));
        resp.setInternshipList(internshipList);
        // 项目
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.ProjectItem> projectList = TalentUserConvert.INSTANCE
                .convertProjectList(talentProjectMapper.selectListByUserId(id));
        resp.setProjectList(projectList);
        // 培训
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TrainingItem> trainingList = TalentUserConvert.INSTANCE
                .convertTrainingList(talentTrainingMapper.selectListByUserId(id));
        resp.setTrainingList(trainingList);
        // 技能
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.SkillItem> skillList = TalentUserConvert.INSTANCE
                .convertSkillList(talentSkillMapper.selectListByUserId(id));
        resp.setSkillList(skillList);
        // 证书
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.CertificateItem> certificateList = TalentUserConvert.INSTANCE
                .convertCertificateList(talentCertificateMapper.selectListByUserId(id));
        resp.setCertificateList(certificateList);
        // 求职
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.JobApplicationItem> jobApplicationList = TalentUserConvert.INSTANCE
                .convertJobApplicationList(talentJobApplicationMapper.selectListByUserId(id));
        resp.setJobApplicationList(jobApplicationList);
        // 工作
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.EmploymentItem> employmentList = TalentUserConvert.INSTANCE
                .convertEmploymentList(talentEmploymentMapper.selectListByUserId(id));
        resp.setEmploymentList(employmentList);
        // 用户标签
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.UserTagItem> userTagList = TalentUserConvert.INSTANCE
                .convertUserTagList(talentUserTagMapper.selectListByUserId(id));
        for (cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.UserTagItem tag : userTagList) {
            if (tag.getTagId() != null) {
                cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentTagLibraryDO tagDO = talentTagLibraryMapper
                        .selectById(tag.getTagId());
                if (tagDO != null)
                    tag.setTagName(tagDO.getTagName());
            }
            if (tag.getTagTypeId() != null) {
                cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentTagTypeDO typeDO = talentTagTypeMapper
                        .selectById(tag.getTagTypeId());
                if (typeDO != null)
                    tag.setTagTypeName(typeDO.getTypeName());
            }
        }
        resp.setUserTagList(userTagList);
        // 时间轴组装
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem> timeline = new java.util.ArrayList<>();
        // 教育
        for (cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.EducationItem edu : educationList) {
            if (edu.getEndDate() != null) {
                cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem t = new cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem();
                t.setType("教育");
                t.setContent("毕业于" + edu.getInstitution());
                t.setDate(edu.getEndDate());
                timeline.add(t);
            }
        }
        // 校园实践
        for (cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.CampusPracticeItem cp : campusPracticeList) {
            if (cp.getEndDate() != null) {
                cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem t = new cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem();
                t.setType("实践");
                t.setContent("参与" + cp.getPracticeName());
                t.setDate(cp.getEndDate());
                timeline.add(t);
            }
        }
        // 实习
        for (cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.InternshipItem it : internshipList) {
            if (it.getEndDate() != null) {
                cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem t = new cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem();
                t.setType("实习");
                t.setContent("在" + it.getCompany() + "完成实习");
                t.setDate(it.getEndDate());
                timeline.add(t);
            }
        }
        // 项目
        for (cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.ProjectItem pj : projectList) {
            // 项目没有标准日期，略过或可扩展
        }
        // 培训
        for (cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TrainingItem tr : trainingList) {
            if (tr.getCompleteDate() != null) {
                cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem t = new cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem();
                t.setType("培训");
                t.setContent("完成" + tr.getCourse());
                t.setDate(tr.getCompleteDate());
                timeline.add(t);
            }
        }
        // 证书
        for (cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.CertificateItem ce : certificateList) {
            if (ce.getIssueDate() != null) {
                cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem t = new cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem();
                t.setType("认证");
                t.setContent("获得" + ce.getName());
                t.setDate(ce.getIssueDate());
                timeline.add(t);
            }
        }
        // 求职
        for (cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.JobApplicationItem ja : jobApplicationList) {
            if (ja.getApplyDate() != null) {
                cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem t = new cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem();
                t.setType("求职");
                t.setContent("申请" + ja.getCompany() + "-" + ja.getPosition());
                t.setDate(ja.getApplyDate());
                timeline.add(t);
            }
        }
        // 工作
        for (cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.EmploymentItem em : employmentList) {
            if (em.getStartDate() != null) {
                cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem t = new cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem();
                t.setType("就业");
                t.setContent("入职" + em.getCompany() + "-" + em.getPosition());
                t.setDate(em.getStartDate());
                timeline.add(t);
            }
            if (em.getEndDate() != null) {
                cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem t = new cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.TimelineItem();
                t.setType("就业");
                t.setContent("离职" + em.getCompany() + "-" + em.getPosition());
                t.setDate(em.getEndDate());
                timeline.add(t);
            }
        }
        // 按日期倒序
        timeline.sort((a, b) -> b.getDate().compareTo(a.getDate()));
        resp.setTimelineList(timeline);
        // 评价
        java.util.List<cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserCommentDO> commentDOList = talentUserCommentMapper
                .selectListByUserId(id);
        java.util.List<cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.CommentItem> commentList = TalentUserConvert.INSTANCE
                .convertCommentList(commentDOList);
        // 组装部分字段
        for (int i = 0; i < commentDOList.size(); i++) {
            cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO.CommentItem c = commentList
                    .get(i);
            cn.bztmaster.cnt.module.talentpool.dal.dataobject.talent.TalentUserCommentDO d = commentDOList.get(i);
            c.setRole(d.getRole());
            c.setName(d.getReviewerName());
            if ("订单".equals(d.getServiceType())) {
                c.setOrderNo(d.getServiceName());
            } else if ("课程".equals(d.getServiceType())) {
                c.setCourse(d.getServiceName());
            } else {
                c.setService(d.getServiceName());
            }
            c.setDate(d.getCreateTime() != null ? new java.text.SimpleDateFormat("yyyy-MM-dd").format(d.getCreateTime())
                    : null);
            c.setScore(d.getScore());
            c.setContent(d.getContent());
        }
        resp.setCommentList(commentList);
        return resp;
    }

    @Override
    public TalentCertificateRespDTO getTalentCertificatesByOneid(String oneid) {
        log.info("根据oneid查询资质证书，oneid: {}", oneid);

        // 1. 根据oneid查询用户信息
        TalentUserDO userDO = talentUserMapper.selectByOneid(oneid);
        if (userDO == null) {
            log.error("根据oneid未找到用户信息，oneid: {}", oneid);
            // 调试：查询所有用户看看是否有类似的oneid
            List<TalentUserDO> allUsers = talentUserMapper.selectList();
            log.info("数据库中所有用户数量: {}", allUsers.size());
            for (TalentUserDO user : allUsers) {
                log.info("用户信息 - userId: {}, oneid: {}, name: {}", user.getUserId(), user.getOneid(), user.getName());
            }
            return null;
        }

        log.info("找到用户信息，userId: {}, name: {}", userDO.getUserId(), userDO.getName());

        // 2. 根据userId查询已审核通过的资质证书
        List<TalentCertificateDO> certificates = talentCertificateMapper.selectVerifiedListByUserId(userDO.getUserId());
        log.info("查询到资质证书数量: {}", certificates.size());

        // 调试：如果没有找到证书，查询所有证书看看是否有该用户的证书
        if (certificates.isEmpty()) {
            List<TalentCertificateDO> allCertificates = talentCertificateMapper.selectListByUserId(userDO.getUserId());
            log.info("该用户的所有证书数量（包括未审核）: {}", allCertificates.size());
            for (TalentCertificateDO cert : allCertificates) {
                log.info("证书信息 - certificateId: {}, name: {}, status: {}",
                        cert.getCertificateId(), cert.getName(), cert.getStatus());
            }
        }

        // 3. 构建响应数据
        TalentCertificateRespDTO respDTO = new TalentCertificateRespDTO();
        List<TalentCertificateRespDTO.Certificate> certificateList = certificates.stream()
                .map(this::convertToCertificateDTO)
                .collect(Collectors.toList());
        respDTO.setCertificates(certificateList);

        return respDTO;
    }

    /**
     * 将TalentCertificateDO转换为TalentCertificateRespDTO.Certificate
     */
    private TalentCertificateRespDTO.Certificate convertToCertificateDTO(TalentCertificateDO certificateDO) {
        TalentCertificateRespDTO.Certificate certificate = new TalentCertificateRespDTO.Certificate();
        certificate.setCertificateId(certificateDO.getCertificateId());
        certificate.setCertificateNo(certificateDO.getCertificateNo());
        certificate.setName(certificateDO.getName());
        certificate.setIssuer(certificateDO.getIssuer());
        certificate.setIssueDate(certificateDO.getIssueDate());
        certificate.setExpiryDate(certificateDO.getExpiryDate());
        certificate.setSource(certificateDO.getSource());
        certificate.setStatus(certificateDO.getStatus());
        certificate.setCertificateImageUrl(certificateDO.getCertificateImageUrl());
        return certificate;
    }
}
