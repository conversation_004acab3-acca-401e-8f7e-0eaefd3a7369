package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 工单指派阿姨响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单指派阿姨响应 VO")
@Data
public class WorkOrderAssignAuntRespVO {

    @Schema(description = "指派是否成功", example = "true")
    private Boolean success;

    @Schema(description = "工单编号", example = "WO202312010001")
    private String workOrderNo;

    @Schema(description = "阿姨OneID", example = "a1b2c3d4-e5f6-7890-abcd-ef1234567890")
    private String auntOneid;

    @Schema(description = "阿姨姓名", example = "李阿姨")
    private String auntName;

    @Schema(description = "指派时间")
    private java.time.LocalDateTime assignmentTime;
} 