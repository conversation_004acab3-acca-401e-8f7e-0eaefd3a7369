package cn.bztmaster.cnt.module.publicbiz.api.aunt.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 机构列表响应 DTO
 *
 * <AUTHOR>
 */
@Data
public class AgencyListRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机构ID
     */
    private Long id;

    /**
     * 机构名称
     */
    private String name;

    /**
     * 机构描述
     */
    private String description;

    /**
     * 机构状态：cooperating-合作中/terminated-已解约
     */
    private String status;
}
