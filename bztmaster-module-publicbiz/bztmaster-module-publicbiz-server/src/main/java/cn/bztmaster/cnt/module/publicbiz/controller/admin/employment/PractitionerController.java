package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.framework.excel.core.util.ExcelUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.PractitionerConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.service.employment.PractitionerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/publicbiz/employment/practitioner")
@Tag(name = "就业服务-旗下阿姨管理")
public class PractitionerController {
    @Resource
    private PractitionerService practitionerService;

    @PostMapping("/create")
    @Operation(summary = "新增阿姨")
    public CommonResult<Long> create(@RequestBody PractitionerSaveReqVO reqVO) {
        return CommonResult.success(practitionerService.createPractitioner(reqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新阿姨")
    public CommonResult<Boolean> update(@RequestBody PractitionerUpdateReqVO reqVO) {
        practitionerService.updatePractitioner(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取阿姨详情")
    public CommonResult<PractitionerRespVO> get(@PathVariable("id") Long id) {
        return CommonResult.success(practitionerService.getPractitioner(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获取阿姨分页")
    public CommonResult<PageResult<PractitionerRespVO>> page(PractitionerPageReqVO reqVO) {
        return CommonResult.success(practitionerService.getPractitionerPage(reqVO));
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新阿姨平台状态")
    public CommonResult<Boolean> updateStatus(@PathVariable("id") Long id,
            @RequestBody PractitionerStatusUpdateReqVO reqVO) {
        reqVO.setId(id);
        practitionerService.updatePractitionerStatus(reqVO);
        return CommonResult.success(true);
    }

    @PutMapping("/{id}/rating")
    @Operation(summary = "更新阿姨评级")
    public CommonResult<Boolean> updateRating(@PathVariable("id") Long id,
            @RequestBody PractitionerRatingUpdateReqVO reqVO) {
        reqVO.setId(id);
        practitionerService.updatePractitionerRating(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出阿姨列表 Excel")
    public void export(HttpServletResponse response, @Validated PractitionerPageReqVO exportReqVO) throws IOException {
        List<PractitionerDO> list = practitionerService.getPractitionerList(exportReqVO);
        // 输出
        ExcelUtils.write(response, "阿姨数据.xls", "数据", PractitionerExcelVO.class,
                PractitionerConvert.INSTANCE.convertExcelList(list));
    }

}