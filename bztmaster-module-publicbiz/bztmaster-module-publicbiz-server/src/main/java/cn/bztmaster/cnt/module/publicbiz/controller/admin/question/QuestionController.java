package cn.bztmaster.cnt.module.publicbiz.controller.admin.question;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionService;
import cn.bztmaster.cnt.framework.excel.core.util.ExcelUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.Map;

/**
 * 资源中心 - 考题管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/publicbiz/question")
@Tag(name = "资源中心 - 考题管理")
public class QuestionController {

    @Resource
    private QuestionService questionService;

    @GetMapping("/page")
    @Operation(summary = "考题分页查询", description = "分页查询考题列表，支持多条件筛选")
    public CommonResult<PageResult<QuestionRespVO>> pageQuestion(@Valid QuestionPageReqVO reqVO) {
        PageResult<QuestionRespVO> pageResult = questionService.pageQuestion(reqVO);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "考题详情查询", description = "根据ID查询考题详细信息，包含选项信息")
    @Parameter(name = "id", description = "考题ID", required = true, example = "1")
    public CommonResult<QuestionRespVO> getQuestion(@PathVariable("id") Long id) {
        QuestionRespVO question = questionService.getQuestion(id);
        return CommonResult.success(question);
    }

    @PostMapping("/add")
    @Operation(summary = "新增考题", description = "新增考题信息")
    public CommonResult<Long> createQuestion(@Valid @RequestBody QuestionSaveReqVO reqVO) {
        Long questionId = questionService.createQuestion(reqVO);
        return CommonResult.success(questionId);
    }

    @PutMapping("/update")
    @Operation(summary = "编辑考题", description = "编辑考题信息")
    public CommonResult<Boolean> updateQuestion(@Valid @RequestBody QuestionSaveReqVO reqVO) {
        questionService.updateQuestion(reqVO);
        return CommonResult.success(true);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除考题", description = "删除考题（软删除）")
    @Parameter(name = "id", description = "考题ID", required = true, example = "1")
    public CommonResult<Boolean> deleteQuestion(@PathVariable("id") Long id) {
        questionService.deleteQuestion(id);
        return CommonResult.success(true);
    }

    @GetMapping("/statistics")
    @Operation(summary = "考题统计报表", description = "获取考题统计数据，包括题目总数、选择题数、判断题数、简答题数")
    public CommonResult<QuestionStatisticsRespVO> getQuestionStatistics(
            @Parameter(description = "业务模块筛选") @RequestParam(value = "biz", required = false) String biz,
            @Parameter(description = "一级分类筛选") @RequestParam(value = "level1Name", required = false) String level1Name,
            @Parameter(description = "二级分类筛选") @RequestParam(value = "level2Name", required = false) String level2Name,
            @Parameter(description = "三级分类筛选") @RequestParam(value = "level3Name", required = false) String level3Name) {
        QuestionStatisticsRespVO statistics = questionService.getQuestionStatistics(biz, level1Name, level2Name, level3Name);
        return CommonResult.success(statistics);
    }

    @PostMapping("/batch-import")
    @Operation(summary = "批量导入考题", description = "批量导入考题，支持Excel和CSV文件上传")
    public CommonResult<Map<String, Object>> batchImportQuestion(
            @Parameter(description = "Excel/CSV文件") @RequestParam("file") MultipartFile file) {
        Map<String, Object> result = questionService.batchImportQuestion(file);
        return CommonResult.success(result);
    }

    @PostMapping("/import/validate-excel")
    @Operation(summary = "校验Excel/CSV导入的考题数据")
    public CommonResult<QuestionImportValidateRespVO> validateImportExcelQuestions(@RequestParam("file") MultipartFile file) throws Exception {
        List<QuestionImportExcelVO> list = ExcelUtils.read(file, QuestionImportExcelVO.class);
        return CommonResult.success(questionService.validateImportExcelQuestions(list));
    }

    @PostMapping("/import/execute")
    @Operation(summary = "执行批量导入考题（只导入校验通过的数据）")
    public CommonResult<QuestionImportRespVO> executeImportQuestions(@RequestBody QuestionImportReqVO reqVO) {
        return CommonResult.success(questionService.importValidQuestions(reqVO.getQuestionList()));
    }

}
