<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.question.QuestionOptionMapper">

    <!-- 批量插入选项 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO publicbiz_question_option (
            tenant_id, question_id, option_type, option_key, option_content, is_correct,
            sort_order, match_target, creator, create_time, update_time
        ) VALUES
        <foreach collection="options" item="item" separator=",">
            (
                #{item.tenantId}, #{item.questionId}, #{item.optionType}, #{item.optionKey},
                #{item.optionContent}, #{item.isCorrect}, #{item.sortOrder}, #{item.matchTarget},
                #{item.creator}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

</mapper>
