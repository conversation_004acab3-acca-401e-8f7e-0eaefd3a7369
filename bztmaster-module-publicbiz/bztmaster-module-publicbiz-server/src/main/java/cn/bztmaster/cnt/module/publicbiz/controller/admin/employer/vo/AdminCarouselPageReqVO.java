package cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 轮播图分页查询请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 轮播图分页查询请求")
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminCarouselPageReqVO extends PageParam {

    @Schema(description = "轮播图标题，支持模糊查询", example = "专业月嫂")
    private String carouselTitle;

    @Schema(description = "状态：1-启用，0-禁用", example = "1")
    private Integer status;

    @Schema(description = "平台：employer-雇主端，aunt-阿姨端", example = "employer")
    private String platform;

    @Schema(description = "开始时间范围-开始", example = "2024-01-01 00:00:00")
    private LocalDateTime startTimeBegin;

    @Schema(description = "开始时间范围-结束", example = "2024-12-31 23:59:59")
    private LocalDateTime startTimeEnd;

    @Schema(description = "结束时间范围-开始", example = "2024-01-01 00:00:00")
    private LocalDateTime endTimeBegin;

    @Schema(description = "结束时间范围-结束", example = "2024-12-31 23:59:59")
    private LocalDateTime endTimeEnd;

    @Schema(description = "创建时间范围-开始", example = "2024-01-01 00:00:00")
    private LocalDateTime createTimeBegin;

    @Schema(description = "创建时间范围-结束", example = "2024-12-31 23:59:59")
    private LocalDateTime createTimeEnd;
} 