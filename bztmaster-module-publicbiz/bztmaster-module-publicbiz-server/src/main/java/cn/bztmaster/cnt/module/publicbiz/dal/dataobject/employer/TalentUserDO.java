package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 人才用户 DO
 *
 * <AUTHOR>
 */
@TableName("talent_user")
@KeySequence("talent_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TalentUserDO extends BaseDO {

    /**
     * 用户ID
     */
    @TableId
    private Long userId;
    /**
     * OneID
     */
    private String oneid;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 身份证号
     */
    private String identityId;
    /**
     * 性别
     */
    private String gender;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 注册来源
     */
    private String registerSource;
    /**
     * 状态
     */
    private String status;
}

