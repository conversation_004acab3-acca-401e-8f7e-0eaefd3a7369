我现在需要根据订单主表和家政订单详情表生成家政服务任务，数据存于家政服务任务表,请参考`PromptDocument/订单中心/家政服务订单/任务生成引擎/家政订单生成任务相关表结构.md`中的表结构,创建一个生成任务接口，
放置于‘src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/order/DomesticTaskOrderController.java’，
接口实现业务逻辑要求如下：
**长周期套餐任务生成逻辑:**
- 系统根据套餐配置的"服务周期"和"服务频次"，自动计算需要生成的任务数量。
- 例如：30 天每日服务 = 生成 30 个任务；26 天每日服务 = 生成 26 个任务。
- 任务状态初始为"待分配"，等待分配给服务人员。
**次数次卡套餐任务生成逻辑:**
- 系统根据套餐配置的"服务次数"和雇主预约的具体时间，生成对应数量的任务。
- 例如：4 次上门收纳 = 生成 4 个任务；6 次深度保洁 = 生成 6 个任务。
- 任务状态初始为"待分配"，等待分配给服务人员。

参考原型:
- PromptDocument/订单中心/家政服务订单/任务生成引擎/家政服务小程序.html
- PromptDocument/订单中心/家政服务订单/任务生成引擎/就业服务服务套餐.html
- PromptDocument/订单中心/家政服务订单/任务生成引擎/订单中心.html