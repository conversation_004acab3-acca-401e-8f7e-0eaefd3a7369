package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@TableName("publicbiz_practitioner_qualification")
@Schema(description = "阿姨资质文件表 DO")
public class PractitionerQualificationDO {
    @TableId
    private Long id;

    @TableField("practitioner_oneId")
    private String practitionerOneId;

    @TableField("file_type")
    private String fileType;

    @TableField("file_name")
    private String fileName;

    @TableField("file_url")
    private String fileUrl;

    @TableField("file_size")
    private Long fileSize;

    @TableField("file_extension")
    private String fileExtension;

    @TableField("sort_order")
    private Integer sortOrder;

    private Integer status;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    private String creator;
    private String updater;
    private Boolean deleted;

    @TableField("tenant_id")
    private Long tenantId;
}
