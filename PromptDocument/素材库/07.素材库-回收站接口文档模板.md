# 素材回收站接口文档模板

本接口文档适用于素材库各类型（图片、视频、文章、文档、图文）的回收站功能，接口命名与正常模块接口区分，避免冲突。

---

## 1. 回收站列表查询接口

### 1.1 图片回收站列表

- **接口地址**：`GET /system/material/image/recycleList`
- **说明**：查询已删除（deleted=1）且30天内的图片素材，支持分页、名称、来源筛选。
- **请求参数**：| 参数名 | 类型 | 必填 | 说明 | | -------------- | ------- | ---- | -------------------- | | pageNo | int | 是 | 页码 | | pageSize | int | 是 | 每页数量 | | name | string | 否 | 素材名称（模糊搜索） | | sourceOrgId | int | 否 | 来源机构ID | | updateTimeFrom | string | 否 | 起始时间（30天前） | | updateTimeTo | string | 否 | 截止时间（当前时间） |

- **返回示例**：

```json
{
  "total": 2,
  "list": [
    {
      "id": 101,
      "name": "图片A",
      "url": "http://...",
      "sourceOrgId": 1,
      "sourceOrgName": "机构A",
      "deleted": 1,
      "updateTime": "2024-06-01 12:00:00"
    },
    {
      "id": 102,
      "name": "图片B",
      "url": "http://...",
      "sourceOrgId": 2,
      "sourceOrgName": "机构B",
      "deleted": 1,
      "updateTime": "2024-06-02 13:00:00"
    }
  ]
}
```

### 1.2 其他类型回收站列表

- **接口地址**：
  - 视频：`GET /system/material/video/recycleList`
  - 文章：`GET /system/material/article/recycleList`
  - 文档：`GET /system/material/document/recycleList`
  - 图文：`GET /system/material/news/recycleList`
- **参数与返回值结构同上，字段根据实际类型适配。**

---

## 2. 回收站恢复接口

### 2.1 图片回收站恢复

- **接口地址**：`POST /system/material/image/recycleRestore`
- **说明**：将已删除（deleted=1）的图片素材恢复为正常（deleted=0）。支持批量。
- **请求参数**：| 参数名 | 类型 | 必填 | 说明 | | ------ | ------------ | ---- | ------------ | | id | int/array | 是 | 素材ID或ID数组 |

- **请求示例**：

```json
{
  "id": [101, 102]
}
```

- **返回示例**：

```json
{
  "success": true
}
```

### 2.2 其他类型回收站恢复

- **接口地址**：
  - 视频：`POST /system/material/video/recycleRestore`
  - 文章：`POST /system/material/article/recycleRestore`
  - 文档：`POST /system/material/document/recycleRestore`
  - 图文：`POST /system/material/news/recycleRestore`
- **参数与返回值结构同上。**

---

## 3. 回收站永久删除接口

### 3.1 图片回收站永久删除

- **接口地址**：`POST /system/material/image/recycleDelete`
- **说明**：物理删除已删除（deleted=1）的图片素材，支持批量。
- **请求参数**：| 参数名 | 类型 | 必填 | 说明 | | ------ | ------------ | ---- | ------------ | | id | int/array | 是 | 素材ID或ID数组 |

- **请求示例**：

```json
{
  "id": [101, 102]
}
```

- **返回示例**：

```json
{
  "success": true
}
```

### 3.2 其他类型回收站永久删除

- **接口地址**：
  - 视频：`POST /system/material/video/recycleDelete`
  - 文章：`POST /system/material/article/recycleDelete`
  - 文档：`POST /system/material/document/recycleDelete`
  - 图文：`POST /system/material/news/recycleDelete`
- **参数与返回值结构同上。**

---

## 4. 说明

- 所有回收站接口均与正常模块接口分离，接口名带`recycle`前/后缀，避免命名冲突。
- 查询接口自动筛选`deleted=1`且`update_time`在30天内的数据。
- 恢复/删除接口支持批量操作。
- 返回值结构可根据实际项目风格调整。
