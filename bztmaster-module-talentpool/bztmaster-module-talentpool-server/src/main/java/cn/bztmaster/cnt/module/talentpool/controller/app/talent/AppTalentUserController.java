package cn.bztmaster.cnt.module.talentpool.controller.app.talent;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserPageReqDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserRespDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserUpdateReqDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserTagDetailRespDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentCertificateRespDTO;
import cn.bztmaster.cnt.module.talentpool.service.talent.TalentUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;

/**
 * 人才用户 Controller
 *
 * 提供人才库用户的分页、详情、编辑、停用、合并等接口。
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apachecore/talentpool")
@Tag(name = "人才用户")
public class AppTalentUserController {

    @Resource
    private TalentUserService talentUserService;

    @GetMapping("/page")
    @Operation(summary = "分页查询人才库用户列表")
    public CommonResult<PageResult<TalentUserRespDTO>> getTalentUserPage(TalentUserPageReqDTO reqDTO) {
        return CommonResult.success(talentUserService.getTalentUserPage(reqDTO));
    }

    @GetMapping("/get")
    @Operation(summary = "根据ID获取人才详细信息")
    public CommonResult<TalentUserRespDTO> getTalentUser(@RequestParam("id") Long id) {
        return CommonResult.success(talentUserService.getTalentUser(id));
    }

    @GetMapping("/get-by-oneid")
    @Operation(summary = "根据OneID获取人才详细信息")
    public CommonResult<TalentUserRespDTO> getTalentUserByOneid(@RequestParam("oneid") String oneid) {
        return CommonResult.success(talentUserService.getTalentUserByOneid(oneid));
    }

    @GetMapping("/get-tags-by-oneid")
    @Operation(summary = "根据OneID获取人才详细标签信息")
    public CommonResult<List<TalentUserTagDetailRespDTO>> getTalentUserTagsByOneid(
            @RequestParam("oneid") String oneid) {
        return CommonResult.success(talentUserService.getTalentUserTagsByOneid(oneid));
    }

    @PutMapping("/update")
    @Operation(summary = "编辑人才基本信息")
    public CommonResult<Boolean> updateTalentUser(@RequestBody TalentUserUpdateReqDTO reqDTO) {
        talentUserService.updateTalentUser(reqDTO);
        return CommonResult.success(true);
    }

    @PostMapping("/disable")
    @Operation(summary = "停用人才")
    public CommonResult<Boolean> disableTalentUser(@RequestParam("id") Long id) {
        talentUserService.disableTalentUser(id);
        return CommonResult.success(true);
    }

    @PostMapping("/merge")
    @Operation(summary = "合并人才（多级识别：身份证号/手机号/UnionID/人工辅助）")
    public CommonResult<Boolean> mergeTalentUser(@RequestParam("id") Long id) {
        talentUserService.mergeTalentUser(id);
        return CommonResult.success(true);
    }

    @GetMapping("/get-certificates-by-oneid")
    @Operation(summary = "根据OneID获取人才资质证书信息")
    @PermitAll
    public CommonResult<TalentCertificateRespDTO> getTalentCertificatesByOneid(@RequestParam("oneid") String oneid) {
        return CommonResult.success(talentUserService.getTalentCertificatesByOneid(oneid));
    }
}