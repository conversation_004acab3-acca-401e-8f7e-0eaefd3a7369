package cn.bztmaster.cnt.module.publicbiz.service.employer;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsHomeRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsIncrementViewRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsListRespVO;

/**
 * 资讯 Service 接口
 *
 * <AUTHOR>
 */
public interface NewsService {

    // ==================== 管理后台接口 ====================

    /**
     * 创建资讯
     *
     * @param createReqVO 创建请求
     * @return 资讯ID
     */
    Long createNews(AdminNewsSaveReqVO createReqVO);

    /**
     * 更新资讯
     *
     * @param updateReqVO 更新请求
     */
    void updateNews(AdminNewsSaveReqVO updateReqVO);

    /**
     * 删除资讯
     *
     * @param id 资讯ID
     */
    void deleteNews(Long id);

    /**
     * 根据ID获取资讯详情
     *
     * @param id 资讯ID
     * @return 资讯详情
     */
    AdminNewsRespVO getNews(Long id);

    /**
     * 分页查询资讯列表
     *
     * @param pageReqVO 分页查询请求
     * @return 资讯分页结果
     */
    PageResult<AdminNewsRespVO> getNewsPage(AdminNewsPageReqVO pageReqVO);

    /**
     * 更新资讯状态
     *
     * @param id 资讯ID
     * @param status 目标状态
     */
    void updateNewsStatus(Long id, String status);

    // ==================== 前端接口 ====================

    /**
     * 获取首页资讯列表（前N条）
     *
     * @param limit 返回数量，最大10
     * @return 首页资讯列表
     */
    NewsHomeRespVO getHomeNewsList(Integer limit);

    /**
     * 分页查询资讯列表
     *
     * @param pageParam  分页参数
     * @param categoryId 分类ID筛选
     * @param keyword    标题关键词搜索
     * @param status     状态筛选：draft-草稿/published-已发布/offline-已下架
     * @return 资讯列表分页结果
     */
    NewsListRespVO getNewsPage(PageParam pageParam, Long categoryId, String keyword, String status);

    /**
     * 获取资讯详情
     *
     * @param id 资讯ID
     * @return 资讯详情
     */
    NewsDetailRespVO getNewsDetail(Long id);

    /**
     * 增加资讯浏览次数
     *
     * @param id 资讯ID
     * @return 增加后的浏览次数
     */
    NewsIncrementViewRespVO incrementViewCount(Long id);

}