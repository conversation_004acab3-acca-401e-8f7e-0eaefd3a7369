package cn.bztmaster.cnt.module.publicbiz.convert;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteManagementSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.convert.siteManagement.SiteManagementConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteManagementDO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * SiteManagementConvert 转换器测试类
 * 
 * 专门测试操作日志相关的类型转换功能
 * 
 * <AUTHOR>
 */
@SpringBootTest
public class SiteManagementConvertTest {

    /**
     * 测试 convertToSaveReqVOForLog 方法
     * 重点测试 seatTypes 字段从 JSON 字符串到对象列表的转换
     */
    @Test
    public void testConvertToSaveReqVOForLog() {
        // 创建测试用的 SiteManagementDO 对象
        SiteManagementDO siteManagementDO = new SiteManagementDO();
        siteManagementDO.setId(1L);
        siteManagementDO.setName("测试场地");
        siteManagementDO.setCampus("TEST_CAMPUS");
        siteManagementDO.setCampusName("测试校区");
        siteManagementDO.setType("培训教室");
        siteManagementDO.setLocation("A座1楼101室");
        siteManagementDO.setEquipment("投影仪、音响设备、空调、白板");
        siteManagementDO.setStatus("可用");
        siteManagementDO.setDescription("标准培训教室，适合各类技能培训");
        siteManagementDO.setManager("张老师");
        siteManagementDO.setManagerPhone("13812345678");
        
        // 设置 seatTypes 为 JSON 字符串格式（模拟数据库中的存储格式）
        String seatTypesJson = "[{\"name\":\"普通座\",\"count\":50,\"remark\":\"标准课桌椅\"},{\"name\":\"VIP座\",\"count\":10,\"remark\":\"高端座椅\"}]";
        siteManagementDO.setSeatTypes(seatTypesJson);
        
        System.out.println("原始 SiteManagementDO:");
        System.out.println("- ID: " + siteManagementDO.getId());
        System.out.println("- 场地名称: " + siteManagementDO.getName());
        System.out.println("- seatTypes (JSON): " + siteManagementDO.getSeatTypes());
        
        try {
            // 执行转换
            SiteManagementSaveReqVO convertedVO = SiteManagementConvert.INSTANCE.convertToSaveReqVOForLog(siteManagementDO);
            
            System.out.println("\n转换后的 SiteManagementSaveReqVO:");
            System.out.println("- ID: " + convertedVO.getId());
            System.out.println("- 场地名称: " + convertedVO.getName());
            System.out.println("- 校区: " + convertedVO.getCampus());
            System.out.println("- 校区名称: " + convertedVO.getCampusName());
            System.out.println("- 场地类型: " + convertedVO.getType());
            System.out.println("- 具体位置: " + convertedVO.getLocation());
            System.out.println("- 设备配置: " + convertedVO.getEquipment());
            System.out.println("- 状态: " + convertedVO.getStatus());
            System.out.println("- 场地描述: " + convertedVO.getDescription());
            System.out.println("- 负责人姓名: " + convertedVO.getManager());
            System.out.println("- 负责人电话: " + convertedVO.getManagerPhone());
            
            // 重点检查 seatTypes 字段的转换
            List<SiteManagementSaveReqVO.SeatTypeVO> seatTypes = convertedVO.getSeatTypes();
            System.out.println("- seatTypes (对象列表):");
            if (seatTypes != null) {
                for (int i = 0; i < seatTypes.size(); i++) {
                    SiteManagementSaveReqVO.SeatTypeVO seatType = seatTypes.get(i);
                    System.out.println("  [" + i + "] 名称: " + seatType.getName() + 
                                     ", 数量: " + seatType.getCount() + 
                                     ", 备注: " + seatType.getRemark());
                }
            } else {
                System.out.println("  null");
            }
            
            System.out.println("\n✅ 转换成功！seatTypes 字段从 JSON 字符串成功转换为对象列表");
            
        } catch (Exception e) {
            System.err.println("\n❌ 转换失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 测试空 seatTypes 的转换
     */
    @Test
    public void testConvertWithNullSeatTypes() {
        SiteManagementDO siteManagementDO = new SiteManagementDO();
        siteManagementDO.setId(2L);
        siteManagementDO.setName("无座位配置的场地");
        siteManagementDO.setSeatTypes(null); // 空的 seatTypes
        
        System.out.println("测试空 seatTypes 的转换:");
        
        try {
            SiteManagementSaveReqVO convertedVO = SiteManagementConvert.INSTANCE.convertToSaveReqVOForLog(siteManagementDO);
            
            System.out.println("- 转换后的 seatTypes: " + convertedVO.getSeatTypes());
            System.out.println("✅ 空 seatTypes 转换成功");
            
        } catch (Exception e) {
            System.err.println("❌ 空 seatTypes 转换失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 测试空字符串 seatTypes 的转换
     */
    @Test
    public void testConvertWithEmptySeatTypes() {
        SiteManagementDO siteManagementDO = new SiteManagementDO();
        siteManagementDO.setId(3L);
        siteManagementDO.setName("空字符串座位配置的场地");
        siteManagementDO.setSeatTypes(""); // 空字符串 seatTypes
        
        System.out.println("测试空字符串 seatTypes 的转换:");
        
        try {
            SiteManagementSaveReqVO convertedVO = SiteManagementConvert.INSTANCE.convertToSaveReqVOForLog(siteManagementDO);
            
            System.out.println("- 转换后的 seatTypes: " + convertedVO.getSeatTypes());
            System.out.println("✅ 空字符串 seatTypes 转换成功");
            
        } catch (Exception e) {
            System.err.println("❌ 空字符串 seatTypes 转换失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
}
