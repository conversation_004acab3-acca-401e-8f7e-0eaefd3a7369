package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 工单接单请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单接单请求 VO")
@Data
public class WorkOrderAcceptReqVO {

    @Schema(description = "工单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "工单ID不能为空")
    private Long workOrderId;
} 