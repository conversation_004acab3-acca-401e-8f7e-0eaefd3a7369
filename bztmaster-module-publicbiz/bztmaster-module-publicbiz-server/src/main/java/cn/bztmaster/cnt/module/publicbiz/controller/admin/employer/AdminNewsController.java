package cn.bztmaster.cnt.module.publicbiz.controller.admin.employer;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.NewsStatusUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.NewsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 资讯管理
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 资讯管理")
@RestController
@RequestMapping("/publicbiz/news")
@Validated
public class AdminNewsController {

    @Resource
    private NewsService newsService;

    @PostMapping("/create")
    @Operation(summary = "创建资讯")
    @PreAuthorize("@ss.hasPermission('publicbiz:news:create')")
    public CommonResult<Long> createNews(@Valid @RequestBody AdminNewsSaveReqVO createReqVO) {
        return success(newsService.createNews(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新资讯")
    @PreAuthorize("@ss.hasPermission('publicbiz:news:update')")
    public CommonResult<Boolean> updateNews(@Valid @RequestBody AdminNewsSaveReqVO updateReqVO) {
        newsService.updateNews(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除资讯")
    @PreAuthorize("@ss.hasPermission('publicbiz:news:delete')")
    public CommonResult<Boolean> deleteNews(@PathVariable("id") Long id) {
        newsService.deleteNews(id);
        return success(true);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获得资讯详情")
    @Parameter(name = "id", description = "资讯ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:news:query')")
    public CommonResult<AdminNewsRespVO> getNews(@PathVariable("id") Long id) {
        AdminNewsRespVO news = newsService.getNews(id);
        return success(news);
    }

    @PostMapping("/page")
    @Operation(summary = "获得资讯分页")
    @PreAuthorize("@ss.hasPermission('publicbiz:news:query')")
    public CommonResult<PageResult<AdminNewsRespVO>> getNewsPage(@Valid @RequestBody AdminNewsPageReqVO pageReqVO) {
        PageResult<AdminNewsRespVO> pageResult = newsService.getNewsPage(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/updateStatus")
    @Operation(summary = "更新资讯状态")
    @PreAuthorize("@ss.hasPermission('publicbiz:news:update')")
    public CommonResult<Boolean> updateNewsStatus(@Valid @RequestBody NewsStatusUpdateReqVO reqVO) {
        newsService.updateNewsStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }
} 