package cn.bztmaster.cnt.module.publicbiz.service.employment.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.ServicePackageCarouselConvert;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.ServicePackageConvert;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.ServicePackageFeatureConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageCarouselDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageFeatureDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageCarouselMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageFeatureMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.partner.PartnerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO;
import cn.bztmaster.cnt.module.publicbiz.service.employment.ServicePackageService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants.*;

@Service
@Validated
@Slf4j
public class ServicePackageServiceImpl implements ServicePackageService {

    @Resource
    private ServicePackageMapper servicePackageMapper;

    @Resource
    private ServicePackageCarouselMapper servicePackageCarouselMapper;

    @Resource
    private ServicePackageFeatureMapper servicePackageFeatureMapper;

    @Resource
    private PartnerMapper partnerMapper;

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_TYPE, subType = SERVICE_PACKAGE_CREATE_SUB_TYPE, bizNo = "{{#result}}", success = SERVICE_PACKAGE_CREATE_SUCCESS)
    public Long createServicePackage(ServicePackageSaveReqVO createReqVO) {
        // 1. 插入主表
        ServicePackageDO servicePackage = ServicePackageConvert.INSTANCE.convert(createReqVO);
        servicePackage.setCreateTime(new Date());
        servicePackage.setDeleted(false);
        servicePackage.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
        servicePackage.setTenantId(SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId() : 1L);
        
        // 设置合作伙伴ID和审核状态
        Long currentUserPartnerId = SecurityFrameworkUtils.getLoginUserPartnerId();
        if (createReqVO.getPartnerId() == null) {
            // 如果前端未传递合作伙伴ID，则使用当前登录用户的合作伙伴ID
            servicePackage.setPartnerId(currentUserPartnerId);
        }

        // 设置合作伙伴名称
        Long finalPartnerId = servicePackage.getPartnerId();
        if (finalPartnerId != null) {
            try {
                PartnerDO partner = partnerMapper.selectById(finalPartnerId);
                if (partner != null && !Boolean.TRUE.equals(partner.getDeleted())) {
                    servicePackage.setPartnerName(partner.getName());
                } else {
                    servicePackage.setPartnerName(null);
                    log.warn("合作伙伴ID {} 对应的合作伙伴不存在或已删除", finalPartnerId);
                }
            } catch (Exception e) {
                servicePackage.setPartnerName(null);
                log.error("查询合作伙伴信息失败，partnerId: {}", finalPartnerId, e);
            }
        }

        servicePackage.setAuditStatus("auditing"); // 默认审核中
        
        servicePackageMapper.insert(servicePackage);

        // 2. 插入轮播图
        if (createReqVO.getCarouselList() != null) {
            for (ServicePackageCarouselSaveReqVO carouselVO : createReqVO.getCarouselList()) {
                ServicePackageCarouselDO carousel = ServicePackageCarouselConvert.INSTANCE.convert(carouselVO);
                carousel.setPackageId(servicePackage.getId());
                carousel.setCreateTime(new Date());
                carousel.setDeleted(false);
                carousel.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                carousel.setTenantId(SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId() : 1L);
                servicePackageCarouselMapper.insert(carousel);
            }
        }

        // 3. 插入特色标签
        if (createReqVO.getFeatureList() != null) {
            for (ServicePackageFeatureSaveReqVO featureVO : createReqVO.getFeatureList()) {
                ServicePackageFeatureDO feature = ServicePackageFeatureConvert.INSTANCE.convert(featureVO);
                feature.setPackageId(servicePackage.getId());
                feature.setCreateTime(new Date());
                feature.setDeleted(false);
                feature.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                feature.setTenantId(SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId() : 1L);
                servicePackageFeatureMapper.insert(feature);
            }
        }

        // 记录操作日志上下文
        LogRecordContext.putVariable("servicePackage", servicePackage);
        return servicePackage.getId();
    }

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_TYPE, subType = SERVICE_PACKAGE_UPDATE_SUB_TYPE, bizNo = "{{#reqVO.id}}", success = SERVICE_PACKAGE_UPDATE_SUCCESS)
    public void updateServicePackage(ServicePackageUpdateReqVO updateReqVO) {
        // 1. 校验存在
        ServicePackageDO oldServicePackage = validateServicePackageExists(updateReqVO.getId());

        // 2. 更新主表
        ServicePackageDO servicePackage = ServicePackageConvert.INSTANCE.convert(updateReqVO);
        servicePackage.setUpdateTime(new Date());
        servicePackage.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());

        // 同步合作伙伴名称（如果partnerId发生变化）
        if (updateReqVO.getPartnerId() != null &&
            !Objects.equals(updateReqVO.getPartnerId(), oldServicePackage.getPartnerId())) {
            try {
                PartnerDO partner = partnerMapper.selectById(updateReqVO.getPartnerId());
                if (partner != null && !Boolean.TRUE.equals(partner.getDeleted())) {
                    servicePackage.setPartnerName(partner.getName());
                } else {
                    servicePackage.setPartnerName(null);
                    log.warn("更新服务套餐时，合作伙伴ID {} 对应的合作伙伴不存在或已删除", updateReqVO.getPartnerId());
                }
            } catch (Exception e) {
                servicePackage.setPartnerName(null);
                log.error("更新服务套餐时查询合作伙伴信息失败，partnerId: {}", updateReqVO.getPartnerId(), e);
            }
        }

        servicePackageMapper.updateById(servicePackage);

        // 3. 智能更新轮播图（有ID的更新，没有ID的新增，删除不存在的）
        String carouselChangeLog = updateServicePackageCarousels(servicePackage.getId(), updateReqVO.getCarouselList());

        // 4. 智能更新特色标签（有ID的更新，没有ID的新增，删除不存在的）
        String featureChangeLog = updateServicePackageFeatures(servicePackage.getId(), updateReqVO.getFeatureList());

        // 手动比较字段变更，避免_DIFF函数的误报问题
        StringBuilder fieldChanges = new StringBuilder();

        // Compare basic fields
        if (!Objects.equals(oldServicePackage.getName(), updateReqVO.getName())) {
            fieldChanges.append("【套餐名称】从【").append(oldServicePackage.getName()).append("】修改为【").append(updateReqVO.getName()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getCategory(), updateReqVO.getCategory())) {
            fieldChanges.append("【服务分类】从【").append(oldServicePackage.getCategory()).append("】修改为【").append(updateReqVO.getCategory()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getThumbnail(), updateReqVO.getThumbnail())) {
            fieldChanges.append("【套餐主图】从【").append(oldServicePackage.getThumbnail()).append("】修改为【").append(updateReqVO.getThumbnail()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getPrice(), updateReqVO.getPrice())) {
            fieldChanges.append("【套餐价格】从【").append(oldServicePackage.getPrice()).append("】修改为【").append(updateReqVO.getPrice()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getOriginalPrice(), updateReqVO.getOriginalPrice())) {
            fieldChanges.append("【原价】从【").append(oldServicePackage.getOriginalPrice()).append("】修改为【").append(updateReqVO.getOriginalPrice()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getUnit(), updateReqVO.getUnit())) {
            fieldChanges.append("【价格单位】从【").append(oldServicePackage.getUnit()).append("】修改为【").append(updateReqVO.getUnit()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getServiceDuration(), updateReqVO.getServiceDuration())) {
            fieldChanges.append("【服务时长】从【").append(oldServicePackage.getServiceDuration()).append("】修改为【").append(updateReqVO.getServiceDuration()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getPackageType(), updateReqVO.getPackageType())) {
            fieldChanges.append("【套餐类型】从【").append(oldServicePackage.getPackageType()).append("】修改为【").append(updateReqVO.getPackageType()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getTaskSplitRule(), updateReqVO.getTaskSplitRule())) {
            fieldChanges.append("【任务拆分规则】从【").append(oldServicePackage.getTaskSplitRule()).append("】修改为【").append(updateReqVO.getTaskSplitRule()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getServiceDescription(), updateReqVO.getServiceDescription())) {
            fieldChanges.append("【服务描述】从【").append(oldServicePackage.getServiceDescription()).append("】修改为【").append(updateReqVO.getServiceDescription()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getServiceDetails(), updateReqVO.getServiceDetails())) {
            fieldChanges.append("【详细服务内容】从【").append(oldServicePackage.getServiceDetails()).append("】修改为【").append(updateReqVO.getServiceDetails()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getServiceProcess(), updateReqVO.getServiceProcess())) {
            fieldChanges.append("【服务流程】从【").append(oldServicePackage.getServiceProcess()).append("】修改为【").append(updateReqVO.getServiceProcess()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getPurchaseNotice(), updateReqVO.getPurchaseNotice())) {
            fieldChanges.append("【购买须知】从【").append(oldServicePackage.getPurchaseNotice()).append("】修改为【").append(updateReqVO.getPurchaseNotice()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getStatus(), updateReqVO.getStatus())) {
            fieldChanges.append("【状态】从【").append(oldServicePackage.getStatus()).append("】修改为【").append(updateReqVO.getStatus()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getAdvanceBookingDays(), updateReqVO.getAdvanceBookingDays())) {
            fieldChanges.append("【预约时间范围】从【").append(oldServicePackage.getAdvanceBookingDays()).append("】修改为【").append(updateReqVO.getAdvanceBookingDays()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getTimeSelectionMode(), updateReqVO.getTimeSelectionMode())) {
            fieldChanges.append("【时间选择模式】从【").append(oldServicePackage.getTimeSelectionMode()).append("】修改为【").append(updateReqVO.getTimeSelectionMode()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getAppointmentMode(), updateReqVO.getAppointmentMode())) {
            fieldChanges.append("【预约模式】从【").append(oldServicePackage.getAppointmentMode()).append("】修改为【").append(updateReqVO.getAppointmentMode()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getServiceStartTime(), updateReqVO.getServiceStartTime())) {
            fieldChanges.append("【服务开始时间】从【").append(oldServicePackage.getServiceStartTime()).append("】修改为【").append(updateReqVO.getServiceStartTime()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getAddressSetting(), updateReqVO.getAddressSetting())) {
            fieldChanges.append("【地址设置】从【").append(oldServicePackage.getAddressSetting()).append("】修改为【").append(updateReqVO.getAddressSetting()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getMaxBookingDays(), updateReqVO.getMaxBookingDays())) {
            fieldChanges.append("【最大预约天数】从【").append(oldServicePackage.getMaxBookingDays()).append("】修改为【").append(updateReqVO.getMaxBookingDays()).append("】；");
        }
        if (!Objects.equals(oldServicePackage.getCancellationPolicy(), updateReqVO.getCancellationPolicy())) {
            fieldChanges.append("【取消政策】从【").append(oldServicePackage.getCancellationPolicy()).append("】修改为【").append(updateReqVO.getCancellationPolicy()).append("】；");
        }

        // Record operation log context
        LogRecordContext.putVariable("servicePackage", oldServicePackage);
        LogRecordContext.putVariable("updateReqVO", updateReqVO);
        LogRecordContext.putVariable("fieldChanges", fieldChanges.toString());

        // If there are carousel changes, add to log context
        if (carouselChangeLog != null && !carouselChangeLog.isEmpty()) {
            LogRecordContext.putVariable("carouselChangeLog", carouselChangeLog);
        }

        // If there are feature changes, add to log context
        if (featureChangeLog != null && !featureChangeLog.isEmpty()) {
            LogRecordContext.putVariable("featureChangeLog", featureChangeLog);
        }
    }

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_TYPE, subType = SERVICE_PACKAGE_DELETE_SUB_TYPE, bizNo = "{{#id}}", success = SERVICE_PACKAGE_DELETE_SUCCESS)
    public void deleteServicePackage(Long id) {
        // 1. 校验存在
        ServicePackageDO servicePackage = validateServicePackageExists(id);

        // 2. 软删除主表（设置deleted=1）
        servicePackage.setDeleted(true);
        servicePackage.setUpdateTime(new Date());
        servicePackage.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
        servicePackageMapper.updateById(servicePackage);

        // 3. 软删除轮播图
        List<ServicePackageCarouselDO> carousels = servicePackageCarouselMapper.selectList(
                new LambdaQueryWrapperX<ServicePackageCarouselDO>()
                        .eq(ServicePackageCarouselDO::getPackageId, id));
        for (ServicePackageCarouselDO carousel : carousels) {
            carousel.setDeleted(true);
            carousel.setUpdateTime(new Date());
            carousel.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
            servicePackageCarouselMapper.updateById(carousel);
        }

        // 4. 软删除特色标签
        List<ServicePackageFeatureDO> features = servicePackageFeatureMapper.selectList(
                new LambdaQueryWrapperX<ServicePackageFeatureDO>()
                        .eq(ServicePackageFeatureDO::getPackageId, id));
        for (ServicePackageFeatureDO feature : features) {
            feature.setDeleted(true);
            feature.setUpdateTime(new Date());
            feature.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
            servicePackageFeatureMapper.updateById(feature);
        }

        // 记录操作日志上下文
        LogRecordContext.putVariable("servicePackage", servicePackage);
    }

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_TYPE, subType = SERVICE_PACKAGE_MOVE_TO_RECYCLE_SUB_TYPE, bizNo = "{{#id}}", success = SERVICE_PACKAGE_MOVE_TO_RECYCLE_SUCCESS)
    public void moveServicePackageToRecycle(Long id) {
        // 1. 校验存在
        ServicePackageDO servicePackage = validateServicePackageExists(id);

        // 2. 更新状态为deleted（移动到回收站）
        servicePackage.setStatus("deleted");
        servicePackage.setUpdateTime(new Date());
        servicePackage.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
        servicePackageMapper.updateById(servicePackage);

        // 记录操作日志上下文
        LogRecordContext.putVariable("servicePackage", servicePackage);
    }

    @Override
    public ServicePackageRespVO getServicePackage(Long id) {
        // 获取当前登录用户的合作伙伴ID
        Long currentUserPartnerId = SecurityFrameworkUtils.getLoginUserPartnerId();

        // 构建查询条件
        LambdaQueryWrapperX<ServicePackageDO> queryWrapper = new LambdaQueryWrapperX<ServicePackageDO>()
                .eq(ServicePackageDO::getId, id)
                .eq(ServicePackageDO::getDeleted, false);

        // 根据当前用户合作伙伴ID过滤数据
        if (currentUserPartnerId != null) {
            queryWrapper.eq(ServicePackageDO::getPartnerId, currentUserPartnerId);
        }
        
        ServicePackageDO servicePackage = servicePackageMapper.selectOne(queryWrapper);
        if (servicePackage == null) {
            return null;
        }

        ServicePackageRespVO respVO = ServicePackageConvert.INSTANCE.convert(servicePackage);

        // 查询轮播图（过滤已软删除的数据）
        List<ServicePackageCarouselDO> carouselList = servicePackageCarouselMapper.selectList(
                new LambdaQueryWrapperX<ServicePackageCarouselDO>()
                        .eq(ServicePackageCarouselDO::getPackageId, id)
                        .eq(ServicePackageCarouselDO::getDeleted, false)
                        .orderByAsc(ServicePackageCarouselDO::getSortOrder));
        respVO.setCarouselList(ServicePackageCarouselConvert.INSTANCE.convertList(carouselList));

        // 查询特色标签（过滤已软删除的数据）
        List<ServicePackageFeatureDO> featureList = servicePackageFeatureMapper.selectList(
                new LambdaQueryWrapperX<ServicePackageFeatureDO>()
                        .eq(ServicePackageFeatureDO::getPackageId, id)
                        .eq(ServicePackageFeatureDO::getDeleted, false)
                        .orderByAsc(ServicePackageFeatureDO::getSortOrder));
        respVO.setFeatureList(ServicePackageFeatureConvert.INSTANCE.convertList(featureList));

        return respVO;
    }

    @Override
    public PageResult<ServicePackageRespVO> getServicePackagePage(ServicePackagePageReqVO pageReqVO) {
        // 获取当前登录用户的合作伙伴ID
        Long currentUserPartnerId = SecurityFrameworkUtils.getLoginUserPartnerId();

        // 构建查询条件
        LambdaQueryWrapperX<ServicePackageDO> queryWrapper = new LambdaQueryWrapperX<ServicePackageDO>()
                .likeIfPresent(ServicePackageDO::getName, pageReqVO.getKeyword())
                .eqIfPresent(ServicePackageDO::getCategory, pageReqVO.getCategory())
                .eqIfPresent(ServicePackageDO::getStatus, pageReqVO.getStatus())
                .eqIfPresent(ServicePackageDO::getPackageType, pageReqVO.getPackageType())
                .eqIfPresent(ServicePackageDO::getAuditStatus, pageReqVO.getAuditStatus())
                .eqIfPresent(ServicePackageDO::getAgencyId, pageReqVO.getAgencyId())
                .likeIfPresent(ServicePackageDO::getAgencyName, pageReqVO.getAgency())
                .eqIfPresent(ServicePackageDO::getPartnerId, pageReqVO.getPartnerId())
                .likeIfPresent(ServicePackageDO::getPartnerName, pageReqVO.getPartner())
                .eq(ServicePackageDO::getDeleted, false)  // 只查询未删除的数据
                .orderByDesc(ServicePackageDO::getId);

        // 根据当前用户合作伙伴ID过滤数据
        // 如果前端请求参数 partnerId 为空，则使用当前登录用户的所属合作伙伴ID作为默认筛选条件
        Long filterPartnerId = pageReqVO.getPartnerId() != null ? pageReqVO.getPartnerId() : currentUserPartnerId;
        if (filterPartnerId != null) {
            queryWrapper.eq(ServicePackageDO::getPartnerId, filterPartnerId);
        }
        
        // 执行分页查询
        PageResult<ServicePackageDO> pageResult = servicePackageMapper.selectPage(pageReqVO, queryWrapper);
        return ServicePackageConvert.INSTANCE.convertPage(pageResult);
    }

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_TYPE, subType = SERVICE_PACKAGE_STATUS_UPDATE_SUB_TYPE, bizNo = "{{#reqVO.ids}}", success = SERVICE_PACKAGE_STATUS_UPDATE_SUCCESS)
    public void updateServicePackageStatus(ServicePackageStatusUpdateReqVO reqVO) {
        // 批量更新状态
        for (Long id : reqVO.getIds()) {
            ServicePackageDO servicePackage = new ServicePackageDO();
            servicePackage.setId(id);
            servicePackage.setStatus(reqVO.getStatus());
            servicePackage.setUpdateTime(new Date());
            servicePackage.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
            
            // 当下架操作时（从active变为pending），同时更新审核状态为待审核
            if ("pending".equals(reqVO.getStatus())) {
                servicePackage.setAuditStatus("pending");
            }
            
            servicePackageMapper.updateById(servicePackage);
        }

        // 记录操作日志上下文
        LogRecordContext.putVariable("reqVO", reqVO);
    }

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_TYPE, subType = "上架服务套餐", bizNo = "{{#id}}", success = "上架了服务套餐【{{#servicePackage.name}}】")
    public void shelfServicePackage(Long id) {
        // 1. 校验存在
        ServicePackageDO servicePackage = validateServicePackageExists(id);
        
        // 2. 检查当前状态
        if ("active".equals(servicePackage.getStatus())) {
            throw new RuntimeException("服务套餐已经上架");
        }
        
        // 3. 更新状态为待上架，审核状态为审核中
        ServicePackageDO updatePackage = new ServicePackageDO();
        updatePackage.setId(id);
        updatePackage.setStatus("pending");
        updatePackage.setAuditStatus("auditing");
        updatePackage.setUpdateTime(new Date());
        updatePackage.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
        servicePackageMapper.updateById(updatePackage);
        
        // 记录操作日志上下文
        LogRecordContext.putVariable("servicePackage", servicePackage);
    }

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_TYPE, subType = "审核服务套餐", bizNo = "{{#id}}", success = "审核了服务套餐【{{#servicePackage.name}}】，审核结果：{{#auditStatus}}{{#rejectReason != null ? '，拒绝原因：' + #rejectReason : ''}}")
    public void auditServicePackage(Long id, String auditStatus, String rejectReason) {
        // 1. 校验存在
        ServicePackageDO servicePackage = validateServicePackageExists(id);
        
        // 2. 校验审核状态
        if (!"auditing".equals(servicePackage.getAuditStatus())) {
            throw new RuntimeException("服务套餐不在审核中状态");
        }
        
        // 3. 校验审核结果
        if (!"approved".equals(auditStatus) && !"rejected".equals(auditStatus)) {
            throw new RuntimeException("审核状态只能是approved或rejected");
        }
        
        // 4. 校验拒绝原因
        if ("rejected".equals(auditStatus) && (rejectReason == null || rejectReason.trim().isEmpty())) {
            throw new RuntimeException("审核拒绝时必须填写拒绝原因");
        }
        
        // 5. 更新审核状态和套餐状态
        ServicePackageDO updatePackage = new ServicePackageDO();
        updatePackage.setId(id);
        updatePackage.setAuditStatus(auditStatus);
        
        if ("approved".equals(auditStatus)) {
            // 审核通过，状态变为上架
            updatePackage.setStatus("active");
            updatePackage.setRejectReason(null); // 清空拒绝原因
        } else {
            // 审核拒绝，状态仍为待上架
            updatePackage.setStatus("pending");
            updatePackage.setRejectReason(rejectReason);
        }
        
        updatePackage.setUpdateTime(new Date());
        updatePackage.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
        servicePackageMapper.updateById(updatePackage);
        
        // 记录操作日志上下文
        LogRecordContext.putVariable("servicePackage", servicePackage);
        LogRecordContext.putVariable("auditStatus", auditStatus);
        LogRecordContext.putVariable("rejectReason", rejectReason);
    }

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_TYPE, subType = "撤回服务套餐", bizNo = "{{#id}}", success = "撤回了服务套餐【{{#servicePackage.name}}】")
    public void withdrawServicePackage(Long id) {
        // 1. 校验存在
        ServicePackageDO servicePackage = validateServicePackageExists(id);
        
        // 2. 检查当前状态
        if (!"pending".equals(servicePackage.getStatus()) || !"auditing".equals(servicePackage.getAuditStatus())) {
            throw new RuntimeException("只有待上架且审核中的服务套餐才能撤回");
        }
        
        // 3. 更新状态为待审核
        ServicePackageDO updatePackage = new ServicePackageDO();
        updatePackage.setId(id);
        updatePackage.setStatus("pending");
        updatePackage.setAuditStatus("pending");
        updatePackage.setRejectReason(null); // 清空拒绝原因
        updatePackage.setUpdateTime(new Date());
        updatePackage.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
        servicePackageMapper.updateById(updatePackage);
        
        // 记录操作日志上下文
        LogRecordContext.putVariable("servicePackage", servicePackage);
    }

    private ServicePackageDO validateServicePackageExists(Long id) {
        // 获取当前登录用户的合作伙伴ID
        Long currentUserPartnerId = SecurityFrameworkUtils.getLoginUserPartnerId();

        // 构建查询条件
        LambdaQueryWrapperX<ServicePackageDO> queryWrapper = new LambdaQueryWrapperX<ServicePackageDO>()
                .eq(ServicePackageDO::getId, id)
                .eq(ServicePackageDO::getDeleted, false);

        // 根据当前用户合作伙伴ID过滤数据
        if (currentUserPartnerId != null) {
            queryWrapper.eq(ServicePackageDO::getPartnerId, currentUserPartnerId);
        }
        
        ServicePackageDO servicePackage = servicePackageMapper.selectOne(queryWrapper);
        if (servicePackage == null) {
            throw new RuntimeException("服务套餐不存在");
        }
        return servicePackage;
    }

    private String updateServicePackageCarousels(Long packageId, List<ServicePackageCarouselSaveReqVO> carouselList) {
        if (carouselList == null) {
            return null;
        }

        StringBuilder changeLog = new StringBuilder();
        List<ServicePackageCarouselDO> existingCarousels = servicePackageCarouselMapper.selectList(
                new LambdaQueryWrapperX<ServicePackageCarouselDO>()
                        .eq(ServicePackageCarouselDO::getPackageId, packageId)
                        .eq(ServicePackageCarouselDO::getDeleted, false));

        // 处理轮播图更新
        for (ServicePackageCarouselSaveReqVO carouselVO : carouselList) {
            if (carouselVO.getId() != null) {
                // 更新现有轮播图
                ServicePackageCarouselDO carousel = ServicePackageCarouselConvert.INSTANCE.convert(carouselVO);
                carousel.setUpdateTime(new Date());
                carousel.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
                servicePackageCarouselMapper.updateById(carousel);
                changeLog.append("更新了轮播图【").append(carouselVO.getImageUrl()).append("】；");
            } else {
                // 新增轮播图
                ServicePackageCarouselDO carousel = ServicePackageCarouselConvert.INSTANCE.convert(carouselVO);
                carousel.setPackageId(packageId);
                carousel.setCreateTime(new Date());
                carousel.setDeleted(false);
                carousel.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                carousel.setTenantId(SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId() : 1L);
                servicePackageCarouselMapper.insert(carousel);
                changeLog.append("新增了轮播图【").append(carouselVO.getImageUrl()).append("】；");
            }
        }

        // 删除不存在的轮播图
        for (ServicePackageCarouselDO existingCarousel : existingCarousels) {
            boolean found = carouselList.stream()
                    .anyMatch(carouselVO -> Objects.equals(carouselVO.getId(), existingCarousel.getId()));
            if (!found) {
                servicePackageCarouselMapper.deleteById(existingCarousel.getId());
                changeLog.append("删除了轮播图【").append(existingCarousel.getImageUrl()).append("】；");
            }
        }

        return changeLog.toString();
    }

    private String updateServicePackageFeatures(Long packageId, List<ServicePackageFeatureSaveReqVO> featureList) {
        if (featureList == null) {
            return null;
        }

        StringBuilder changeLog = new StringBuilder();
        List<ServicePackageFeatureDO> existingFeatures = servicePackageFeatureMapper.selectList(
                new LambdaQueryWrapperX<ServicePackageFeatureDO>()
                        .eq(ServicePackageFeatureDO::getPackageId, packageId)
                        .eq(ServicePackageFeatureDO::getDeleted, false));

        // 处理特色标签更新
        for (ServicePackageFeatureSaveReqVO featureVO : featureList) {
            if (featureVO.getId() != null) {
                // 更新现有特色标签
                ServicePackageFeatureDO feature = ServicePackageFeatureConvert.INSTANCE.convert(featureVO);
                feature.setUpdateTime(new Date());
                feature.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
                servicePackageFeatureMapper.updateById(feature);
                changeLog.append("更新了特色标签【").append(featureVO.getFeatureName()).append("】；");
            } else {
                // 新增特色标签
                ServicePackageFeatureDO feature = ServicePackageFeatureConvert.INSTANCE.convert(featureVO);
                feature.setPackageId(packageId);
                feature.setCreateTime(new Date());
                feature.setDeleted(false);
                feature.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                feature.setTenantId(SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId() : 1L);
                servicePackageFeatureMapper.insert(feature);
                changeLog.append("新增了特色标签【").append(featureVO.getFeatureName()).append("】；");
            }
        }

        // 删除不存在的特色标签
        for (ServicePackageFeatureDO existingFeature : existingFeatures) {
            boolean found = featureList.stream()
                    .anyMatch(featureVO -> Objects.equals(featureVO.getId(), existingFeature.getId()));
            if (!found) {
                servicePackageFeatureMapper.deleteById(existingFeature.getId());
                changeLog.append("删除了特色标签【").append(existingFeature.getFeatureName()).append("】；");
            }
        }

        return changeLog.toString();
    }
} 