package cn.bztmaster.cnt.module.publicbiz.controller.admin.question;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * 资源中心 - 考题分类管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/publicbiz/question/category")
@Tag(name = "资源中心 - 考题分类管理")
public class QuestionCategoryController {

    @Resource
    private QuestionCategoryService questionCategoryService;

    @GetMapping("/page")
    @Operation(summary = "分类分页查询", description = "分页查询考题分类列表")
    public CommonResult<PageResult<QuestionCategoryRespVO>> pageQuestionCategory(@Valid QuestionCategoryPageReqVO reqVO) {
        PageResult<QuestionCategoryRespVO> pageResult = questionCategoryService.pageQuestionCategory(reqVO);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/list")
    @Operation(summary = "分类列表查询", description = "查询分类列表（不分页），用于下拉选择")
    public CommonResult<List<QuestionCategoryRespVO>> listQuestionCategory(
            @Parameter(description = "业务模块筛选") @RequestParam(value = "biz", required = false) String biz,
            @Parameter(description = "分类层级筛选，支持多个层级") @RequestParam(value = "level", required = false) List<Integer> level,
            @Parameter(description = "父级分类ID") @RequestParam(value = "parentId", required = false) Long parentId) {
        List<QuestionCategoryRespVO> list = questionCategoryService.listQuestionCategory(biz, level, parentId);
        return CommonResult.success(list);
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "分类详情查询", description = "根据ID查询分类详细信息")
    @Parameter(name = "id", description = "分类ID", required = true, example = "1")
    public CommonResult<QuestionCategoryRespVO> getQuestionCategory(@PathVariable("id") Long id) {
        QuestionCategoryRespVO category = questionCategoryService.getQuestionCategory(id);
        return CommonResult.success(category);
    }

    @PostMapping("/add")
    @Operation(summary = "新增分类", description = "新增考题分类")
    public CommonResult<Long> createQuestionCategory(@Valid @RequestBody QuestionCategorySaveReqVO reqVO) {
        Long categoryId = questionCategoryService.createQuestionCategory(reqVO);
        return CommonResult.success(categoryId);
    }

    @PutMapping("/update")
    @Operation(summary = "编辑分类", description = "编辑考题分类")
    public CommonResult<Boolean> updateQuestionCategory(@Valid @RequestBody QuestionCategorySaveReqVO reqVO) {
        questionCategoryService.updateQuestionCategory(reqVO);
        return CommonResult.success(true);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除分类", description = "删除考题分类（软删除）")
    @Parameter(name = "id", description = "分类ID", required = true, example = "1")
    public CommonResult<Boolean> deleteQuestionCategory(@PathVariable("id") Long id) {
        questionCategoryService.deleteQuestionCategory(id);
        return CommonResult.success(true);
    }

}
