package cn.bztmaster.cnt.module.publicbiz.controller.admin.business;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.business.BusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/publicbiz/business")
@Tag(name = "商机中心-商机管理")
public class BusinessController {

    @Resource
    private BusinessService businessService;

    @GetMapping("/list")
    @Operation(summary = "商机分页列表")
    public CommonResult<PageResult<BusinessRespVO>> list(BusinessPageReqVO reqVO) {
        return CommonResult.success(businessService.getBusinessPage(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "新增商机")
    public CommonResult<Long> create(@RequestBody BusinessSaveReqVO reqVO) {
        return CommonResult.success(businessService.createBusiness(reqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "编辑商机")
    public CommonResult<Boolean> update(@RequestBody BusinessSaveReqVO reqVO) {
        businessService.updateBusiness(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "商机详情")
    public CommonResult<BusinessDetailRespVO> detail(@RequestParam("id") Long id) {
        return CommonResult.success(businessService.getBusinessDetail(id));
    }

    @PostMapping("/delete")
    @Operation(summary = "逻辑删除商机")
    public CommonResult<Boolean> delete(@RequestParam("id")  Long id) {
        businessService.deleteBusiness(id);
        return CommonResult.success(true);
    }
} 