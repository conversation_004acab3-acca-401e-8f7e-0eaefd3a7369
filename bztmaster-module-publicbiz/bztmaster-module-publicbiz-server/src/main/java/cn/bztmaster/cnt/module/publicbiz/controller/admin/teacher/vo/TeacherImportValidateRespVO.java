package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "师资库 - 批量导入校验响应 VO")
public class TeacherImportValidateRespVO {
    
    @Schema(description = "校验结果列表")
    private List<TeacherImportValidateItemVO> validateList;
    
    @Schema(description = "总记录数")
    private Integer totalCount;
    
    @Schema(description = "有效记录数")
    private Integer validCount;
    
    @Schema(description = "错误记录数")
    private Integer errorCount;
} 