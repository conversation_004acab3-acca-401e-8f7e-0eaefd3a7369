<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.teacher.TeacherMapper">
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.teacher.TeacherDO">
        <id property="id" column="id" />
        <result property="avatar" column="avatar" />
        <result property="name" column="name" />
        <result property="description" column="description" />
        <result property="type" column="type" />
        <result property="biz" column="biz" />
        <result property="orgId" column="org_id" />
        <result property="org" column="org" />
        <result property="field" column="field" />
        <result property="phone" column="phone" />
        <result property="status" column="status" />
        <result property="signStatus" column="sign_status" />
        <result property="signDate" column="sign_date" />
        <result property="contractType" column="contract_type" />
        <result property="contractTemplate" column="contract_template" />
        <result property="contractNo" column="contract_no" />
        <result property="contractName" column="contract_name" />
        <result property="contractPeriodStart" column="contract_period_start" />
        <result property="contractPeriodEnd" column="contract_period_end" />
        <result property="contractAmount" column="contract_amount" />
        <result property="contractFileName" column="contract_file_name" />
        <result property="contractFileUrl" column="contract_file_url" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creator" column="creator" />
        <result property="updater" column="updater" />
        <result property="deleted" column="deleted" />
        <result property="tenantId" column="tenant_id" />
    </resultMap>
    <!-- 可扩展自定义SQL -->
</mapper> 