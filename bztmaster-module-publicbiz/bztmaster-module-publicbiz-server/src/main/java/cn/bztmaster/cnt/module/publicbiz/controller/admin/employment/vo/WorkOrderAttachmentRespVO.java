package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工单附件响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单附件响应 VO")
@Data
public class WorkOrderAttachmentRespVO {

    @Schema(description = "主键", example = "1")
    private Long id;

    @Schema(description = "工单编号", example = "WO202312010001")
    private String workOrderNo;

    @Schema(description = "文件名", example = "投诉照片.jpg")
    private String fileName;

    @Schema(description = "文件URL", example = "https://example.com/file.jpg")
    private String fileUrl;

    @Schema(description = "文件大小(字节)", example = "1024000")
    private Long fileSize;

    @Schema(description = "文件分类", example = "evidence")
    private String fileCategory;

    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "上传人ID", example = "123")
    private Long uploaderId;

    @Schema(description = "上传人姓名", example = "张三")
    private String uploaderName;

    @Schema(description = "文件类型", example = "jpg")
    private String fileType;

    @Schema(description = "上传目的", example = "工单附件")
    private String uploadPurpose;
} 