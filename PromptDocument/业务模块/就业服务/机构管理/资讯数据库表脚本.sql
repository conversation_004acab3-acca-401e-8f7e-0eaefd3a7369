资讯主表：publicbiz_news

CREATE TABLE `publicbiz_news` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `news_title` varchar(200) NOT NULL COMMENT '资讯标题',
  `news_summary` text COMMENT '资讯摘要',
  `news_content` longtext COMMENT '资讯内容',
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类ID',
  `category_name` varchar(100) DEFAULT NULL COMMENT '分类名称',
  `cover_image_url` varchar(500) DEFAULT NULL COMMENT '封面图片URL',
  `material_id` bigint(20) DEFAULT NULL COMMENT '关联素材文章ID',
  `author` varchar(50) DEFAULT NULL COMMENT '作者',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `status` varchar(20) NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿/published-已发布/offline-已下架',
  `view_count` int(11) DEFAULT '0' COMMENT '浏览次数',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞次数',
  `share_count` int(11) DEFAULT '0' COMMENT '分享次数',
  `comment_count` bigint(20) DEFAULT '0' COMMENT '评论数',
  `sort` int(11) DEFAULT '0' COMMENT '排序，数字越小越靠前',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_news_title` (`news_title`),
  KEY `idx_author` (`author`),
  KEY `idx_status` (`status`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='资讯主表';

