# 登录授权后端接口文档

## 接口概述

本文档描述了小程序启动时获取用户角色的接口规范，用于根据用户身份动态跳转到不同的首页界面。

## 1. 获取用户角色接口

### 接口信息
- **接口名称**: 获取用户角色
- **接口路径**: `/publicbiz/employer/getUserRole`
- **请求方法**: `POST`
- **接口描述**: 根据微信登录code获取用户角色信息，用于小程序启动时的身份判断

### 请求参数

#### 请求头 (Headers)
```
Content-Type: application/json
```

#### 请求体 (Body)
```json
{
  "code": "微信登录临时code"
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | string | 是 | 微信登录临时code，通过uni.login()获取 |

### 响应格式

#### 成功响应
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "role": "employer",
    "userInfo": {
      "id": "user_123456",
      "openid": "wx_openid_123456",
      "nickname": "用户昵称",
      "avatar": "https://example.com/avatar.jpg",
      "mobile": "13800138000",
      "role": "employer",
      "status": "active",
      "createTime": "2024-01-01 12:00:00",
      "updateTime": "2024-01-01 12:00:00"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_123456"
  }
}
```

#### 失败响应
```json
{
  "code": 1001,
  "msg": "参数错误",
  "data": null
}
```

### 响应参数说明

#### 顶层参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应码，0表示成功，非0表示失败 |
| msg | string | 响应消息 |
| data | object | 响应数据，失败时为null |

#### data参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| role | string | 用户角色，枚举值：employer(雇主)、aunt(阿姨)、agency(机构) |
| userInfo | object | 用户基本信息 |
| token | string | 访问令牌，用于后续接口认证 |
| refreshToken | string | 刷新令牌，用于刷新访问令牌 |

#### userInfo参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | string | 用户ID |
| openid | string | 微信openid |
| nickname | string | 用户昵称 |
| avatar | string | 用户头像URL |
| mobile | string | 手机号码 |
| role | string | 用户角色 |
| status | string | 用户状态：active(正常)、inactive(禁用) |
| createTime | string | 创建时间，格式：yyyy-MM-dd HH:mm:ss |
| updateTime | string | 更新时间，格式：yyyy-MM-dd HH:mm:ss |

### 角色枚举说明

| 角色值 | 角色名称 | 说明 | 跳转页面 |
|--------|----------|------|----------|
| employer | 雇主 | 普通用户，可以发布服务需求 | /pages/index/index |
| aunt | 阿姨 | 服务提供者，提供家政服务 | /pages/aunt-workbench/index |
| agency | 机构 | 服务机构，管理阿姨和订单 | /pages/agency-workbench/index |

### 响应码说明

| 响应码 | 说明 | 处理建议 |
|--------|------|----------|
| 0 | 成功 | 正常处理 |
| 1001 | 参数错误 | 检查code参数是否正确 |
| 1002 | 微信登录失败 | 重新获取code |
| 1003 | 用户不存在 | 引导用户注册 |
| 1004 | 用户被禁用 | 提示用户联系客服 |
| 1005 | 角色权限不足 | 提示用户升级权限 |
| 5000 | 服务器内部错误 | 稍后重试 |

## 2. 切换用户身份接口

### 接口信息
- **接口名称**: 切换用户身份
- **接口路径**: `/publicbiz/employer/mpuser/switch-identity`
- **请求方法**: `POST`
- **接口描述**: 小程序端切换身份时根据openid更新用户最后登录的身份类型

### 请求参数

#### 请求头 (Headers)
```
Content-Type: application/json
```

#### 请求体 (Body)
```json
{
  "openid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "identityType": "EMPLOYER"
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| openid | string | 是 | 微信openid，用户的唯一标识 |
| identityType | string | 是 | 身份类型，枚举值：EMPLOYER(雇主)、AUNT(阿姨)、AGENCY(机构) |

### 响应格式

#### 成功响应
```json
{
  "code": 0,
  "msg": "success",
  "data": 1024
}
```

#### 失败响应
```json
{
  "code": 5000,
  "msg": "服务器内部错误",
  "data": null
}
```

### 响应参数说明

#### 顶层参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应码，0表示成功，非0表示失败 |
| msg | string | 响应消息 |
| data | long | 响应数据，成功时返回记录ID，失败时为null |

### 身份类型枚举说明

| 身份类型值 | 身份名称 | 说明 |
|------------|----------|------|
| EMPLOYER | 雇主 | 普通用户，可以发布服务需求 |
| AUNT | 阿姨 | 服务提供者，提供家政服务 |
| AGENCY | 机构 | 服务机构，管理阿姨和订单 |

### 响应码说明

| 响应码 | 说明 | 处理建议 |
|--------|------|----------|
| 0 | 成功 | 正常处理，身份切换成功 |
| 5000 | 服务器内部错误 | 稍后重试 |

### 业务逻辑说明

1. **身份切换流程**
   - 根据openid查询用户现有记录
   - 如果记录存在，更新身份类型和最后登录时间
   - 如果记录不存在，创建新记录
   - 返回记录ID

2. **使用场景**
   - 小程序内切换身份时调用
   - 用户重新登录时更新身份信息
   - 多身份用户切换角色时使用

3. **注意事项**
   - 接口支持匿名访问（@PermitAll）
   - 自动更新最后登录时间
   - 如果用户不存在会自动创建记录

