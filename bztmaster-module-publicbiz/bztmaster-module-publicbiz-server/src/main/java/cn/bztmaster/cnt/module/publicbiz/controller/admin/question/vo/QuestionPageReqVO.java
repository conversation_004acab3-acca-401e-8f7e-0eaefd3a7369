package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考题分页查询 Request VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "资源中心 - 考题分页查询 Request VO")
public class QuestionPageReqVO extends PageParam {

    /**
     * 一级分类名称
     */
    @Schema(description = "一级分类名称", example = "职业技能等级认定")
    private String level1Name;

    /**
     * 二级分类名称
     */
    @Schema(description = "二级分类名称", example = "家政服务类")
    private String level2Name;

    /**
     * 三级分类名称
     */
    @Schema(description = "三级分类名称", example = "家政服务员")
    private String level3Name;

    /**
     * 题型
     */
    @Schema(description = "题型", example = "单选题")
    private String type;

    /**
     * 业务模块
     */
    @Schema(description = "业务模块", example = "家政业务")
    private String biz;

    /**
     * 业务模块名称
     */
    @Schema(description = "业务模块名称", example = "家政服务业务")
    private String bizName;

    /**
     * 题干内容关键词搜索
     */
    @Schema(description = "题干内容关键词搜索", example = "职业道德")
    private String keyword;

}
