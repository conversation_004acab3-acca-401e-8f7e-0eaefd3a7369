// 测试代码 - 验证ServicePackage相关类的字段更新
// 这个文件用于验证新增的partnerId和partnerName字段是否正确添加到所有相关类中

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

public class TestServicePackageFields {
    
    public static void main(String[] args) {
        System.out.println("=== 验证ServicePackage相关类的字段更新 ===");
        
        // 验证DO类
        verifyFields("ServicePackageDO", 
            "cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO",
            Arrays.asList("partnerId", "partnerName"));
        
        // 验证SaveReqVO类
        verifyFields("ServicePackageSaveReqVO", 
            "cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackageSaveReqVO",
            Arrays.asList("partnerId", "partnerName"));
        
        // 验证UpdateReqVO类
        verifyFields("ServicePackageUpdateReqVO", 
            "cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackageUpdateReqVO",
            Arrays.asList("partnerId", "partnerName"));
        
        // 验证RespVO类（管理端）
        verifyFields("ServicePackageRespVO (Admin)", 
            "cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackageRespVO",
            Arrays.asList("partnerId", "partnerName"));
        
        // 验证RespVO类（雇主端）
        verifyFields("ServicePackageRespVO (Employer)", 
            "cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageRespVO",
            Arrays.asList("partnerId", "partnerName"));
        
        // 验证PageReqVO类
        verifyFields("ServicePackagePageReqVO", 
            "cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackagePageReqVO",
            Arrays.asList("partnerId", "partner", "agency"));
    }
    
    private static void verifyFields(String className, String fullClassName, List<String> expectedFields) {
        System.out.println("\n--- 验证 " + className + " ---");
        try {
            Class<?> clazz = Class.forName(fullClassName);
            Field[] fields = clazz.getDeclaredFields();
            
            for (String expectedField : expectedFields) {
                boolean found = Arrays.stream(fields)
                    .anyMatch(field -> field.getName().equals(expectedField));
                
                if (found) {
                    System.out.println("✓ " + expectedField + " 字段存在");
                } else {
                    System.out.println("✗ " + expectedField + " 字段缺失");
                }
            }
        } catch (ClassNotFoundException e) {
            System.out.println("✗ 类 " + fullClassName + " 未找到");
        }
    }
}

/*
预期输出：
=== 验证ServicePackage相关类的字段更新 ===

--- 验证 ServicePackageDO ---
✓ partnerId 字段存在
✓ partnerName 字段存在

--- 验证 ServicePackageSaveReqVO ---
✓ partnerId 字段存在
✓ partnerName 字段存在

--- 验证 ServicePackageUpdateReqVO ---
✓ partnerId 字段存在
✓ partnerName 字段存在

--- 验证 ServicePackageRespVO (Admin) ---
✓ partnerId 字段存在
✓ partnerName 字段存在

--- 验证 ServicePackageRespVO (Employer) ---
✓ partnerId 字段存在
✓ partnerName 字段存在

--- 验证 ServicePackagePageReqVO ---
✓ partnerId 字段存在
✓ partner 字段存在
✓ agency 字段存在
*/
