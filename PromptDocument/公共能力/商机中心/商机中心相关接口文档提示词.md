# 商机中心接口文档提示词

## 1. 商机列表查询

- **接口地址**：`GET /api/business/list`
- **功能**：分页查询商机列表，支持多条件筛选。
- **请求参数**（Query）：

  | 参数名           | 类型    | 必填 | 说明           |
  |------------------|---------|------|----------------|
  | pageNo           | int     | 是   | 页码           |
  | pageSize         | int     | 是   | 每页条数       |
  | name             | string  | 否   | 商机名称（模糊）|
  | businessType     | string  | 否   | 业务模块       |
  | businessStage    | string  | 否   | 销售阶段       |
  | ownerUserId      | bigint  | 否   | 销售负责人ID   |

- **返回参数**（JSON）：

  ```json
  {
    "total": 100,
    "list": [
      {
        "id": 1,
        "name": "XX大学2024年春季实践项目",
        "customerId": 1001,
        "customerName": "XX大学",
        "businessType": "高校业务",
        "totalPrice": 500000,
        "businessStage": "方案报价",
        "ownerUserId": 10,
        "ownerUserName": "张三",
        "createTime": "2024-05-10 10:00:00"
      }
    ]
  }
  ```

---

## 2. 新建商机

- **接口地址**：`POST /api/business/create`
- **功能**：新建商机
- **请求参数**（Body，JSON）：

  | 字段名         | 类型         | 必填 | 说明         |
  |----------------|-------------|------|--------------|
  | name           | string      | 是   | 商机名称     |
  | customerId     | bigint      | 是   | 关联客户ID   |
  | customerName   | string      | 是   | 关联客户名称 |
  | businessType   | string      | 是   | 业务模块     |
  | totalPrice     | decimal     | 否   | 商机金额     |
  | businessStage  | string      | 是   | 销售阶段     |
  | ownerUserId    | bigint      | 是   | 销售负责人ID |
  | ownerUserName  | string      | 是   | 销售负责人姓名 |

- **返回参数**：

  ```json
  {
    "success": true,
    "id": 123
  }
  ```

---

## 3. 编辑商机

- **接口地址**：`POST /api/business/update`
- **功能**：编辑商机
- **请求参数**（Body，JSON）：

  | 字段名         | 类型         | 必填 | 说明         |
  |----------------|-------------|------|--------------|
  | id             | bigint      | 是   | 商机ID       |
  | name           | string      | 是   | 商机名称     |
  | customerId     | bigint      | 是   | 关联客户ID   |
  | customerName   | string      | 是   | 关联客户名称 |
  | businessType   | string      | 是   | 业务模块     |
  | totalPrice     | decimal     | 否   | 商机金额     |
  | businessStage  | string      | 是   | 销售阶段     |
  | ownerUserId    | bigint      | 是   | 销售负责人ID |
  | ownerUserName  | string      | 是   | 销售负责人姓名 |

- **返回参数**：

  ```json
  {
    "success": true
  }
  ```

---

## 4. 商机详情

- **接口地址**：`GET /api/business/detail`
- **功能**：获取商机详情
- **请求参数**（Query）：

  | 字段名 | 类型   | 必填 | 说明   |
  |--------|--------|------|--------|
  | id     | bigint | 是   | 商机ID |

- **返回参数**：

  ```json
  {
    "id": 1,
    "name": "XX大学2024年春季实践项目",
    "customerId": 1001,
    "customerName": "XX大学",
    "businessType": "高校业务",
    "totalPrice": 500000,
    "businessStage": "方案报价",
    "ownerUserId": 10,
    "ownerUserName": "张三",
    "createTime": "2024-05-10 10:00:00"
  }
  ```

---

## 5. 跟进商机

- **接口地址**：`POST /api/business/followup/create`
- **功能**：添加商机跟进记录
- **请求参数**（Body，JSON）：

  | 字段名         | 类型     | 必填 | 说明         |
  |----------------|----------|------|--------------|
  | businessId     | bigint   | 是   | 商机ID       |
  | content        | string   | 是   | 跟进内容     |
  | followTime     | datetime | 是   | 跟进时间     |
  | followUserId   | bigint   | 是   | 跟进人ID     |
  | followUserName | string   | 是   | 跟进人姓名   |

- **返回参数**：

  ```json
  {
    "success": true,
    "id": 456
  }
  ```

---

## 6. 商机跟进记录列表

- **接口地址**：`GET /api/business/followup/list`
- **功能**：分页查询商机跟进记录
- **请求参数**（Query）：

  | 字段名     | 类型   | 必填 | 说明   |
  |------------|--------|------|--------|
  | businessId | bigint | 是   | 商机ID |
  | pageNo     | int    | 是   | 页码   |
  | pageSize   | int    | 是   | 每页条数 |

- **返回参数**：

  ```json
  {
    "total": 10,
    "list": [
      {
        "id": 1,
        "businessId": 1,
        "content": "已与客户初步沟通",
        "followTime": "2024-05-12 09:00:00",
        "followUserId": 10,
        "followUserName": "张三"
      }
    ]
  }
  ```

---

## 7. 删除商机（逻辑删除）

- **接口地址**：`POST /api/business/delete`
- **功能**：逻辑删除商机（将deleted字段设为1）
- **请求参数**（Body，JSON）：

  | 字段名 | 类型   | 必填 | 说明   |
  |--------|--------|------|--------|
  | id     | bigint | 是   | 商机ID |

- **返回参数**：

  ```json
  {
    "success": true
  }
  ```

---

## 8. 其他说明

- 所有接口需带上租户ID（tenant_id）参数，支持多租户。
- 所有时间字段建议统一为 `yyyy-MM-dd HH:mm:ss` 格式。
- 所有分页接口返回 `total` 和 `list`。
- 所有接口返回结构建议统一为 `{ code, data, msg }` 格式，便于前后端协作。
- 对于新增、编辑、删除、跟进等操作都要生成对应的操作日志到 `publicbiz_business_log`表中。
- 可根据实际业务扩展更多接口，如导出、批量操作、操作日志等。

---

