# employer 模块 API 文件创建完成说明

## 概述

已按照项目规范在 `bztmaster-module-publicbiz-api` 模块下为 `employer` 包创建了完整的 API 接口和 DTO 文件，主要围绕 `mp_user_last_login_identity` 表相关功能。

## 创建的文件列表

### 1. DTO 文件 (dto 包)

#### 1.1 小程序用户最后登录身份记录 DTO
- **文件路径**: `bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/employer/dto/MpUserLastLoginIdentityDTO.java`
- **功能**: 小程序用户最后登录身份记录的响应 DTO，包含所有字段和枚举描述

#### 1.2 小程序用户最后登录身份记录请求 DTO
- **文件路径**: `bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/employer/dto/MpUserLastLoginIdentityReqDTO.java`
- **功能**: 小程序用户最后登录身份记录的请求 DTO，包含参数校验注解

#### 1.3 雇主工作台信息 DTO
- **文件路径**: `bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/employer/dto/EmployerWorkbenchInfoDTO.java`
- **功能**: 雇主工作台信息的响应 DTO，包含工作台相关统计数据

#### 1.4 雇主订单列表 DTO
- **文件路径**: `bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/employer/dto/EmployerOrderListDTO.java`
- **功能**: 雇主订单列表的响应 DTO，包含订单基本信息

### 2. API 接口文件

#### 2.1 小程序用户最后登录身份记录 API
- **文件路径**: `bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/employer/MpUserLastLoginIdentityApi.java`
- **功能**: 小程序用户最后登录身份记录的 API 接口定义

#### 2.2 雇主工作台 API
- **文件路径**: `bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/employer/EmployerWorkbenchApi.java`
- **功能**: 雇主工作台的 API 接口定义

## API 接口详情

### MpUserLastLoginIdentityApi 接口方法

1. **createMpUserLastLoginIdentity** - 创建小程序用户最后登录身份记录
2. **updateMpUserLastLoginIdentity** - 更新小程序用户最后登录身份记录
3. **deleteMpUserLastLoginIdentity** - 删除小程序用户最后登录身份记录
4. **getMpUserLastLoginIdentity** - 根据ID获得小程序用户最后登录身份记录
5. **getMpUserLastLoginIdentityByUserIdAndOpenid** - 根据用户ID和openid获得记录
6. **getMpUserLastLoginIdentityByOpenid** - 根据openid获得记录
7. **getMpUserLastLoginIdentityByUserId** - 根据用户ID获得记录
8. **saveOrUpdateMpUserLastLoginIdentity** - 保存或更新记录

### EmployerWorkbenchApi 接口方法

1. **getWorkbenchInfo** - 根据雇主ID获得工作台信息
2. **getWorkbenchInfoByUserId** - 根据用户ID获得工作台信息
3. **getWorkbenchInfoByOpenid** - 根据openid获得工作台信息

## DTO 字段说明

### MpUserLastLoginIdentityDTO 主要字段
- `id` - 主键ID
- `userId` - 用户ID
- `openid` - 微信openid
- `unionid` - 微信unionid
- `identityType` - 身份类型
- `identityTypeDesc` - 身份类型描述
- `identityId` - 身份关联ID
- `identityName` - 身份名称
- `lastLoginTime` - 最后登录时间
- `lastLoginIp` - 最后登录IP
- `deviceInfo` - 设备信息
- `loginStatus` - 登录状态
- `loginStatusDesc` - 登录状态描述
- `sessionKey` - 微信session_key
- `accessToken` - 访问令牌
- `refreshToken` - 刷新令牌
- `tokenExpireTime` - 令牌过期时间

### MpUserLastLoginIdentityReqDTO 主要字段
- `userId` - 用户ID (必填)
- `openid` - 微信openid (必填)
- `unionid` - 微信unionid
- `identityType` - 身份类型 (必填)
- `identityId` - 身份关联ID
- `identityName` - 身份名称
- `lastLoginTime` - 最后登录时间 (必填)
- `lastLoginIp` - 最后登录IP
- `deviceInfo` - 设备信息
- `loginStatus` - 登录状态 (必填)
- `sessionKey` - 微信session_key
- `accessToken` - 访问令牌
- `refreshToken` - 刷新令牌
- `tokenExpireTime` - 令牌过期时间

### EmployerWorkbenchInfoDTO 主要字段
- `employerId` - 雇主ID
- `employerName` - 雇主姓名
- `avatar` - 雇主头像
- `phone` - 雇主电话
- `currentIdentityType` - 当前身份类型
- `currentIdentityTypeDesc` - 当前身份类型描述
- `lastLoginTime` - 最后登录时间
- `lastLoginIp` - 最后登录IP
- `deviceInfo` - 设备信息
- `loginStatus` - 登录状态
- `loginStatusDesc` - 登录状态描述
- `pendingOrderCount` - 待处理订单数量
- `processingOrderCount` - 进行中订单数量
- `completedOrderCount` - 已完成订单数量
- `totalOrderCount` - 总订单数量
- `totalAmount` - 总消费金额
- `monthlyAmount` - 本月消费金额
- `availableBalance` - 可用余额
- `pointsBalance` - 积分余额
- `couponCount` - 优惠券数量
- `messageCount` - 消息数量
- `notificationCount` - 系统通知数量
- `isVerified` - 是否实名认证
- `isBankCardBound` - 是否绑定银行卡
- `isPaymentPasswordSet` - 是否设置支付密码

## 使用示例

### 创建小程序用户最后登录身份记录
```java
MpUserLastLoginIdentityReqDTO reqDTO = new MpUserLastLoginIdentityReqDTO();
reqDTO.setUserId(1024L);
reqDTO.setOpenid("oH_Tu5EtrrF6n7u8v9w0x1y2z3");
reqDTO.setIdentityType("EMPLOYER");
reqDTO.setIdentityName("张先生");
reqDTO.setLastLoginTime(LocalDateTime.now());
reqDTO.setLoginStatus(1);

CommonResult<Long> result = mpUserLastLoginIdentityApi.createMpUserLastLoginIdentity(reqDTO);
```

### 查询小程序用户最后登录身份记录
```java
CommonResult<MpUserLastLoginIdentityDTO> result = 
    mpUserLastLoginIdentityApi.getMpUserLastLoginIdentityByOpenid("oH_Tu5EtrrF6n7u8v9w0x1y2z3");
```

### 获取雇主工作台信息
```java
CommonResult<EmployerWorkbenchInfoDTO> result = 
    employerWorkbenchApi.getWorkbenchInfo(1024L);
```

## 注意事项

1. 所有 API 接口都返回统一的 `CommonResult<T>` 格式
2. 请求 DTO 包含完整的参数校验注解
3. 响应 DTO 包含枚举描述字段，便于前端显示
4. API 接口设计遵循 RESTful 规范
5. 所有文件都遵循项目的命名规范和代码风格
6. 接口文档注释完整，便于其他模块调用

## 后续工作

1. 在 server 模块中实现这些 API 接口
2. 添加单元测试用例
3. 完善业务逻辑和异常处理
4. 根据实际业务需求调整接口参数和返回值
5. 添加更多的业务相关 API 接口 