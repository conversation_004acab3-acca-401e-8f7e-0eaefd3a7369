package cn.bztmaster.cnt.module.publicbiz.api.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.*;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntRegisterService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 阿姨注册 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class AuntRegisterApiImpl implements AuntRegisterApi {

    @Resource
    private AuntRegisterService auntRegisterService;

    @Override
    public CommonResult<AuntRegisterSubmitRespDTO> submitRegister(AuntRegisterSubmitReqDTO reqDTO) {
        AuntRegisterSubmitRespDTO respDTO = auntRegisterService.submitRegister(reqDTO);
        return CommonResult.success(respDTO);
    }

    @Override
    public CommonResult<AuntRegisterStatusRespDTO> getRegisterStatus(String mobile, String openId) {
        AuntRegisterStatusRespDTO respDTO = auntRegisterService.getRegisterStatus(mobile, openId);
        return CommonResult.success(respDTO);
    }

    @Override
    public CommonResult<AuntRegisterDetailRespDTO> getRegisterDetail(String applicationId) {
        AuntRegisterDetailRespDTO respDTO = auntRegisterService.getRegisterDetail(applicationId);
        return CommonResult.success(respDTO);
    }

    @Override
    public CommonResult<AuntRegisterSubmitRespDTO> resubmitRegister(AuntRegisterResubmitReqDTO reqDTO) {
        AuntRegisterSubmitRespDTO respDTO = auntRegisterService.resubmitRegister(reqDTO);
        return CommonResult.success(respDTO);
    }

    @Override
    public CommonResult<List<AgencyListRespDTO>> getAgencyList(String keyword) {
        List<AgencyListRespDTO> respDTOList = auntRegisterService.getAgencyList(keyword);
        return CommonResult.success(respDTOList);
    }
}
