# 机构详情接口文档

## 1. 获取机构基本信息

### 接口地址
```
GET /publicbiz/agency/detail
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 机构ID |

### 请求示例
```javascript
uni.request({
  url: '/publicbiz/agency/detail',
  data: { 
    id: 1001 
  },
  success: (res) => {
    console.log('机构详情:', res.data)
  }
})
```

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 机构详情数据 |

### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1001,
    "agencyNo": "AG20241201001",
    "name": "金牌家政服务有限公司",
    "shortName": "金牌家政",
    "agencyType": "cooperation",
    "legalRepresentative": "张三",
    "unifiedSocialCreditCode": "91410100MA12345678",
    "establishmentDate": "2016-01-01",
    "registeredAddress": "河南省郑州市二七区大学路128号",
    "operatingAddress": "河南省郑州市二七区大学路128号",
    "businessScope": "家政服务、保洁服务、月嫂服务等",
    "contactPerson": "李四",
    "contactPhone": "0371-8888-8888",
    "contactEmail": "<EMAIL>",
    "agencyAddress": "郑州市二七区大学路128号",
    "province": "河南省",
    "city": "郑州市",
    "district": "二七区",
    "street": "大学路街道",
    "detailAddress": "128号",
    "longitude": 113.6401,
    "latitude": 34.7236,
    "locationAccuracy": "high",
    "cooperationStatus": "cooperating",
    "contractNo": "HT20241201001",
    "contractStartDate": "2024-01-01",
    "contractEndDate": "2024-12-31",
    "commissionRate": 15.00,
    "reviewStatus": "approved",
    "status": "active",
    "remark": "优质家政服务机构",
    "createTime": "2024-01-01 10:00:00",
    "updateTime": "2024-01-01 10:00:00",
    // 前端展示所需字段
    "logo": "https://example.com/logo.png",
    "banner": "https://example.com/banner.png",
    "rating": 4.5,
    "reviewCount": 23000,
    "serviceCount": 125000,
    "establishTime": 8
  }
}
```

### 数据库查询
```sql
SELECT 
  id, agency_no, agency_name, agency_short_name, agency_type,
  legal_representative, unified_social_credit_code, establishment_date,
  registered_address, operating_address, business_scope,
  contact_person, contact_phone, contact_email,
  agency_address, province, city, district, street, detail_address,
  longitude, latitude, location_accuracy,
  cooperation_status, contract_no, contract_start_date, contract_end_date,
  commission_rate, review_status, status, remark,
  create_time, update_time
FROM publicbiz_agency 
WHERE id = ? AND deleted = 0 AND status = 'active'
```

## 2. 获取机构阿姨列表

### 接口地址
```
GET /publicbiz/agency/aunts
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| agencyId | Long | 是 | 机构ID |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页数量，默认10 |

### 请求示例
```javascript
uni.request({
  url: '/publicbiz/agency/aunts',
  data: { 
    agencyId: 1001,
    page: 1,
    size: 10
  },
  success: (res) => {
    console.log('机构阿姨列表:', res.data)
  }
})
```

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 阿姨列表数据 |

### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 50,
    "pages": 5,
    "current": 1,
    "size": 10,
    "records": [
      {
        "id": 1,
        "auntOneid": "aunt-001",
        "name": "赵阿姨",
        "phone": "13800138001",
        "idCard": "******************",
        "hometown": "四川成都",
        "age": 35,
        "gender": "female",
        "avatar": "https://example.com/avatar1.png",
        "serviceType": "保洁",
        "experienceYears": 5,
        "platformStatus": "cooperating",
        "rating": 4.9,
        "agencyId": 1001,
        "agencyName": "金牌家政服务有限公司",
        "status": "active",
        "currentStatus": "待岗",
        "currentOrderId": null,
        "totalOrders": 156,
        "totalIncome": 15600.00,
        "customerSatisfaction": 4.8,
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00",
        // 前端展示所需字段
        "tag": "金牌保洁"
      }
    ]
  }
}
```

### 数据库查询
```sql
SELECT 
  id, aunt_oneid, name, phone, id_card, hometown, age, gender,
  avatar, service_type, experience_years, platform_status, rating,
  agency_id, agency_name, status, current_status, current_order_id,
  total_orders, total_income, customer_satisfaction,
  create_time, update_time
FROM publicbiz_practitioner 
WHERE agency_id = ? AND deleted = 0 AND status = 'active'
  AND platform_status = 'cooperating'
  AND (? IS NULL OR service_type = ?)
ORDER BY rating DESC, total_orders DESC
LIMIT ? OFFSET ?
```

## 3. 获取机构服务套餐列表

### 接口地址
```
GET /publicbiz/agency/packages
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| agencyId | Long | 是 | 机构ID |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页数量，默认10 |

### 请求示例
```javascript
uni.request({
  url: '/publicbiz/employer/agency/packages',
  data: { 
    agencyId: 1001,
    page: 1,
    size: 10
  },
  success: (res) => {
    console.log('机构套餐列表:', res.data)
  }
})
```

### 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 套餐列表数据 |

### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 20,
    "pages": 2,
    "current": 1,
    "size": 10,
    "records": [
      {
        "id": 1,
        "name": "月度保洁服务 | 30天",
        "category": "日常保洁",
        "thumbnail": "https://example.com/package1.png",
        "price": 2800.00,
        "originalPrice": 3200.00,
        "unit": "月",
        "serviceDuration": "30天",
        "packageType": "long-term",
        "serviceDescription": "专业保洁服务，每日2小时，30天服务周期",
        "serviceDetails": "<p>详细服务内容...</p>",
        "serviceProcess": "<p>服务流程...</p>",
        "purchaseNotice": "购买须知...",
        "status": "active",
        "advanceBookingDays": 1,
        "timeSelectionMode": "fixed",
        "appointmentMode": "start-date",
        "serviceStartTime": "within-3-days",
        "addressSetting": "fixed",
        "maxBookingDays": 30,
        "cancellationPolicy": "取消政策...",
        "auditStatus": "approved",
        "agencyId": 1001,
        "agencyName": "金牌家政服务有限公司",
        "categoryId": 1,
        "serviceTimeStart": "09:00:00",
        "serviceTimeEnd": "11:00:00",
        "restDayType": "sunday",
        "serviceTimespan": "9:00-11:00",
        "serviceTimes": 30,
        "validityPeriod": 30,
        "validityPeriodUnit": "day",
        "serviceIntervalType": "daily",
        "serviceIntervalValue": 1,
        "singleDurationHours": 2,
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00",
        // 前端展示所需字段
        "title": "月度保洁服务 | 30天",
        "duration": "30天 | 每日2小时",
        "image": "https://example.com/package1.png",
        "tag": "长周期套餐"
      }
    ]
  }
}
```

### 数据库查询
```sql
SELECT 
  id, name, category, thumbnail, price, original_price, unit,
  service_duration, package_type, service_description, service_details,
  service_process, purchase_notice, status, advance_booking_days,
  time_selection_mode, appointment_mode, service_start_time,
  address_setting, max_booking_days, cancellation_policy,
  audit_status, agency_id, agency_name, category_id,
  service_time_start, service_time_end, rest_day_type,
  service_timespan, service_times, validity_period, validity_period_unit,
  service_interval_type, service_interval_value, single_duration_hours,
  create_time, update_time
FROM publicbiz_service_package 
WHERE agency_id = ? AND deleted = 0 AND status = 'active'
  AND audit_status = 'approved'
  AND (? IS NULL OR category = ?)
  AND (? IS NULL OR package_type = ?)
ORDER BY create_time DESC
LIMIT ? OFFSET ?
```

## 4. 获取机构评价统计

### 接口地址
```
GET /publicbiz/agency/review-stats
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| agencyId | Long | 是 | 机构ID |

### 请求示例
```javascript
uni.request({
  url: '/publicbiz/employer/agency/review-stats',
  data: { 
    agencyId: 1001
  },
  success: (res) => {
    console.log('机构评价统计:', res.data)
  }
})
```

### 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalReviews": 23000,
    "averageRating": 4.5
  }
}
```

### 数据库查询
```sql
-- 评价总数和平均评分
SELECT 
  COUNT(*) as total_reviews,
  AVG(rating) as average_rating
FROM publicbiz_aunt_review 
WHERE agency_id = ? AND deleted = 0 AND status = 1




```


## 5. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 机构不存在 |
| 500 | 服务器内部错误 |

## 6. 注意事项

1. 所有接口都需要进行用户身份验证
2. 机构ID必须为有效的数字
3. 分页参数page从1开始，size建议不超过50
4. 时间格式统一使用ISO 8601格式：YYYY-MM-DD HH:mm:ss
5. 图片URL需要完整的可访问地址
6. 系统暂时没有优惠券，优惠券模块不改动
7. 金额字段使用decimal类型，保留2位小数 