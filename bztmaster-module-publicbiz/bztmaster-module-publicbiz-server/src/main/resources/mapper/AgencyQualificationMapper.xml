<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyQualificationMapper">

    <sql id="selectFields">
        id, agency_id, file_type, file_name, file_url,
        file_size, file_extension, sort_order, status,
        tenant_id, creator, create_time, updater, update_time, deleted
    </sql>

    <select id="selectListByAgencyId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyQualificationDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_agency_qualification
        WHERE agency_id = #{agencyId}
        AND status = 1
        AND deleted = 0
        ORDER BY sort_order ASC, id ASC
    </select>

    <delete id="deleteByAgencyId">
        DELETE FROM publicbiz_agency_qualification
        WHERE agency_id = #{agencyId}
    </delete>

</mapper> 