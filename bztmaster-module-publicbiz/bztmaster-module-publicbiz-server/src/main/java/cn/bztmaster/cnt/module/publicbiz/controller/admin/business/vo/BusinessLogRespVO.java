package cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "商机中心 - 商机日志 Response VO")
public class BusinessLogRespVO {
    private Long id;
    private Long tenantId;
    private Long businessId;
    private String action;
    private String content;
    private Date actionTime;
    private Long actionUserId;
    private String actionUserName;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Boolean deleted;
} 