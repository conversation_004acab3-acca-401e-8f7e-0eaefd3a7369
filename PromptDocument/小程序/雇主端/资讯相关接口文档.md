新闻资讯表结构如下：
CREATE TABLE `publicbiz_news` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `news_title` varchar(200) NOT NULL COMMENT '资讯标题',
  `news_summary` text COMMENT '资讯摘要',
  `news_content` longtext COMMENT '资讯内容',
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类ID',
  `category_name` varchar(100) DEFAULT NULL COMMENT '分类名称',
  `cover_image_url` varchar(500) DEFAULT NULL COMMENT '封面图片URL',
  `material_id` bigint(20) DEFAULT NULL COMMENT '关联素材文章ID',
  `author` varchar(50) DEFAULT NULL COMMENT '作者',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `status` varchar(20) NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿/published-已发布/offline-已下架',
  `view_count` int(11) DEFAULT '0' COMMENT '浏览次数',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞次数',
  `share_count` int(11) DEFAULT '0' COMMENT '分享次数',
  `comment_count` bigint(20) DEFAULT '0' COMMENT '评论数',
  `sort` int(11) DEFAULT '0' COMMENT '排序，数字越小越靠前',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_news_title` (`news_title`),
  KEY `idx_author` (`author`),
  KEY `idx_status` (`status`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资讯主表';
# 雇主端首页资讯接口文档

## 接口概述
雇主端首页资讯相关接口，包含获取资讯列表、资讯详情等功能，支持分页查询和条件筛选。

## 1. 获取首页资讯列表（前4条）

### 接口信息
- **接口名称**: 获取首页资讯列表
- **请求方式**: GET
- **接口路径**: `/publicbiz/employer/news/home`
- **接口描述**: 获取首页展示的资讯列表，按排序返回前4条已发布的资讯

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | integer | 否 | 4 | 返回数量，最大10 |

### 响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "newsTitle": "家政服务行业新标准正式发布实施",
      "newsSummary": "为了更好地规范家政服务行业，提升服务质量...",
      "coverImageUrl": "https://example.com/news1.jpg",
      "categoryName": "行业动态",
      "author": "平台管理员",
      "publishTime": "2024-01-15 10:00:00",
      "viewCount": 1250,
      "likeCount": 89,
      "shareCount": 23,
      "commentCount": 15,
      "sort": 1,
      "createTime": "2024-01-15 10:00:00"
    }
  ]
}
```

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应状态码，0表示成功 |
| message | string | 响应消息 |
| data | array | 资讯列表 |
| data[].id | integer | 资讯ID |
| data[].newsTitle | string | 资讯标题 |
| data[].newsSummary | string | 资讯摘要 |
| data[].coverImageUrl | string | 封面图片URL |
| data[].categoryName | string | 分类名称 |
| data[].author | string | 作者 |
| data[].publishTime | string | 发布时间，格式：yyyy-MM-dd HH:mm:ss |
| data[].viewCount | integer | 浏览次数 |
| data[].likeCount | integer | 点赞次数 |
| data[].shareCount | integer | 分享次数 |
| data[].commentCount | integer | 评论数 |
| data[].sort | integer | 排序值 |
| data[].createTime | string | 创建时间 |

## 2. 获取资讯列表（分页）

### 接口信息
- **接口名称**: 获取资讯列表
- **请求方式**: GET
- **接口路径**: `/publicbiz/employer/news/list`
- **接口描述**: 获取资讯列表，支持分页和条件筛选

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| pageSize | integer | 否 | 10 | 每页数量，最大50 |
| categoryId | integer | 否 | - | 分类ID筛选 |
| keyword | string | 否 | - | 标题关键词搜索 |
| status | string | 否 | published | 状态筛选：draft-草稿/published-已发布/offline-已下架 |

### 响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "newsTitle": "家政服务行业新标准正式发布实施",
        "newsSummary": "为了更好地规范家政服务行业，提升服务质量...",
        "coverImageUrl": "https://example.com/news1.jpg",
        "categoryId": 1,
        "categoryName": "行业动态",
        "author": "平台管理员",
        "publishTime": "2024-01-15 10:00:00",
        "viewCount": 1250,
        "likeCount": 89,
        "shareCount": 23,
        "commentCount": 15,
        "sort": 1,
        "createTime": "2024-01-15 10:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应状态码，0表示成功 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| data.list | array | 资讯列表 |
| data.list[].id | integer | 资讯ID |
| data.list[].newsTitle | string | 资讯标题 |
| data.list[].newsSummary | string | 资讯摘要 |
| data.list[].coverImageUrl | string | 封面图片URL |
| data.list[].categoryId | integer | 分类ID |
| data.list[].categoryName | string | 分类名称 |
| data.list[].author | string | 作者 |
| data.list[].publishTime | string | 发布时间 |
| data.list[].viewCount | integer | 浏览次数 |
| data.list[].likeCount | integer | 点赞次数 |
| data.list[].shareCount | integer | 分享次数 |
| data.list[].commentCount | integer | 评论数 |
| data.list[].sort | integer | 排序值 |
| data.list[].createTime | string | 创建时间 |
| data.total | integer | 总记录数 |
| data.page | integer | 当前页码 |
| data.pageSize | integer | 每页数量 |
| data.totalPages | integer | 总页数 |

## 3. 获取资讯详情

### 接口信息
- **接口名称**: 获取资讯详情
- **请求方式**: GET
- **接口路径**: `/publicbiz/employer/news/detail/{id}`
- **接口描述**: 根据ID获取资讯详细信息

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 资讯ID |

### 响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "newsTitle": "家政服务行业新标准正式发布实施",
    "newsSummary": "为了更好地规范家政服务行业，提升服务质量...",
    "newsContent": "<p>这里是资讯的详细内容，支持HTML格式...</p>",
    "categoryId": 1,
    "categoryName": "行业动态",
    "coverImageUrl": "https://example.com/news1.jpg",
    "materialId": 123,
    "author": "平台管理员",
    "publishTime": "2024-01-15 10:00:00",
    "status": "published",
    "viewCount": 1250,
    "likeCount": 89,
    "shareCount": 23,
    "commentCount": 15,
    "sort": 1,
    "createTime": "2024-01-15 10:00:00",
    "updateTime": "2024-01-15 10:00:00"
  }
}
```

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应状态码，0表示成功 |
| message | string | 响应消息 |
| data | object | 资讯详情 |
| data.id | integer | 资讯ID |
| data.newsTitle | string | 资讯标题 |
| data.newsSummary | string | 资讯摘要 |
| data.newsContent | string | 资讯内容，支持HTML格式 |
| data.categoryId | integer | 分类ID |
| data.categoryName | string | 分类名称 |
| data.coverImageUrl | string | 封面图片URL |
| data.materialId | integer | 关联素材文章ID |
| data.author | string | 作者 |
| data.publishTime | string | 发布时间 |
| data.status | string | 状态：draft-草稿/published-已发布/offline-已下架 |
| data.viewCount | integer | 浏览次数 |
| data.likeCount | integer | 点赞次数 |
| data.shareCount | integer | 分享次数 |
| data.commentCount | integer | 评论数 |
| data.sort | integer | 排序值 |
| data.createTime | string | 创建时间 |
| data.updateTime | string | 更新时间 |

## 4. 增加资讯浏览次数

### 接口信息
- **接口名称**: 增加资讯浏览次数
- **请求方式**: POST
- **接口路径**: `/publicbiz/employer/news/incrementView/{id}`
- **接口描述**: 增加指定资讯的浏览次数

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 资讯ID |

### 响应格式
```json
{
  "code": 0,
  "message": "浏览次数增加成功",
  "data": {
    "viewCount": 1251
  }
}
```

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 业务规则
1. **状态筛选**: 首页和列表默认只返回已发布(status=published)的资讯
2. **排序规则**: 按sort升序排列，相同sort按发布时间降序
3. **分页限制**: 每页最大数量限制为50条
4. **浏览次数**: 查看详情时自动增加浏览次数
5. **内容安全**: 资讯内容支持HTML格式，需要做好XSS防护

## 查询条件
- 删除标记：deleted = 0
- 状态：status = 'published' (已发布状态)
- 排序：按sort ASC, publish_time DESC排序

## 示例请求

### 获取首页资讯（前4条）
```
GET /publicbiz/employer/news/home?limit=4
```

### 获取资讯列表（分页）
```
GET /publicbiz/employer/news/list?page=1&pageSize=10&categoryId=1&keyword=家政
```

### 获取资讯详情
```
GET /publicbiz/employer/news/detail/1
```

### 增加浏览次数
```
POST /publicbiz/employer/news/incrementView/1
```

## 前端集成说明
1. **首页加载**: 页面加载时调用home接口获取前4条资讯
2. **查看全部**: 点击"查看全部"跳转到资讯列表页面
3. **资讯详情**: 点击资讯卡片跳转到详情页面
4. **浏览次数**: 进入详情页面时自动调用增加浏览次数接口
5. **图片处理**: 封面图片支持懒加载和错误处理

## 数据库查询示例

### 首页资讯查询
```sql
SELECT 
    id,
    news_title,
    news_summary,
    cover_image_url,
    category_name,
    author,
    publish_time,
    view_count,
    like_count,
    share_count,
    comment_count,
    sort,
    create_time
FROM publicbiz_news 
WHERE deleted = 0 
    AND status = 'published'
    AND publish_time <= NOW()
ORDER BY sort ASC, publish_time DESC
LIMIT 4;
```

### 资讯列表查询
```sql
SELECT 
    id,
    news_title,
    news_summary,
    cover_image_url,
    category_id,
    category_name,
    author,
    publish_time,
    view_count,
    like_count,
    share_count,
    comment_count,
    sort,
    create_time
FROM publicbiz_news 
WHERE deleted = 0 
    AND status = 'published'
    AND publish_time <= NOW()
    AND (category_id = ? OR ? IS NULL)
    AND (news_title LIKE CONCAT('%', ?, '%') OR ? IS NULL)
ORDER BY sort ASC, publish_time DESC
LIMIT ?, ?;
```

### 资讯详情查询
```sql
SELECT 
    id,
    news_title,
    news_summary,
    news_content,
    category_id,
    category_name,
    cover_image_url,
    material_id,
    author,
    publish_time,
    status,
    view_count,
    like_count,
    share_count,
    comment_count,
    sort,
    create_time,
    update_time
FROM publicbiz_news 
WHERE deleted = 0 
    AND id = ?
    AND status = 'published';
```


