package cn.bztmaster.cnt.module.publicbiz.util.domestic;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;

/**
 * 家政服务排班日期计算工具类测试
 * 
 * <AUTHOR>
 */
class DomesticScheduleUtilTest {

    @Test
    @DisplayName("测试每日服务频次 - 无休息日")
    void testDailyFrequencyWithoutRestDay() {
        LocalDate startDate = LocalDate.of(2025, 8, 18); // 周一
        int servicePeriodDays = 7; // 7天
        String serviceFrequency = "day";
        String restDayType = "none";
        
        List<LocalDate> scheduleDates = DomesticScheduleUtil.generateScheduleDates(
            startDate, servicePeriodDays, serviceFrequency, restDayType);
        
        // 应该生成7个任务，每天一个
        assertEquals(7, scheduleDates.size());
        assertEquals(startDate, scheduleDates.get(0));
        assertEquals(startDate.plusDays(6), scheduleDates.get(6));
    }

    @Test
    @DisplayName("测试每日服务频次 - 跳过周日")
    void testDailyFrequencySkipSunday() {
        LocalDate startDate = LocalDate.of(2025, 8, 16); // 周六
        int servicePeriodDays = 7; // 7天
        String serviceFrequency = "day";
        String restDayType = "sunday";
        
        List<LocalDate> scheduleDates = DomesticScheduleUtil.generateScheduleDates(
            startDate, servicePeriodDays, serviceFrequency, restDayType);
        
        // 应该跳过周日（8月17日）
        assertTrue(scheduleDates.size() >= 6);
        assertFalse(scheduleDates.contains(LocalDate.of(2025, 8, 17))); // 周日应该被跳过
        
        // 验证没有周日
        for (LocalDate date : scheduleDates) {
            assertNotEquals(DayOfWeek.SUNDAY, date.getDayOfWeek());
        }
    }

    @Test
    @DisplayName("测试每周服务频次 - 无休息日")
    void testWeeklyFrequencyWithoutRestDay() {
        LocalDate startDate = LocalDate.of(2025, 8, 18); // 周一
        int servicePeriodDays = 28; // 4周
        String serviceFrequency = "weekly";
        String restDayType = "none";
        
        List<LocalDate> scheduleDates = DomesticScheduleUtil.generateScheduleDates(
            startDate, servicePeriodDays, serviceFrequency, restDayType);
        
        // 应该生成4个任务，每周一个
        assertEquals(4, scheduleDates.size());
        assertEquals(startDate, scheduleDates.get(0));
        assertEquals(startDate.plusWeeks(1), scheduleDates.get(1));
        assertEquals(startDate.plusWeeks(2), scheduleDates.get(2));
        assertEquals(startDate.plusWeeks(3), scheduleDates.get(3));
    }

    @Test
    @DisplayName("测试每周服务频次 - 跳过周日")
    void testWeeklyFrequencySkipSunday() {
        LocalDate startDate = LocalDate.of(2025, 8, 17); // 周日
        int servicePeriodDays = 21; // 3周
        String serviceFrequency = "weekly";
        String restDayType = "sunday";
        
        List<LocalDate> scheduleDates = DomesticScheduleUtil.generateScheduleDates(
            startDate, servicePeriodDays, serviceFrequency, restDayType);
        
        // 第一个任务应该从周一开始（因为周日被跳过）
        assertTrue(scheduleDates.size() >= 2);
        assertEquals(LocalDate.of(2025, 8, 18), scheduleDates.get(0)); // 周一
        
        // 验证没有周日
        for (LocalDate date : scheduleDates) {
            assertNotEquals(DayOfWeek.SUNDAY, date.getDayOfWeek());
        }
    }

    @Test
    @DisplayName("测试每月服务频次 - 无休息日")
    void testMonthlyFrequencyWithoutRestDay() {
        LocalDate startDate = LocalDate.of(2025, 8, 1);
        int servicePeriodDays = 90; // 3个月
        String serviceFrequency = "monthly";
        String restDayType = "none";
        
        List<LocalDate> scheduleDates = DomesticScheduleUtil.generateScheduleDates(
            startDate, servicePeriodDays, serviceFrequency, restDayType);
        
        // 应该生成3个任务，每月一个
        assertEquals(3, scheduleDates.size());
        assertEquals(startDate, scheduleDates.get(0));
        assertEquals(startDate.plusMonths(1), scheduleDates.get(1));
        assertEquals(startDate.plusMonths(2), scheduleDates.get(2));
    }

    @Test
    @DisplayName("测试跳过法定节假日")
    void testSkipStatutoryHolidays() {
        LocalDate startDate = LocalDate.of(2025, 1, 1); // 元旦
        int servicePeriodDays = 5;
        String serviceFrequency = "day";
        String restDayType = "statutory";
        
        List<LocalDate> scheduleDates = DomesticScheduleUtil.generateScheduleDates(
            startDate, servicePeriodDays, serviceFrequency, restDayType);
        
        // 应该跳过元旦（1月1日）
        assertFalse(scheduleDates.contains(LocalDate.of(2025, 1, 1)));
        
        // 第一个任务应该从1月2日开始
        if (!scheduleDates.isEmpty()) {
            assertEquals(LocalDate.of(2025, 1, 2), scheduleDates.get(0));
        }
    }

    @Test
    @DisplayName("测试同时跳过周日和法定节假日")
    void testSkipBothSundayAndStatutoryHolidays() {
        LocalDate startDate = LocalDate.of(2025, 1, 1); // 元旦，周三
        int servicePeriodDays = 10;
        String serviceFrequency = "day";
        String restDayType = "both";
        
        List<LocalDate> scheduleDates = DomesticScheduleUtil.generateScheduleDates(
            startDate, servicePeriodDays, serviceFrequency, restDayType);
        
        // 应该跳过元旦和所有周日
        assertFalse(scheduleDates.contains(LocalDate.of(2025, 1, 1))); // 元旦
        
        // 验证没有周日和法定节假日
        for (LocalDate date : scheduleDates) {
            assertNotEquals(DayOfWeek.SUNDAY, date.getDayOfWeek());
            assertFalse(DomesticScheduleUtil.isStatutoryHoliday(date));
        }
    }

    @Test
    @DisplayName("测试任务数量计算 - 每日")
    void testCalculateTaskCountDaily() {
        int taskCount = DomesticScheduleUtil.calculateTaskCount(30, "day");
        assertEquals(30, taskCount);
    }

    @Test
    @DisplayName("测试任务数量计算 - 每周")
    void testCalculateTaskCountWeekly() {
        int taskCount = DomesticScheduleUtil.calculateTaskCount(28, "weekly");
        assertEquals(4, taskCount); // 28天 = 4周
        
        taskCount = DomesticScheduleUtil.calculateTaskCount(30, "weekly");
        assertEquals(5, taskCount); // 30天 = 5周（向上取整）
    }

    @Test
    @DisplayName("测试任务数量计算 - 每月")
    void testCalculateTaskCountMonthly() {
        int taskCount = DomesticScheduleUtil.calculateTaskCount(90, "monthly");
        assertEquals(3, taskCount); // 90天 = 3个月
        
        taskCount = DomesticScheduleUtil.calculateTaskCount(100, "monthly");
        assertEquals(4, taskCount); // 100天 = 4个月（向上取整）
    }

    @Test
    @DisplayName("测试服务频次枚举解析")
    void testServiceFrequencyParsing() {
        assertEquals(DomesticScheduleUtil.ServiceFrequency.DAY, 
                    DomesticScheduleUtil.ServiceFrequency.fromCode("day"));
        assertEquals(DomesticScheduleUtil.ServiceFrequency.DAY, 
                    DomesticScheduleUtil.ServiceFrequency.fromCode("每日"));
        assertEquals(DomesticScheduleUtil.ServiceFrequency.WEEKLY, 
                    DomesticScheduleUtil.ServiceFrequency.fromCode("weekly"));
        assertEquals(DomesticScheduleUtil.ServiceFrequency.WEEKLY, 
                    DomesticScheduleUtil.ServiceFrequency.fromCode("每周"));
        assertEquals(DomesticScheduleUtil.ServiceFrequency.MONTHLY, 
                    DomesticScheduleUtil.ServiceFrequency.fromCode("monthly"));
        assertEquals(DomesticScheduleUtil.ServiceFrequency.YEARLY, 
                    DomesticScheduleUtil.ServiceFrequency.fromCode("year"));
    }

    @Test
    @DisplayName("测试休息日类型枚举解析")
    void testRestDayTypeParsing() {
        assertEquals(DomesticScheduleUtil.RestDayType.NONE, 
                    DomesticScheduleUtil.RestDayType.fromCode("none"));
        assertEquals(DomesticScheduleUtil.RestDayType.SUNDAY, 
                    DomesticScheduleUtil.RestDayType.fromCode("sunday"));
        assertEquals(DomesticScheduleUtil.RestDayType.STATUTORY, 
                    DomesticScheduleUtil.RestDayType.fromCode("statutory"));
        assertEquals(DomesticScheduleUtil.RestDayType.BOTH, 
                    DomesticScheduleUtil.RestDayType.fromCode("both"));
        assertEquals(DomesticScheduleUtil.RestDayType.NEGOTIABLE, 
                    DomesticScheduleUtil.RestDayType.fromCode("negotiable"));
    }

    @Test
    @DisplayName("测试边界情况 - 服务周期为0")
    void testEdgeCaseZeroServicePeriod() {
        LocalDate startDate = LocalDate.of(2025, 8, 18);
        int servicePeriodDays = 0;
        String serviceFrequency = "day";
        String restDayType = "none";
        
        List<LocalDate> scheduleDates = DomesticScheduleUtil.generateScheduleDates(
            startDate, servicePeriodDays, serviceFrequency, restDayType);
        
        assertTrue(scheduleDates.isEmpty());
    }

    @Test
    @DisplayName("测试边界情况 - 服务周期为1天")
    void testEdgeCaseOneDay() {
        LocalDate startDate = LocalDate.of(2025, 8, 18);
        int servicePeriodDays = 1;
        String serviceFrequency = "day";
        String restDayType = "none";
        
        List<LocalDate> scheduleDates = DomesticScheduleUtil.generateScheduleDates(
            startDate, servicePeriodDays, serviceFrequency, restDayType);
        
        assertEquals(1, scheduleDates.size());
        assertEquals(startDate, scheduleDates.get(0));
    }
}
