package cn.bztmaster.cnt.module.publicbiz.convert.employer;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsHomeRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsListRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.NewsDO;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsStatusUpdateReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.NewsStatusUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 资讯 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface NewsConvert {

    NewsConvert INSTANCE = Mappers.getMapper(NewsConvert.class);

    NewsHomeRespVO.NewsItem convertHomeItem(NewsDO bean);

    List<NewsHomeRespVO.NewsItem> convertHomeItems(List<NewsDO> list);

    NewsListRespVO.NewsItem convertListItem(NewsDO bean);

    List<NewsListRespVO.NewsItem> convertListItems(List<NewsDO> list);

    PageResult<NewsListRespVO.NewsItem> convertPage(PageResult<NewsDO> page);

    NewsDetailRespVO convertDetail(NewsDO bean);

    // ==================== 管理后台转换方法 ====================

    AdminNewsRespVO convertAdmin(NewsDO bean);

    List<AdminNewsRespVO> convertAdminList(List<NewsDO> list);

    PageResult<AdminNewsRespVO> convertAdminPage(PageResult<NewsDO> page);

    // ==================== API层转换方法 ====================

    NewsRespDTO convertApi(AdminNewsRespVO bean);

    List<NewsRespDTO> convertApiList(List<AdminNewsRespVO> list);

    PageResult<NewsRespDTO> convertApiPage(PageResult<AdminNewsRespVO> page);

    AdminNewsPageReqVO convertPageReq(NewsPageReqDTO bean);

    AdminNewsSaveReqVO convertSaveReq(NewsSaveReqDTO bean);

    NewsStatusUpdateReqVO convertStatusReq(NewsStatusUpdateReqDTO bean);

}