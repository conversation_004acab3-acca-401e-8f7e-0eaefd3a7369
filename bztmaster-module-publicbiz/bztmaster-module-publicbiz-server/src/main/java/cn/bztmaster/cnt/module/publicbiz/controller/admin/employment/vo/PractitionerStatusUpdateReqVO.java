package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "就业服务-阿姨状态更新 Request VO")
public class PractitionerStatusUpdateReqVO {
    @Schema(description = "阿姨ID")
    private Long id;

    @Schema(description = "目标状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "目标状态不能为空")
    private String platformStatus;

    @Schema(description = "解约原因")
    private String reason;
} 