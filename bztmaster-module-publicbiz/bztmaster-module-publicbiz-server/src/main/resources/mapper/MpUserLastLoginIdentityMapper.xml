<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserLastLoginIdentityMapper">

    <sql id="selectFields">
        id, user_id, openid, unionid, identity_type, identity_id, identity_name, last_login_time, last_login_ip, 
        device_info, login_status, session_key, access_token, refresh_token, token_expire_time, 
        create_time, update_time, creator, updater, deleted, tenant_id
    </sql>

    <select id="selectByUserIdAndOpenid" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserLastLoginIdentityDO">
        SELECT <include refid="selectFields"/>
        FROM mp_user_last_login_identity
        WHERE user_id = #{userId} AND openid = #{openid} AND deleted = 0
        LIMIT 1
    </select>

    <select id="selectByOpenid" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserLastLoginIdentityDO">
        SELECT <include refid="selectFields"/>
        FROM mp_user_last_login_identity
        WHERE openid = #{openid} AND deleted = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <select id="selectByUserId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserLastLoginIdentityDO">
        SELECT <include refid="selectFields"/>
        FROM mp_user_last_login_identity
        WHERE user_id = #{userId} AND deleted = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

</mapper> 