package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 阿姨注册状态响应 VO
 *
 * <AUTHOR>
 */
@Data
public class AuntRegisterStatusRespVO {

    /**
     * 申请ID
     */
    private String applicationId;

    /**
     * 阿姨OneID
     */
    private String auntOneId;

    /**
     * 申请状态：pending-待审核/approved-已通过/rejected-已拒绝
     */
    private String status;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 是否可以重新提交
     */
    private Boolean canResubmit;
}
