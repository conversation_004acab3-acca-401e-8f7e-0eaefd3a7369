package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 工单重新指派任务请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单重新指派任务请求 VO")
@Data
public class WorkOrderReassignTaskReqVO {

    @Schema(description = "任务编号列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "任务编号列表不能为空")
    private List<String> taskNoList;

    @Schema(description = "新执行人员姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵阿姨")
    @NotBlank(message = "新执行人员姓名不能为空")
    private String newPractitionerName;

    @Schema(description = "新执行人员OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "b2c3d4e5-f6g7-8901-bcde-f23456789012")
    @NotBlank(message = "新执行人员OneID不能为空")
    private String newPractitionerOneid;


} 