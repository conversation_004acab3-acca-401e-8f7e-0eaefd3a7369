# 首页轮播图接口文档

## 接口概述
获取首页轮播图列表，支持按平台、状态、时间范围等条件筛选，返回已启用的轮播图数据。

## 接口信息
- **接口名称**: 获取轮播图列表
- **请求方式**: GET
- **接口路径**: `/publicbiz/employer/carousel/list`
- **接口描述**: 获取指定平台的轮播图列表，支持分页和条件筛选

## 请求参数

### Query参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| platform | string | 是 | - | 平台类型：employer-雇主端，aunt-阿姨端 |
| page | integer | 否 | 1 | 页码，从1开始 |
| pageSize | integer | 否 | 10 | 每页数量，最大50 |
| status | integer | 否 | 1 | 状态筛选：1-启用，0-禁用，不传则查询所有 |

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "carouselTitle": "限时优惠活动",
        "carouselImageUrl": "https://example.com/banner1.jpg",
        "carouselLinkUrl": "/pages/activity/index",
        "sortOrder": 1,
        "status": 1,
        "startTime": "2024-01-01 00:00:00",
        "endTime": "2024-12-31 23:59:59",
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应状态码，200表示成功 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| data.list | array | 轮播图列表 |
| data.list[].id | integer | 轮播图ID |
| data.list[].carouselTitle | string | 轮播图标题 |
| data.list[].carouselImageUrl | string | 轮播图片URL |
| data.list[].carouselLinkUrl | string | 跳转链接URL，可为空 |
| data.list[].sortOrder | integer | 排序值，数字越小越靠前 |
| data.list[].status | integer | 状态：1-启用，0-禁用 |
| data.list[].startTime | string | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| data.list[].endTime | string | 结束时间，格式：yyyy-MM-dd HH:mm:ss |
| data.list[].createTime | string | 创建时间 |
| data.list[].updateTime | string | 更新时间 |
| data.total | integer | 总记录数 |
| data.page | integer | 当前页码 |
| data.pageSize | integer | 每页数量 |
| data.totalPages | integer | 总页数 |

## 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 业务规则
1. **状态筛选**: 默认只返回状态为启用(status=1)的轮播图
2. **时间筛选**: 自动过滤当前时间不在有效时间范围内的轮播图
3. **排序规则**: 按sortOrder升序排列，相同sortOrder按创建时间降序
4. **分页限制**: 每页最大数量限制为50条
5. **平台隔离**: 不同平台的轮播图数据相互隔离

## 查询条件
- 删除标记：deleted = 0
- 状态：status = 1 (启用状态)
- 平台：platform = 请求参数中的platform值
- 时间范围：当前时间在startTime和endTime之间，或startTime/endTime为空

## 示例请求

### 获取雇主端轮播图
```
GET /api/carousel/list?platform=employer&page=1&pageSize=5
```

### 获取阿姨端轮播图
```
GET /api/carousel/list?platform=aunt&page=1&pageSize=5
```

### 获取所有状态的轮播图（管理员使用）
```
GET /api/carousel/list?platform=employer&status=&page=1&pageSize=10
```

## 前端集成说明
1. 页面加载时调用接口获取轮播图数据
2. 根据返回的carouselImageUrl显示图片
3. 点击轮播图时，如果有carouselLinkUrl则进行页面跳转
4. 建议实现轮播图缓存机制，减少接口调用频率
5. 支持下拉刷新重新获取轮播图数据

