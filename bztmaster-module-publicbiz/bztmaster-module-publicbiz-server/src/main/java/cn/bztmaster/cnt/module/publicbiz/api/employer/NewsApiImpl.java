package cn.bztmaster.cnt.module.publicbiz.api.employer;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsStatusUpdateReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.NewsService;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.NewsStatusUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.convert.employer.NewsConvert;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 资讯 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class NewsApiImpl implements NewsApi {

    @Resource
    private NewsService newsService;

    @Override
    public PageResult<NewsRespDTO> pageNews(NewsPageReqDTO reqDTO) {
        // 转换为管理后台VO
        AdminNewsPageReqVO pageReqVO = NewsConvert.INSTANCE.convertPageReq(reqDTO);
        
        // 调用Service查询
        PageResult<AdminNewsRespVO> pageResult = newsService.getNewsPage(pageReqVO);
        
        // 转换为API DTO
        return NewsConvert.INSTANCE.convertApiPage(pageResult);
    }

    @Override
    public NewsRespDTO getNews(Long id) {
        // 调用Service查询
        AdminNewsRespVO news = newsService.getNews(id);
        
        // 转换为API DTO
        return NewsConvert.INSTANCE.convertApi(news);
    }

    @Override
    public Long createNews(NewsSaveReqDTO reqDTO) {
        // 转换为管理后台VO
        AdminNewsSaveReqVO createReqVO = NewsConvert.INSTANCE.convertSaveReq(reqDTO);
        
        // 调用Service创建
        return newsService.createNews(createReqVO);
    }

    @Override
    public void updateNews(NewsSaveReqDTO reqDTO) {
        // 转换为管理后台VO
        AdminNewsSaveReqVO updateReqVO = NewsConvert.INSTANCE.convertSaveReq(reqDTO);
        
        // 调用Service更新
        newsService.updateNews(updateReqVO);
    }

    @Override
    public void deleteNews(Long id) {
        newsService.deleteNews(id);
    }

    @Override
    public void updateNewsStatus(NewsStatusUpdateReqDTO reqDTO) {
        // 转换为管理后台VO
        NewsStatusUpdateReqVO statusReqVO = NewsConvert.INSTANCE.convertStatusReq(reqDTO);
        
        // 调用Service更新状态
        newsService.updateNewsStatus(statusReqVO.getId(), statusReqVO.getStatus());
    }
} 