package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 工单任务详情响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单任务详情响应 VO")
@Data
public class WorkOrderTaskDetailRespVO {

    @Schema(description = "订单号", example = "ORD202312010001")
    private String orderNo;

    @Schema(description = "服务类型名称", example = "日常保洁")
    private String serviceCategoryName;

    @Schema(description = "任务总数", example = "10")
    private Integer taskCount;

    @Schema(description = "已完成任务数", example = "8")
    private Integer completedTaskCount;

    @Schema(description = "待处理任务数", example = "2")
    private Integer pendingTaskCount;

    @Schema(description = "已取消任务数", example = "0")
    private Integer cancelledTaskCount;

    @Schema(description = "任务进度(百分比)", example = "80.0")
    private Double taskProgress;
} 