package cn.bztmaster.cnt.module.publicbiz.service.siteManagement;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteManagementSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.service.siteManagement.SiteManagementService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 场地管理操作日志测试类
 * 
 * 用于验证场地管理模块的操作日志功能是否正常工作
 * 
 * <AUTHOR>
 */
@SpringBootTest
public class SiteManagementLogTest {

    @Resource
    private SiteManagementService siteManagementService;

    /**
     * 测试场地新增操作日志
     */
    @Test
    public void testCreateSiteManagementLog() {
        // 创建测试数据
        SiteManagementSaveReqVO createReqVO = createTestSiteManagement();
        
        System.out.println("准备创建场地：" + createReqVO.getName());
        
        // 执行新增操作
        Long siteId = siteManagementService.createSiteManagement(createReqVO);
        
        System.out.println("场地创建成功，ID: " + siteId);
        System.out.println("请检查操作日志表，应该记录：新增了场地【" + createReqVO.getName() + "】，校区【" + createReqVO.getCampusName() + "】");
    }

    /**
     * 测试场地更新操作日志（重点测试 seatTypes 字段的类型转换）
     */
    @Test
    public void testUpdateSiteManagementLog() {
        // 1. 先创建一个场地
        SiteManagementSaveReqVO createReqVO = createTestSiteManagement();
        Long siteId = siteManagementService.createSiteManagement(createReqVO);

        // 2. 更新场地信息，特别是座位类型配置
        SiteManagementSaveReqVO updateReqVO = createTestSiteManagement();
        updateReqVO.setId(siteId);
        updateReqVO.setName("更新后的场地名称");
        updateReqVO.setType("会议室");
        updateReqVO.setLocation("B座2楼201室");
        updateReqVO.setEquipment("投影仪、音响设备、空调、白板、视频会议设备");
        updateReqVO.setDescription("高端会议室，适合重要会议");
        updateReqVO.setManager("李老师");
        updateReqVO.setManagerPhone("13987654321");

        // 修改座位类型配置 - 这是重点测试的字段
        List<SiteManagementSaveReqVO.SeatTypeVO> newSeatTypes = new ArrayList<>();
        SiteManagementSaveReqVO.SeatTypeVO seatType1 = new SiteManagementSaveReqVO.SeatTypeVO();
        seatType1.setName("会议座");
        seatType1.setCount(30);
        seatType1.setRemark("高端会议椅");
        newSeatTypes.add(seatType1);

        SiteManagementSaveReqVO.SeatTypeVO seatType2 = new SiteManagementSaveReqVO.SeatTypeVO();
        seatType2.setName("贵宾座");
        seatType2.setCount(10);
        seatType2.setRemark("真皮贵宾椅");
        newSeatTypes.add(seatType2);

        updateReqVO.setSeatTypes(newSeatTypes);

        System.out.println("准备更新场地，从【" + createReqVO.getName() + "】更新为【" + updateReqVO.getName() + "】");
        System.out.println("特别测试 seatTypes 字段的类型转换（JSON字符串 -> 对象列表）");

        // 执行更新操作
        siteManagementService.updateSiteManagement(updateReqVO);

        System.out.println("场地更新完成");
        System.out.println("请检查操作日志表，应该记录字段级别的变更信息，包括座位类型配置的变更");
    }

    /**
     * 测试场地删除操作日志
     */
    @Test
    public void testDeleteSiteManagementLog() {
        // 1. 先创建一个场地
        SiteManagementSaveReqVO createReqVO = createTestSiteManagement();
        Long siteId = siteManagementService.createSiteManagement(createReqVO);
        
        System.out.println("准备删除场地：" + createReqVO.getName());
        
        // 2. 删除场地
        siteManagementService.deleteSiteManagement(siteId);
        
        System.out.println("场地删除完成");
        System.out.println("请检查操作日志表，应该记录：删除了场地【" + createReqVO.getName() + "】，校区【" + createReqVO.getCampusName() + "】");
    }

    /**
     * 创建测试用的场地数据
     */
    private SiteManagementSaveReqVO createTestSiteManagement() {
        SiteManagementSaveReqVO reqVO = new SiteManagementSaveReqVO();
        reqVO.setName("测试场地_" + System.currentTimeMillis());
        reqVO.setCampus("TEST_CAMPUS");
        reqVO.setCampusName("测试校区");
        reqVO.setType("培训教室");
        reqVO.setLocation("A座1楼101室");
        reqVO.setEquipment("投影仪、音响设备、空调、白板");
        reqVO.setStatus("可用");
        reqVO.setDescription("标准培训教室，适合各类技能培训");
        reqVO.setManager("张老师");
        reqVO.setManagerPhone("13812345678");
        
        // 设置座位类型配置
        List<SiteManagementSaveReqVO.SeatTypeVO> seatTypes = new ArrayList<>();
        SiteManagementSaveReqVO.SeatTypeVO seatType = new SiteManagementSaveReqVO.SeatTypeVO();
        seatType.setName("普通座");
        seatType.setCount(50);
        seatType.setRemark("标准课桌椅");
        seatTypes.add(seatType);
        reqVO.setSeatTypes(seatTypes);
        
        return reqVO;
    }
}
