package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 机构资质文件新增请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构资质文件新增请求 VO")
@Data
public class AgencyQualificationCreateVO {

    @Schema(description = "文件名称", example = "营业执照.pdf")
    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    @Schema(description = "文件类型", example = "business_license")
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    @Schema(description = "文件URL", example = "https://example.com/file.pdf")
    @NotBlank(message = "文件URL不能为空")
    private String fileUrl;

    @Schema(description = "文件大小(KB)", example = "1024")
    private Long fileSize;

    @Schema(description = "备注", example = "备注信息")
    private String remark;
} 