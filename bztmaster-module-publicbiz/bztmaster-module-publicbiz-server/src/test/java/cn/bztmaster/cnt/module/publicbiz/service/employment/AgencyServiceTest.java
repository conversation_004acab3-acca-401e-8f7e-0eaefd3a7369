package cn.bztmaster.cnt.module.publicbiz.service.employment;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.test.core.ut.BaseDbUnitTest;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import static cn.bztmaster.cnt.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.bztmaster.cnt.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.bztmaster.cnt.framework.test.core.util.RandomUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * {@link AgencyService} 的单元测试类
 *
 * <AUTHOR>
 */
class AgencyServiceTest extends BaseDbUnitTest {

    @Resource
    private AgencyService agencyService;

    @MockBean
    private AgencyMapper agencyMapper;

    @Test
    @DisplayName("分页查询机构列表")
    public void testPageAgency() {
        // mock 数据
        AgencyDO dbAgency = randomPojo(AgencyDO.class, o -> { // 等会查询到
            o.setAgencyName("测试机构");
            o.setAgencyNo("AG001");
            o.setCooperationStatus("cooperating");
            o.setReviewStatus("pending");
        });
        agencyMapper.insert(dbAgency);// @Sql: 先插入出一条存在的数据
        // 测试 agencyName 不匹配
        agencyMapper.insert(cloneIgnoreId(dbAgency, o -> o.setAgencyName("不匹配")));
        // 测试 agencyNo 不匹配
        agencyMapper.insert(cloneIgnoreId(dbAgency, o -> o.setAgencyNo("不匹配")));
        // 测试 cooperationStatus 不匹配
        agencyMapper.insert(cloneIgnoreId(dbAgency, o -> o.setCooperationStatus("suspended")));
        // 测试 reviewStatus 不匹配
        agencyMapper.insert(cloneIgnoreId(dbAgency, o -> o.setReviewStatus("approved")));
        // 准备参数
        AgencyPageReqVO reqVO = new AgencyPageReqVO();
        reqVO.setKeyword("测试");
        reqVO.setCooperationStatus("cooperating");
        reqVO.setReviewStatus("pending");

        // 调用
        PageResult<AgencyRespVO> pageResult = agencyService.pageAgency(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbAgency, pageResult.getList().get(0));
    }

    @Test
    @DisplayName("获取机构详情")
    public void testGetAgency() {
        // mock 数据
        AgencyDO dbAgency = randomPojo(AgencyDO.class);
        agencyMapper.insert(dbAgency);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbAgency.getId();

        // 调用
        AgencyRespVO agency = agencyService.getAgency(id);
        // 断言
        assertPojoEquals(dbAgency, agency);
    }

    @Test
    @DisplayName("更新机构审核状态")
    public void testUpdateAgency() {
        // mock 数据
        AgencyDO dbAgency = randomPojo(AgencyDO.class);
        agencyMapper.insert(dbAgency);// @Sql: 先插入出一条存在的数据
        // 准备参数
        AgencyUpdateReqVO reqVO = new AgencyUpdateReqVO();
        reqVO.setId(dbAgency.getId());
        reqVO.setReviewStatus("approved");
        reqVO.setReviewRemark("审核通过");

        // 调用
        agencyService.updateAgency(reqVO);
        // 校验是否更新正确
        AgencyDO newAgency = agencyMapper.selectById(reqVO.getId()); // 获取最新的
        assertEquals(reqVO.getReviewStatus(), newAgency.getReviewStatus());
        assertEquals(reqVO.getReviewRemark(), newAgency.getReviewRemark());
    }

}