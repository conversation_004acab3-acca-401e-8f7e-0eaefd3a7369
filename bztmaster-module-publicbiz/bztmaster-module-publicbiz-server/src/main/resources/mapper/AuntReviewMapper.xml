<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.AuntReviewMapper">

    <!-- 根据套餐ID查询平均评分 -->
    <select id="selectAverageRatingByPackageId" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG(rating), 0)
        FROM publicbiz_aunt_review
        WHERE deleted = 0
            AND status = 1
            AND service_package_id = #{packageId}
    </select>

    <!-- 根据套餐ID列表查询平均评分 -->
    <select id="selectAverageRatingByPackageIds" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.AuntReviewDO">
        SELECT
            service_package_id as servicePackageId,
            COALESCE(AVG(rating), 0) as rating
        FROM publicbiz_aunt_review
        WHERE deleted = 0
            AND status = 1
            AND service_package_id IN
        <foreach collection="packageIds" item="packageId" open="(" separator="," close=")">
            #{packageId}
        </foreach>
        GROUP BY service_package_id
    </select>

    <!-- 根据机构ID查询该机构所有套餐的平均评分 -->
    <select id="selectAverageRatingByAgencyId" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG(ar.rating), 0)
        FROM publicbiz_aunt_review ar
        INNER JOIN publicbiz_service_package sp ON ar.service_package_id = sp.id
        WHERE ar.deleted = 0
            AND ar.status = 1
            AND sp.deleted = 0
            AND sp.status = 'active'
            AND sp.audit_status = 'approved'
            AND sp.agency_id = #{agencyId}
    </select>

</mapper>
