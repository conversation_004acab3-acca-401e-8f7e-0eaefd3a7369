# 高校实践订单支付记录功能说明

## 功能概述

当高校实践订单的支付状态为"已支付"时，系统会自动在`publicbiz_order_payment`表中创建支付记录，用于跟踪订单的支付信息。

## 功能特性

### 1. 自动创建支付记录
- 当创建订单时，如果`paymentStatus`为"paid"，系统会自动创建支付记录
- 当更新订单时，如果支付状态从非"paid"变为"paid"，系统会自动创建支付记录

### 2. 自动更新支付记录
- 当更新订单时，如果支付状态保持为"paid"，系统会自动更新支付记录
- 更新内容包括：支付方式、支付金额、操作人、支付备注等

### 3. 自动删除支付记录
- 当更新订单时，如果支付状态从"paid"变为非"paid"，系统会自动删除支付记录
- 当删除订单时，系统会自动删除相关的支付记录

## 数据库表结构

### publicbiz_order_payment 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 支付记录ID，主键 |
| order_id | BIGINT | 订单ID |
| order_no | VARCHAR(50) | 订单号 |
| payment_no | VARCHAR(50) | 支付单号，唯一 |
| payment_type | VARCHAR(20) | 支付类型 |
| payment_amount | DECIMAL(12,2) | 支付金额 |
| payment_status | VARCHAR(20) | 支付状态 |
| payment_time | DATETIME | 支付时间 |
| operator_id | BIGINT | 操作人ID |
| operator_name | VARCHAR(50) | 操作人姓名 |
| payment_remark | TEXT | 支付备注 |
| transaction_id | VARCHAR(100) | 第三方交易号 |

## 支付类型说明

| 支付类型 | 说明 |
|----------|------|
| cash | 现金 |
| wechat | 微信支付 |
| alipay | 支付宝 |
| bank_transfer | 银行转账 |
| pos | POS机刷卡 |
| other | 其他 |

## 支付状态说明

| 支付状态 | 说明 |
|----------|------|
| pending | 待支付 |
| success | 支付成功 |
| failed | 支付失败 |
| cancelled | 已取消 |

## 使用示例

### 1. 创建已支付订单（自动创建支付记录）

```json
{
  "projectName": "YY大学春季实习项目商机",
  "universityName": "222",
  "enterpriseName": "333",
  "startDate": "2025-08-01",
  "endDate": "2028-08-17",
  "totalAmount": 4200001,
  "managerId": 1,
  "managerName": "李四",
  "contractType": "electronic",
  "paymentStatus": "paid",
  "collectionAmount": 4200001,
  "collectionMethod": "bank_transfer",
  "collectionDate": "2024-12-19",
  "operatorName": "李四",
  "collectionRemark": "银行转账收款"
}
```

### 2. 更新订单支付状态（自动更新支付记录）

```json
{
  "id": 1,
  "orderNo": "HP202412190001",
  "paymentStatus": "paid",
  "collectionAmount": 4200001,
  "collectionMethod": "wechat",
  "collectionDate": "2024-12-19",
  "operatorName": "王五",
  "collectionRemark": "微信支付收款"
}
```

## 支付单号生成规则

支付单号格式：`PAY + yyyyMMdd + 6位随机数`

示例：
- `PAY202412190001`
- `PAY202412190002`

## 业务逻辑

### 1. 创建订单时的支付记录处理

```java
// 如果支付状态为已支付，创建支付记录
if ("paid".equals(createReqVO.getPaymentStatus())) {
    createPaymentRecord(order.getId(), orderNo, createReqVO);
}
```

### 2. 更新订单时的支付记录处理

```java
// 处理支付记录更新
handlePaymentRecordUpdate(updateReqVO, oldOrder);
```

### 3. 删除订单时的支付记录处理

```java
// 删除支付记录
deletePaymentRecord(id);
```

## 错误处理

### 1. 支付记录创建失败
- 错误码：`PAYMENT_RECORD_CREATE_FAILED`
- 错误信息：支付记录创建失败
- 处理方式：抛出异常，回滚事务

### 2. 支付记录更新失败
- 错误码：`PAYMENT_RECORD_UPDATE_FAILED`
- 错误信息：支付记录更新失败
- 处理方式：记录错误日志，不影响主流程

### 3. 支付记录删除失败
- 错误码：`PAYMENT_RECORD_DELETE_FAILED`
- 错误信息：支付记录删除失败
- 处理方式：记录错误日志，不影响主流程

## 注意事项

1. **事务一致性**：支付记录的创建、更新、删除都在同一个事务中进行，确保数据一致性
2. **异常处理**：支付记录操作失败不会影响主订单流程，但会记录错误日志
3. **数据完整性**：支付记录与订单记录保持同步，订单删除时支付记录也会被删除
4. **支付单号唯一性**：系统会自动生成唯一的支付单号，避免重复
5. **操作日志**：所有支付记录操作都会记录详细的操作日志

## 测试验证

### 1. 单元测试

已添加以下测试用例：
- `testCreateOrder_WithCollectionInfo()`: 测试创建包含收款信息的订单，验证支付记录创建
- 支付记录相关的Mock设置和验证

### 2. 运行测试

```bash
cd bztmaster-module-publicbiz/bztmaster-module-publicbiz-server
mvn test -Dtest=UniversityPracticeOrderServiceTest
```

## 总结

通过新增的支付记录功能，系统能够完整地跟踪高校实践订单的支付信息，包括：

- 自动创建支付记录
- 自动更新支付记录
- 自动删除支付记录
- 完整的错误处理机制
- 详细的操作日志记录

这为订单管理提供了更完整的数据支持，便于财务对账和业务分析。



