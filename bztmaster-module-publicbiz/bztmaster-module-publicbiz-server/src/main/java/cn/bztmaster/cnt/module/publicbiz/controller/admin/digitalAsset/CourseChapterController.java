package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.CourseChapterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 课程章节管理 Controller
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/components/ManagementCourseForOnline.vue
 * 
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 课程章节管理")
@RestController
@RequestMapping("/publicbiz/digital-asset/course/chapter")
@Validated
public class CourseChapterController {

    @Resource
    private CourseChapterService courseChapterService;

    @GetMapping("/list/{courseId}")
    @Operation(summary = "获取课程章节列表")
    @Parameter(name = "courseId", description = "课程ID", required = true)
    public CommonResult<List<CourseChapterRespVO>> listCourseChapter(@PathVariable("courseId") Long courseId) {
        List<CourseChapterRespVO> list = courseChapterService.listCourseChapter(courseId);
        return CommonResult.success(list);
    }

    @PostMapping("/add")
    @Operation(summary = "新增课程章节")
    public CommonResult<Long> createCourseChapter(@Valid @RequestBody CourseChapterSaveReqVO reqVO) {
        Long chapterId = courseChapterService.createCourseChapter(reqVO);
        return CommonResult.success(chapterId);
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程章节")
    public CommonResult<Boolean> updateCourseChapter(@Valid @RequestBody CourseChapterSaveReqVO reqVO) {
        courseChapterService.updateCourseChapter(reqVO);
        return CommonResult.success(true);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除课程章节")
    @Parameter(name = "id", description = "章节ID", required = true)
    public CommonResult<Boolean> deleteCourseChapter(@PathVariable("id") Long id) {
        courseChapterService.deleteCourseChapter(id);
        return CommonResult.success(true);
    }
}
