package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDate;

/**
 * 人才资质证书 DO
 *
 * <AUTHOR>
 */
@TableName("talent_certificate")
@KeySequence("talent_certificate_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TalentCertificateDO extends BaseDO {

    /**
     * 证书ID
     */
    @TableId
    private Long certificateId;
    /**
     * 证书编号
     */
    private String certificateNo;
    /**
     * 关联用户ID
     */
    private Long userId;
    /**
     * 证书名称
     */
    private String name;
    /**
     * 发证机构
     */
    private String issuer;
    /**
     * 颁发日期
     */
    private LocalDate issueDate;
    /**
     * 到期时间
     */
    private LocalDate expiryDate;
    /**
     * 记录来源（可选值：PLATFORM、AGENCY、SELF）
     */
    private String source;
    /**
     * 审核状态（可选值：VERIFIED、PENDING_VERIFICATION、REJECTED）
     */
    private String status;
    /**
     * 证书图片
     */
    private String certificateImageUrl;
}

