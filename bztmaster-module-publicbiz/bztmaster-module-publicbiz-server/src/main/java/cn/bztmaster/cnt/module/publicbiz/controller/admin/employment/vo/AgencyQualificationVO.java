package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 机构资质文件 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构资质文件 VO")
@Data
public class AgencyQualificationVO {

    @Schema(description = "文件类型", example = "business_license")
    private String fileType;

    @Schema(description = "文件名", example = "营业执照.jpg")
    private String fileName;

    @Schema(description = "文件URL", example = "https://example.com/files/business_license.jpg")
    private String fileUrl;

    @Schema(description = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    @Schema(description = "文件扩展名", example = "jpg")
    private String fileExtension;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;
} 