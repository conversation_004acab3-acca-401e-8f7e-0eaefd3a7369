package cn.bztmaster.cnt.module.publicbiz.api.aunt.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 阿姨注册提交请求 DTO
 *
 * <AUTHOR>
 */
@Data
public class AuntRegisterSubmitReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;

    /**
     * 微信openId
     */
    @NotBlank(message = "微信openId不能为空")
    private String openId;

    /**
     * 阿姨姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idCard;

    /**
     * 选择的机构
     * "platform" 表示平台自营，数字表示机构ID
     */
    @NotBlank(message = "请选择归属机构")
    private String selectedAgency;

    /**
     * 身份证正面照片URL
     */
    @NotBlank(message = "请上传身份证正面照片")
    private String idCardFront;

    /**
     * 身份证背面照片URL
     */
    @NotBlank(message = "请上传身份证背面照片")
    private String idCardBack;

    /**
     * 健康证照片URL
     */
    @NotBlank(message = "请上传健康证照片")
    private String healthCert;

    /**
     * 技能证书照片URL（可选）
     */
    private String skillCert;
}
