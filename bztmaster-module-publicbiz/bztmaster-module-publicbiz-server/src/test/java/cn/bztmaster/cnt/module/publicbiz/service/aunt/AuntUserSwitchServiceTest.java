package cn.bztmaster.cnt.module.publicbiz.service.aunt;

import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntUserSwitchReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntUserSwitchRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.impl.AuntUserSwitchServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * AuntUserSwitchService 单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class AuntUserSwitchServiceTest {

    @Mock
    private PractitionerMapper practitionerMapper;

    @InjectMocks
    private AuntUserSwitchServiceImpl auntUserSwitchService;

    private PractitionerDO mockPractitioner;

    @BeforeEach
    void setUp() {
        mockPractitioner = new PractitionerDO();
        mockPractitioner.setAuntOneid("12345678-1234-1234-1234-123456789012");
        mockPractitioner.setName("张阿姨");
        mockPractitioner.setPhone("13800138000");
        mockPractitioner.setServiceType("月嫂");
        mockPractitioner.setExperienceYears(5);
        mockPractitioner.setPlatformStatus("cooperating");
        mockPractitioner.setRating(new BigDecimal("4.5"));
        mockPractitioner.setStatus("active");
        mockPractitioner.setCurrentStatus("待岗");
    }

    @Test
    void testCheckPractitionerRegistration_ByOpenId_Found() {
        // Given
        String openId = "o1234567890abcdef";
        AuntUserSwitchReqVO reqVO = new AuntUserSwitchReqVO();
        reqVO.setOpenId(openId);

        when(practitionerMapper.selectByOpenId(openId)).thenReturn(mockPractitioner);

        // When
        AuntUserSwitchRespVO result = auntUserSwitchService.checkPractitionerRegistration(reqVO);

        // Then
        assertNotNull(result);
        assertTrue(result.getIsRegistered());
        assertEquals("12345678-1234-1234-1234-123456789012", result.getAuntOneId());
        assertEquals("张阿姨", result.getName());
        assertEquals("13800138000", result.getPhone());
        assertEquals("月嫂", result.getServiceType());
        assertEquals(5, result.getExperienceYears());
        assertEquals("cooperating", result.getPlatformStatus());
        assertEquals(new BigDecimal("4.5"), result.getRating());
        assertEquals("active", result.getStatus());
        assertEquals("待岗", result.getCurrentStatus());

        verify(practitionerMapper).selectByOpenId(openId);
        verify(practitionerMapper, never()).selectByMobile(anyString());
    }

    @Test
    void testCheckPractitionerRegistration_ByMobile_Found() {
        // Given
        String mobile = "13800138000";
        AuntUserSwitchReqVO reqVO = new AuntUserSwitchReqVO();
        reqVO.setMobile(mobile);

        when(practitionerMapper.selectByOpenId(null)).thenReturn(null);
        when(practitionerMapper.selectByMobile(mobile)).thenReturn(mockPractitioner);

        // When
        AuntUserSwitchRespVO result = auntUserSwitchService.checkPractitionerRegistration(reqVO);

        // Then
        assertNotNull(result);
        assertTrue(result.getIsRegistered());
        assertEquals("张阿姨", result.getName());

        verify(practitionerMapper, never()).selectByOpenId(anyString());
        verify(practitionerMapper).selectByMobile(mobile);
    }

    @Test
    void testCheckPractitionerRegistration_NotFound() {
        // Given
        AuntUserSwitchReqVO reqVO = new AuntUserSwitchReqVO();
        reqVO.setOpenId("nonexistent_openid");
        reqVO.setMobile("nonexistent_mobile");

        when(practitionerMapper.selectByOpenId("nonexistent_openid")).thenReturn(null);
        when(practitionerMapper.selectByMobile("nonexistent_mobile")).thenReturn(null);

        // When
        AuntUserSwitchRespVO result = auntUserSwitchService.checkPractitionerRegistration(reqVO);

        // Then
        assertNotNull(result);
        assertFalse(result.getIsRegistered());
        assertNull(result.getAuntOneId());
        assertNull(result.getName());

        verify(practitionerMapper).selectByOpenId("nonexistent_openid");
        verify(practitionerMapper).selectByMobile("nonexistent_mobile");
    }

    @Test
    void testCheckPractitionerRegistration_EmptyRequest() {
        // Given
        AuntUserSwitchReqVO reqVO = new AuntUserSwitchReqVO();

        // When
        AuntUserSwitchRespVO result = auntUserSwitchService.checkPractitionerRegistration(reqVO);

        // Then
        assertNotNull(result);
        assertFalse(result.getIsRegistered());

        verify(practitionerMapper, never()).selectByOpenId(anyString());
        verify(practitionerMapper, never()).selectByMobile(anyString());
    }

}
