package cn.bztmaster.cnt.module.publicbiz.service.aunt.impl;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerQualificationDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerQualificationMapper;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntRegisterService;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class AuntRegisterServiceImpl implements AuntRegisterService {

    @Resource
    private PractitionerMapper practitionerMapper;

    @Resource
    private PractitionerQualificationMapper qualificationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuntRegisterSubmitRespDTO submitRegister(AuntRegisterSubmitReqDTO reqDTO) {
        // 验证手机号唯一性
        PractitionerDO existingPractitioner = practitionerMapper.selectByMobile(reqDTO.getMobile());
        if (existingPractitioner != null) {
            throw new ServiceException(1002, "手机号已注册");
        }

        // 验证身份证号唯一性
        existingPractitioner = practitionerMapper.selectByIdCard(reqDTO.getIdCard());
        if (existingPractitioner != null) {
            throw new ServiceException(1004, "身份证号已注册");
        }

        // 生成阿姨OneID
        String auntOneId = IdUtil.fastSimpleUUID();

        // 保存阿姨基本信息
        PractitionerDO practitioner = new PractitionerDO();
        practitioner.setAuntOneid(auntOneId);
        practitioner.setName(reqDTO.getName());
        practitioner.setPhone(reqDTO.getMobile());
        practitioner.setIdCard(reqDTO.getIdCard());
        practitioner.setStatus("pending");
        practitioner.setPlatformStatus("cooperating");
        practitioner.setRating(new BigDecimal("4.5"));
        practitioner.setTotalOrders(0);
        practitioner.setTotalIncome(new BigDecimal("0.00"));

        // 处理机构信息
        if ("platform".equals(reqDTO.getSelectedAgency())) {
            practitioner.setAgencyId(null);
            practitioner.setAgencyName("平台自营");
        } else {
            try {
                Long agencyId = Long.valueOf(reqDTO.getSelectedAgency());
                practitioner.setAgencyId(agencyId);
                practitioner.setAgencyName("合作机构");
            } catch (NumberFormatException e) {
                throw new ServiceException(1009, "机构不存在");
            }
        }

        practitionerMapper.insert(practitioner);

        // 生成申请编号
        String applicationId = "APP" + System.currentTimeMillis();

        // 构建响应
        AuntRegisterSubmitRespDTO respDTO = new AuntRegisterSubmitRespDTO();
        respDTO.setApplicationId(applicationId);
        respDTO.setAuntOneId(auntOneId);
        respDTO.setStatus("pending");
        respDTO.setSubmitTime(LocalDateTime.now());
        respDTO.setEstimatedReviewTime("1-2个工作日");

        return respDTO;
    }

    @Override
    public AuntRegisterStatusRespDTO getRegisterStatus(String mobile, String openId) {
        PractitionerDO practitioner = practitionerMapper.selectByMobile(mobile);
        if (practitioner == null) {
            throw new ServiceException(1007, "申请不存在");
        }

        AuntRegisterStatusRespDTO respDTO = new AuntRegisterStatusRespDTO();
        respDTO.setApplicationId(practitioner.getAuntOneid());
        respDTO.setAuntOneId(practitioner.getAuntOneid());
        respDTO.setStatus(practitioner.getStatus());
        respDTO.setCanResubmit("rejected".equals(practitioner.getStatus()));

        return respDTO;
    }

    @Override
    public AuntRegisterDetailRespDTO getRegisterDetail(String applicationId) {
        PractitionerDO practitioner = practitionerMapper.selectByAuntOneId(applicationId);
        if (practitioner == null) {
            throw new ServiceException(1007, "申请不存在");
        }

        AuntRegisterDetailRespDTO respDTO = new AuntRegisterDetailRespDTO();
        respDTO.setApplicationId(applicationId);
        respDTO.setAuntOneId(practitioner.getAuntOneid());
        respDTO.setStatus(practitioner.getStatus());

        // 基本信息
        AuntRegisterDetailRespDTO.BasicInfo basicInfo = new AuntRegisterDetailRespDTO.BasicInfo();
        basicInfo.setName(practitioner.getName());
        basicInfo.setPhone(practitioner.getPhone());
        basicInfo.setIdCard(practitioner.getIdCard());
        respDTO.setBasicInfo(basicInfo);

        // 机构信息
        AuntRegisterDetailRespDTO.AgencyInfo agencyInfo = new AuntRegisterDetailRespDTO.AgencyInfo();
        agencyInfo.setAgencyId(practitioner.getAgencyId());
        agencyInfo.setAgencyName(practitioner.getAgencyName());
        respDTO.setAgencyInfo(agencyInfo);

        return respDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuntRegisterSubmitRespDTO resubmitRegister(AuntRegisterResubmitReqDTO reqDTO) {
        PractitionerDO existingPractitioner = practitionerMapper.selectByAuntOneId(reqDTO.getApplicationId());
        if (existingPractitioner == null) {
            throw new ServiceException(1007, "申请不存在");
        }
        if (!"rejected".equals(existingPractitioner.getStatus())) {
            throw new ServiceException(1008, "申请状态不允许重新提交");
        }

        existingPractitioner.setName(reqDTO.getName());
        existingPractitioner.setStatus("pending");
        practitionerMapper.updateById(existingPractitioner);

        AuntRegisterSubmitRespDTO respDTO = new AuntRegisterSubmitRespDTO();
        respDTO.setApplicationId("APP" + System.currentTimeMillis());
        respDTO.setAuntOneId(existingPractitioner.getAuntOneid());
        respDTO.setStatus("pending");
        respDTO.setSubmitTime(LocalDateTime.now());
        respDTO.setEstimatedReviewTime("1-2个工作日");

        return respDTO;
    }

    @Override
    public List<AgencyListRespDTO> getAgencyList(String keyword) {
        List<AgencyListRespDTO> agencies = new ArrayList<>();
        
        AgencyListRespDTO agency1 = new AgencyListRespDTO();
        agency1.setId(1L);
        agency1.setName("XX家政");
        agency1.setDescription("专业家政服务，信誉良好");
        agency1.setStatus("cooperating");
        agencies.add(agency1);

        AgencyListRespDTO agency2 = new AgencyListRespDTO();
        agency2.setId(2L);
        agency2.setName("YY家政");
        agency2.setDescription("服务周到，客户满意度高");
        agency2.setStatus("cooperating");
        agencies.add(agency2);

        AgencyListRespDTO agency3 = new AgencyListRespDTO();
        agency3.setId(3L);
        agency3.setName("ZZ家政");
        agency3.setDescription("经验丰富，服务专业");
        agency3.setStatus("cooperating");
        agencies.add(agency3);

        if (StrUtil.isNotBlank(keyword)) {
            agencies.removeIf(agency -> !agency.getName().contains(keyword) && !agency.getDescription().contains(keyword));
        }

        return agencies;
    }
}
