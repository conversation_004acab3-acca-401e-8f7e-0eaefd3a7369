package cn.bztmaster.cnt.module.publicbiz.api.employer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资讯状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum NewsStatusEnum {

    DRAFT("draft", "草稿"),
    PUBLISHED("published", "已发布"),
    OFFLINE("offline", "已下架");

    /**
     * 状态值
     */
    private final String value;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 状态枚举
     */
    public static NewsStatusEnum getByValue(String value) {
        for (NewsStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断值是否有效
     *
     * @param value 状态值
     * @return 是否有效
     */
    public static boolean isValidValue(String value) {
        return getByValue(value) != null;
    }
} 