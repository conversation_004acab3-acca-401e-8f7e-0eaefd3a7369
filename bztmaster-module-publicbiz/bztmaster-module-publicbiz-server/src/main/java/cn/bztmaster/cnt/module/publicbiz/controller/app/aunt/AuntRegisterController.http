### 提交阿姨注册申请
POST {{baseUrl}}/publicbiz/aunt/register/submit
Content-Type: application/json

{
  "mobile": "15527495882",
  "openId": "wx_openid_123456",
  "name": "小月测试",
  "idCard": "******************",
  "selectedAgency": "platform",
  "idCardFront": "https://example.com/id_front.jpg",
  "idCardBack": "https://example.com/id_back.jpg",
  "healthCert": "https://example.com/health_cert.jpg",
  "skillCert": "https://example.com/skill_cert.jpg"
}

### 获取阿姨注册申请状态
GET {{baseUrl}}/publicbiz/aunt/register/status?mobile=15527495882&openId=wx_openid_123456

### 获取阿姨注册申请详情
GET {{baseUrl}}/publicbiz/aunt/register/detail?applicationId=APP202412160001

### 重新提交阿姨注册申请
POST {{baseUrl}}/publicbiz/aunt/register/resubmit
Content-Type: application/json

{
  "applicationId": "APP202412160001",
  "mobile": "15527495882",
  "openId": "wx_openid_123456",
  "name": "小月测试",
  "idCard": "******************",
  "selectedAgency": "platform",
  "idCardFront": "https://example.com/id_front_new.jpg",
  "idCardBack": "https://example.com/id_back_new.jpg",
  "healthCert": "https://example.com/health_cert_new.jpg",
  "skillCert": "https://example.com/skill_cert_new.jpg"
}

### 获取家政机构列表
GET {{baseUrl}}/publicbiz/aunt/register/agencies

### 搜索家政机构
GET {{baseUrl}}/publicbiz/aunt/register/agencies?keyword=XX家政
