# 任务工单管理API接口文档

## 1. 接口概述和基础信息

### 1.1 基础路径
```
基础路径: /publicbiz/workOrder
```

### 1.2 数据格式
- 请求格式：`application/json`
- 响应格式：`application/json`
- 字符编码：`UTF-8`

### 1.3 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01 12:00:00"
}
```

### 1.4 认证方式
- 请求头：`Authorization: Bearer {token}`
- 租户ID：`X-Tenant-Id: {tenantId}`

## 2. 接口详细说明

### 2.1 工单分页查询接口

**接口地址：** `POST /publicbiz/workOrder/page`

**功能说明：** 分页查询工单列表，支持多条件筛选

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Integer | 是 | 页码，从1开始 |
| size | Integer | 是 | 每页大小，最大100 |
| workOrderType | String | 否 | 工单类型：complaint-投诉/substitution_request-换人申请/take_leave-请假/顶岗/separation_application-离职申请 |
| workOrderStatus | String | 否 | 工单状态：pending-待处理/processing-处理中/resolved-已解决/closed-已关闭 |
| priority | String | 否 | 优先级：low-低/medium-中/high-高/urgent-紧急 |
| agencyName | String | 否 | 机构名称，支持模糊查询

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| data | Array | 工单列表 |
| data[].id | Long | 工单ID |
| data[].workOrderNo | String | 工单编号 |
| data[].orderNo | String | 关联订单号 |
| data[].workOrderTitle | String | 工单标题 |
| data[].workOrderType | String | 工单类型 |
| data[].priority | String | 优先级 |
| data[].workOrderStatus | String | 工单状态 |
| data[].assigneeId | Long | 负责人ID |
| data[].assigneeName | String | 负责人姓名 |
| data[].auntName | String | 阿姨姓名 |
| data[].complainerName | String | 投诉人姓名 |
| data[].agencyId | Long | 机构ID |
| data[].agencyName | String | 机构名称 |
| data[].remark | String | 备注 |
| data[].createTime | String | 创建时间

**请求示例：**
```json
{
  "page": 1,
  "size": 10,
  "workOrderType": "complaint",
  "workOrderStatus": "pending",
  "priority": "high",
  "agencyName": "阳光家政"
}
```

**返回示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "workOrderNo": "GD20240627001",
      "orderNo": "DD20240626002",
      "workOrderTitle": "服务质量投诉",
      "workOrderType": "complaint",
      "priority": "high",
      "workOrderStatus": "pending",
       "assigneeId": null,
        "assigneeName": null,
        "auntName": "陈静",
        "complainerName": "王先生",
        "agencyId": 1,
        "agencyName": "阳光家政",
        "remark": "客户要求尽快处理",
        "createTime": "2024-06-27 10:30:00"
    },
    "total": 25
  ],
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2.2 工单类型统计接口

**接口地址：** `GET /publicbiz/workOrder/typeStats`

**功能说明：** 根据当前机构id和工单类型统计数量

**请求参数：** 无

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| all | Integer | 全部工单数量 |
| complaint | Integer | 投诉工单数量 |
| substitution_request | Integer | 换人申请数量 |
| take_leave | Integer | 请假/顶岗数量 |
| separation_application | Integer | 离职申请数量 |

**返回示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "all": 25,
    "complaint": 8,
    "substitution_request": 5,
    "take_leave": 7,
    "separation_application": 5
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2.3 工单详情查询接口

**接口地址：** `GET /publicbiz/workOrder/detail/{workOrderNo}`

**功能说明：** 根据工单编码查询工单详细信息

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| workOrderNo | String | 是 | 工单编码，路径参数 |

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 工单ID |
| workOrderNo | String | 工单编号 |
| orderNo | String | 关联订单号 |
| workOrderTitle | String | 工单标题 |
| workOrderContent | String | 工单内容 |
| workOrderType | String | 工单类型 |
| priority | String | 优先级 |
| workOrderStatus | String | 工单状态 |
| assigneeId | Long | 负责人ID |
| assigneeName | String | 负责人姓名 |
| auntOneid | String | 阿姨OneID |
| auntName | String | 阿姨姓名 |
| leaveType | Integer | 请假类型(1-请假,2-调休) |
| startTime | String | 开始时间 |
| endTime | String | 结束时间 |
| durationHours | Decimal | 请假时长(小时) |
| durationDays | Decimal | 请假天数 |
| status | Integer | 审批状态(0-审批中,1-已批准,2-已驳回) |
| approveTime | String | 审批时间 |
| approveRemark | String | 审批备注 |
| complaintType | String | 投诉类型 |
| complaintLevel | String | 投诉等级 |
| complaintTime | String | 投诉时间 |
| customerExpectation | String | 客户期望 |
| complainerId | Long | 投诉人ID |
| complainerName | String | 投诉人姓名 |
| agencyId | Long | 机构ID |
| agencyName | String | 机构名称 |
| reassignmentStartDate | String | 重新指派起始日期 |
| newAuntOneid | String | 指派新阿姨OneID |
| newAuntName | String | 指派新阿姨名称 |
| reassignmentDescription | String | 指派说明 |
| reassignmentReason | String | 转派原因 |
| reassignmentRemark | String | 转派备注 |
| taskCount | Integer | 任务总数 |
| completedTaskCount | Integer | 已完成任务数 |
| pendingTaskCount | Integer | 待执行任务数 |
| remark | String | 备注 |
| createTime | String | 创建时间 |
| updateTime | String | 更新时间 |

**返回示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "workOrderNo": "GD20240627001",
    "orderNo": "DD20240626002",
    "workOrderTitle": "服务质量投诉",
    "workOrderContent": "阿姨在服务过程中态度恶劣,经常玩手机,不认真工作...",
    "workOrderType": "complaint",
    "priority": "high",
    "workOrderStatus": "pending",
    "assigneeId": null,
    "assigneeName": null,
    "auntOneid": "AY00125",
    "auntName": "陈静",
    "complaintType": "service_quality",
    "complaintLevel": "high",
    "complaintTime": "2024-06-27 14:30:00",
    "customerExpectation": "希望更换阿姨,或者对当前阿姨进行培训...",
    "complainerId": 1001,
    "complainerName": "王先生",
    "agencyId": 1,
    "agencyName": "阳光家政",
    "taskCount": 30,
    "completedTaskCount": 10,
    "pendingTaskCount": 20,
    "createTime": "2024-06-27 10:30:00",
    "updateTime": "2024-06-27 10:30:00"
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2.4 工单接单接口

**接口地址：** `POST /publicbiz/workOrder/accept`

**功能说明：** 接单处理工单，修改工单状态为处理中并添加日志，日志类型为状态变更

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| workOrderId | Long | 是 | 工单ID |
| assigneeId | Long | 是 | 处理人ID |
| assigneeName | String | 是 | 处理人姓名 |
| acceptRemark | String | 否 | 接单备注 |

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 是否成功 |
| workOrderStatus | String | 更新后的工单状态 |

**请求示例：**
```json
{
  "workOrderId": 1,
  "assigneeId": 1001,
  "assigneeName": "李主管",
  "acceptRemark": "已接单，开始处理"
}
```

**返回示例：**
```json
{
  "code": 200,
  "message": "接单成功",
  "data": {
    "success": true,
    "workOrderStatus": "processing"
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2.5 工单转派接口

**接口地址：** `POST /publicbiz/workOrder/transfer`

**功能说明：** 转派工单给其他处理人，修改工单数据并添加日志，日志类型为分配处理人

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| workOrderId | Long | 是 | 工单ID |
| newAssigneeId | Long | 是 | 新处理人ID |
| newAssigneeName | String | 是 | 新处理人姓名 |
| transferReason | String | 是 | 转派原因 |
| transferRemark | String | 否 | 转派备注 |
| priority | String | 否 | 调整后的优先级 |

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 是否成功 |
| workOrderStatus | String | 更新后的工单状态 |

**请求示例：**
```json
{
  "workOrderId": 1,
  "newAssigneeId": 1002,
  "newAssigneeName": "王经理",
  "transferReason": "专业技能",
  "transferRemark": "需要专业技能处理",
  "priority": "high"
}
```

**返回示例：**
```json
{
  "code": 200,
  "message": "转派成功",
  "data": {
    "success": true,
    "workOrderStatus": "pending"
  },
  "timestamp": "2024-01-01 12:00:00"
}
```



### 2.6 工单处理日志查询接口

**接口地址：** `GET /publicbiz/workOrder/logs/{workOrderNo}`

**功能说明：** 查询工单处理日志

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| workOrderNo | String | 是 | 工单编码，路径参数 |

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| data | Array | 日志列表 |
| data[].id | Long | 日志ID |
| data[].logType | String | 日志类型 |
| data[].logTitle | String | 日志标题 |
| data[].logContent | String | 日志内容 |
| data[].operatorId | Long | 操作人ID |
| data[].operatorName | String | 操作人姓名 |
| data[].operatorRole | String | 操作人角色 |
| data[].relatedPartyType | String | 关联方类型 |
| data[].relatedPartyName | String | 关联方名称 |
| data[].createTime | String | 创建时间 |

**返回示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "logType": "creation",
      "logTitle": "工单创建",
      "logContent": "系统自动创建工单",
      "operatorId": null,
      "operatorName": "系统",
      "operatorRole": "系统",
      "relatedPartyType": "客户",
      "relatedPartyName": "王先生",
      "createTime": "2024-06-27 10:30:00"
    },
    {
      "id": 2,
      "logType": "status_change",
      "logTitle": "状态变更",
      "logContent": "工单状态从待处理变更为处理中",
      "operatorId": 1001,
      "operatorName": "李主管",
      "operatorRole": "客服主管",
      "relatedPartyType": "处理人",
      "relatedPartyName": "李主管",
      "createTime": "2024-06-27 11:00:00"
    }
  ],
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2.7 工单附件上传接口

**接口地址：** `POST /publicbiz/workOrder/uploadAttachment`

**功能说明：** 上传工单附件

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| workOrderNo | String | 是 | 工单编码 |
| file | File | 是 | 上传的文件 |
| fileCategory | String | 是 | 文件分类：evidence-证据/document-文档/other-其他 |
| uploadPurpose | String | 否 | 上传目的说明 |

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 附件ID |
| fileName | String | 文件名 |
| fileUrl | String | 文件URL |
| fileType | String | 文件类型 |
| fileCategory | String | 文件分类 |
| uploadPurpose | String | 上传目的 |
| createTime | String | 上传时间 |

**请求示例：**
```json
{
  "workOrderNo": "GD20240627001",
  "fileCategory": "evidence",
  "uploadPurpose": "投诉证据照片"
}
```

**返回示例：**
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "id": 1,
    "fileName": "投诉照片.jpg",
    "fileUrl": "https://example.com/files/complaint_photo.jpg",
    "fileType": "image/jpeg",
    "fileCategory": "evidence",
    "uploadPurpose": "投诉证据照片",
    "createTime": "2024-06-27 10:30:00"
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2.8 工单附件列表查询接口

**接口地址：** `GET /publicbiz/workOrder/attachments/{workOrderNo}`

**功能说明：** 查询工单附件列表

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| workOrderNo | String | 是 | 工单编码，路径参数 |

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| data | Array | 附件列表 |
| data[].id | Long | 附件ID |
| data[].fileName | String | 文件名 |
| data[].fileUrl | String | 文件URL |
| data[].fileType | String | 文件类型 |
| data[].fileCategory | String | 文件分类 |
| data[].uploadPurpose | String | 上传目的 |
| data[].createTime | String | 上传时间 |

**返回示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "fileName": "投诉照片.jpg",
      "fileUrl": "https://example.com/files/complaint_photo.jpg",
      "fileType": "image/jpeg",
      "fileCategory": "evidence",
      "uploadPurpose": "投诉证据",
      "createTime": "2024-06-27 10:30:00"
    }
  ],
  "timestamp": "2024-01-01 12:00:00"
}
```



### 2.9 获取阿姨列表接口

**接口地址：** `GET /publicbiz/workOrder/workOrderPractitioners`

**功能说明：** 获取待岗状态的阿姨列表，用于工单转派或换人申请时选择

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| data | Array | 阿姨列表 |
| data[].id | Long | 阿姨ID |
| data[].auntOneid | String | 阿姨OneID |
| data[].name | String | 阿姨姓名 |
| data[].rating | Decimal | 评级，1.0-5.0 |
| data[].serviceType | String | 主要服务类型 |

**返回示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "auntOneid": "AY00126",
      "name": "张丽",
      "rating": 4.8,
      "serviceType": "保洁"
    },
    {
      "id": 2,
      "auntOneid": "AY00127",
      "name": "李梅",
      "rating": 4.6,
      "serviceType": "育儿嫂"
    }
  ],
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2.10 获取服务任务详细接口

**接口地址：** `GET /publicbiz/workOrder/taskDetail/{orderNo}`

**功能说明：** 根据订单号查询服务任务的详细信息

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderNo | String | 是 | 订单号，路径参数 |

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| data | Object | 任务详细信息 |
| data.orderNo | String | 订单号 |
| data.serviceCategoryName | String | 服务类型 |
| data.taskCount | Integer | 任务总数 |
| data.completedTaskCount | Integer | 已完成任务数 |
| data.pendingTaskCount | Integer | 待执行任务数 |
| data.cancelledTaskCount | Integer | 已取消任务数 |
| data.taskProgress | Decimal | 任务进度百分比 |

**返回示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "orderNo": "DD20240626002",
    "serviceCategoryName": "保洁",
    "taskCount": 30,
    "completedTaskCount": 10,
    "pendingTaskCount": 18,
    "cancelledTaskCount": 2,
    "taskProgress": 33.33
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2.11 获取任务列表接口

**接口地址：** `GET /publicbiz/workOrder/taskList`

**功能说明：** 根据订单号查询任务列表，支持按任务状态和执行人员筛选

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderNo | String | 是 | 订单号，路径参数 |
| taskStatus | String | 否 | 任务状态筛选：pending-待分配/assigned-已分配/in_progress-进行中/completed-已完成/cancelled-已取消 |
| practitionerName | String | 否 | 执行人员姓名，支持模糊查询 |
| practitionerOneid | String | 否 | 执行人员OneID |

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| data | Array | 任务列表 |
| data[].id | Long | 任务ID |
| data[].taskNo | String | 任务编号 |
| data[].taskSequence | Integer | 任务序号 |
| data[].plannedStartTime | String | 计划开始时间 |
| data[].taskStatus | String | 任务状态 |
| data[].practitionerName | String | 服务人员姓名 |
| data[].practitionerOneid | String | 服务人员OneID |
| data[].actualEndTime | String | 实际结束时间 |

**筛选参数说明：**
- `taskStatus`: 可筛选特定状态的任务，如只查看已完成的任务
- `practitionerName`: 可按执行人员姓名筛选，支持模糊查询
- `practitionerOneid`: 可按执行人员OneID精确筛选

**请求示例：**
```
GET /publicbiz/workOrder/taskList/DD20240626002?taskStatus=completed&practitionerName=张丽
```

**返回示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "taskNo": "RW20240627001",
      "taskSequence": 1,
      "plannedStartTime": "2024-06-27 09:00:00",
      "taskStatus": "completed",
      "practitionerName": "张丽",
      "practitionerOneid": "AY00126",
      "actualEndTime": "2024-06-27 13:00:00"
    },
    {
      "id": 2,
      "taskNo": "RW20240627002",
      "taskSequence": 2,
      "plannedStartTime": "2024-06-28 09:00:00",
      "taskStatus": "pending",
      "practitionerName": "李梅",
      "practitionerOneid": "AY00127",
      "actualEndTime": null
    }
  ],
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2.12 重新指派任务接口

**接口地址：** `POST /publicbiz/workOrder/reassignTask`

**功能说明：** 重新指派任务给新的阿姨，支持单个或批量修改

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| taskNoList | Array | 是 | 任务编码列表 |
| practitionerName | String | 是 | 阿姨名称 |
| practitionerOneid | String | 是 | 阿姨OneID |

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 是否成功 |
| reassignedCount | Integer | 成功重新指派的任务数量 |
| failedTasks | Array | 重新指派失败的任务列表 |
| failedTasks[].taskNo | String | 失败的任务编码 |
| failedTasks[].reason | String | 失败原因 |

**请求示例：**
```json
{
  "taskNoList": ["RW20240627001", "RW20240627002"],
  "practitionerName": "张丽",
  "practitionerOneid": "AY00126"
}
```

**返回示例：**
```json
{
  "code": 200,
  "message": "重新指派成功",
  "data": {
    "success": true,
    "reassignedCount": 2,
    "failedTasks": []
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2.13 任务工单提交处理结果接口

**接口地址：** `POST /publicbiz/workOrder/submitResolution`

**功能说明：** 提交工单处理结果，添加处理日志并更新工单状态为已解决

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| workOrderNo | String | 是 | 工单编码 |
| logType | String | 是 | 日志类型：resolution-处理结果 |
| logContent | String | 是 | 处理结果内容 |

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 是否成功 |
| workOrderStatus | String | 更新后的工单状态 |
| logId | Long | 新增的日志ID |

**请求示例：**
```json
{
  "workOrderNo": "GD20240627001",
  "logType": "resolution",
  "logContent": "经调查核实，已与客户沟通达成一致，客户对处理结果表示满意，工单已解决。"
}
```

**返回示例：**
```json
{
  "code": 200,
  "message": "处理结果提交成功",
  "data": {
    "success": true,
    "workOrderStatus": "resolved",
    "logId": 123
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2.14 指派阿姨接口

**接口地址：** `POST /publicbiz/workOrder/assignAunt`

**功能说明：** 根据指派起始日期和订单号查找publicbiz_domestic_task表中所有待执行任务修改服务人员信息,并修改任务工单表中的指派新阿姨OneID，指派新阿姨名称，指派说明内容，指派更新时间

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderNo | String | 是 | 订单号 |
| plannedStartTime | String | 是 | 指派起始日期，格式：yyyy-MM-dd |
| newAuntOneid | String | 是 | 新阿姨OneID |
| newAuntName | String | 是 | 新阿姨姓名 |
| reassignmentDescription | String | 是 | 指派说明内容 |

**响应字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 是否成功 |
| reassignedTaskCount | Integer | 成功重新指派的任务数量 |
| failedTasks | Array | 重新指派失败的任务列表 |
| failedTasks[].taskNo | String | 失败的任务编码 |
| failedTasks[].reason | String | 失败原因 |

**请求示例：**
```json
{
  "orderNo": "DD20240626002",
  "plannedStartTime": "2024-06-28",
  "newAuntOneid": "AY00126",
  "newAuntName": "张丽",
  "reassignmentDescription": "原阿姨请假，临时更换为张丽阿姨"
}
```

**返回示例：**
```json
{
  "code": 200,
  "message": "阿姨指派成功",
  "data": {
    "success": true,
    "reassignedTaskCount": 5,
    "failedTasks": []
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

## 3. 数据字典

### 3.1 工单类型 (workOrderType)
- `complaint`: 投诉
- `substitution_request`: 换人申请
- `take_leave`: 请假/顶岗
- `separation_application`: 离职申请

### 3.2 工单状态 (workOrderStatus)
- `pending`: 待处理
- `processing`: 处理中
- `resolved`: 已解决
- `closed`: 已关闭

### 3.3 优先级 (priority)
- `low`: 低
- `medium`: 中
- `high`: 高
- `urgent`: 紧急

### 3.4 投诉类型 (complaintType)
- `service_quality`: 服务质量
- `attitude`: 服务态度
- `punctuality`: 守时问题
- `other`: 其他

### 3.5 投诉等级 (complaintLevel)
- `low`: 轻微
- `medium`: 中等
- `high`: 严重
- `urgent`: 紧急

### 3.6 请假类型 (leaveType)
- `1`: 请假
- `2`: 调休

### 3.7 审批状态 (status)
- `0`: 审批中
- `1`: 已批准
- `2`: 已驳回

### 3.8 日志类型 (logType)
- `creation`: 工单创建
- `status_change`: 状态变更
- `assignment`: 分配处理人
- `comment`: 处理意见
- `resolution`: 处理结果

### 3.9 步骤状态 (stepStatus)
- `pending`: 待处理
- `processing`: 处理中
- `completed`: 已完成
- `skipped`: 已跳过

### 3.10 步骤结果 (stepResult)
- `approved`: 同意
- `rejected`: 驳回
- `transferred`: 转交

### 3.11 文件分类 (fileCategory)
- `evidence`: 证据材料
- `contract`: 合同文件
- `other`: 其他

## 4. 错误码说明

### 4.1 HTTP状态码
- `200`: 请求成功
- `400`: 参数验证错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器错误

### 4.2 业务错误码
- `1001`: 工单不存在
- `1002`: 工单状态不允许操作
- `1003`: 无权限操作该工单
- `1004`: 处理人不存在
- `1005`: 文件上传失败
- `1006`: 工单编号重复

### 4.3 错误响应格式
```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": {
    "field": "workOrderId",
    "error": "工单ID不能为空"
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

## 5. 注意事项

1. 所有时间字段均采用 `yyyy-MM-dd HH:mm:ss` 格式
2. 文件上传大小限制为10MB
3. 工单状态变更需要记录操作日志
4. 转派工单时会自动创建处理日志
5. 接单后工单状态自动变更为"处理中"
6. 工单关闭前需要填写处理结果
7. 附件删除需要验证操作权限
8. 流程进度更新需要按步骤顺序进行 