package cn.bztmaster.cnt.module.publicbiz.controller.admin.category;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.category.vo.ServiceCategoryListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.category.vo.ServiceCategoryRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.category.vo.ServiceCategorySaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.category.vo.ServiceCategorySimpleRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.category.ServiceCategoryDO;
import cn.bztmaster.cnt.module.publicbiz.service.category.ServiceCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Comparator;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 服务分类")
@RestController
@RequestMapping("/publicbiz/employment/category")
@Validated
public class ServiceCategoryController {

    @Resource
    private ServiceCategoryService categoryService;

    @PostMapping("/create")
    @Operation(summary = "创建服务分类")
    //@PreAuthorize("@ss.hasPermission('publicbiz:category:create')")
    public CommonResult<Long> createCategory(@Valid @RequestBody ServiceCategorySaveReqVO createReqVO) {
        return success(categoryService.createCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新服务分类")
    @PreAuthorize("@ss.hasPermission('publicbiz:category:update')")
    public CommonResult<Boolean> updateCategory(@Valid @RequestBody ServiceCategorySaveReqVO updateReqVO) {
        categoryService.updateCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除服务分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:category:delete')")
    public CommonResult<Boolean> deleteCategory(@RequestParam("id") Long id) {
        categoryService.deleteCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得服务分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:category:query')")
    public CommonResult<ServiceCategoryRespVO> getCategory(@RequestParam("id") Long id) {
        ServiceCategoryDO category = categoryService.getCategory(id);
        return success(BeanUtils.toBean(category, ServiceCategoryRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得服务分类列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:category:query')")
    public CommonResult<List<ServiceCategoryRespVO>> getCategoryList(@Valid ServiceCategoryListReqVO listReqVO) {
        List<ServiceCategoryDO> list = categoryService.getCategoryList(listReqVO);
        list.sort(Comparator.comparing(ServiceCategoryDO::getSort));
        return success(BeanUtils.toBean(list, ServiceCategoryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得服务分类分页")
    @PreAuthorize("@ss.hasPermission('publicbiz:category:query')")
    public CommonResult<PageResult<ServiceCategoryRespVO>> getCategoryPage(@Valid ServiceCategoryListReqVO listReqVO) {
        PageResult<ServiceCategoryDO> pageResult = categoryService.getCategoryPage(listReqVO);
        return success(BeanUtils.toBean(pageResult, ServiceCategoryRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得服务分类精简列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:category:query')")
    public CommonResult<List<ServiceCategorySimpleRespVO>> getCategorySimpleList() {
        List<ServiceCategoryDO> list = categoryService.getEnableCategoryList();
        list.sort(Comparator.comparing(ServiceCategoryDO::getSort));
        return success(BeanUtils.toBean(list, ServiceCategorySimpleRespVO.class));
    }

} 