<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.teacher.TeacherCertMapper">
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.teacher.TeacherCertDO">
        <id property="id" column="id" />
        <result property="teacherId" column="teacher_id" />
        <result property="certType" column="cert_type" />
        <result property="certName" column="cert_name" />
        <result property="fileName" column="file_name" />
        <result property="fileUrl" column="file_url" />
        <result property="validStartDate" column="valid_start_date" />
        <result property="validEndDate" column="valid_end_date" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creator" column="creator" />
        <result property="updater" column="updater" />
        <result property="deleted" column="deleted" />
        <result property="tenantId" column="tenant_id" />
    </resultMap>
    <!-- 可扩展自定义SQL -->
</mapper> 