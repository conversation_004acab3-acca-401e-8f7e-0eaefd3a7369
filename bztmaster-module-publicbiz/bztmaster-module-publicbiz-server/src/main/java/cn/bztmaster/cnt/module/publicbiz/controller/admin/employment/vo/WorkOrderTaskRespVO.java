package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工单任务响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单任务响应 VO")
@Data
public class WorkOrderTaskRespVO {

    @Schema(description = "主键", example = "1")
    private Long id;

    @Schema(description = "任务编号", example = "TASK202312010001")
    private String taskNo;

    @Schema(description = "订单号", example = "ORD202312010001")
    private String orderNo;

    @Schema(description = "任务名称", example = "日常保洁")
    private String taskName;

    @Schema(description = "任务描述", example = "客厅、卧室、厨房保洁")
    private String taskDescription;

    @Schema(description = "任务状态", example = "completed")
    private String taskStatus;

    @Schema(description = "执行人员姓名", example = "李阿姨")
    private String practitionerName;

    @Schema(description = "执行人员OneID", example = "a1b2c3d4-e5f6-7890-abcd-ef1234567890")
    private String practitionerOneid;

    @Schema(description = "计划开始时间")
    private LocalDateTime plannedStartTime;

    @Schema(description = "计划结束时间")
    private LocalDateTime plannedEndTime;

    @Schema(description = "实际开始时间")
    private LocalDateTime actualStartTime;

    @Schema(description = "实际结束时间")
    private LocalDateTime actualEndTime;

    @Schema(description = "任务进度(百分比)", example = "100.0")
    private Double progress;

    @Schema(description = "备注", example = "任务完成良好")
    private String remark;
} 