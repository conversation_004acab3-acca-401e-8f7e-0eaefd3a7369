package cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 资讯响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 资讯响应 VO")
@Data
public class AdminNewsRespVO {

    @Schema(description = "资讯ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "资讯标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024年家政服务行业发展趋势分析")
    private String newsTitle;

    @Schema(description = "资讯摘要", example = "随着人口老龄化加剧和双职工家庭增多，家政服务需求持续增长...")
    private String newsSummary;

    @Schema(description = "资讯内容", example = "<p>家政服务标准化是提升服务质量的重要保障...</p>")
    private String newsContent;

    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    @Schema(description = "分类名称", example = "行业动态")
    private String categoryName;

    @Schema(description = "封面图片URL", example = "https://example.com/images/news1.jpg")
    private String coverImageUrl;

    @Schema(description = "关联素材文章ID", example = "1024")
    private Long materialId;

    @Schema(description = "作者", example = "张专家")
    private String author;

    @Schema(description = "发布时间", example = "2024-01-15 10:00:00")
    private LocalDateTime publishTime;

    @Schema(description = "状态：draft-草稿/published-已发布/offline-已下架", requiredMode = Schema.RequiredMode.REQUIRED, example = "published")
    private String status;

    @Schema(description = "浏览次数", example = "1250")
    private Integer viewCount;

    @Schema(description = "点赞次数", example = "89")
    private Integer likeCount;

    @Schema(description = "分享次数", example = "23")
    private Integer shareCount;

    @Schema(description = "评论数", example = "15")
    private Long commentCount;

    @Schema(description = "排序值", example = "1")
    private Integer sort;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-15 09:30:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-15 10:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "租户ID", example = "1")
    private Long tenantId;

    @Schema(description = "创建人", example = "admin")
    private String creator;

    @Schema(description = "更新人", example = "admin")
    private String updater;
} 