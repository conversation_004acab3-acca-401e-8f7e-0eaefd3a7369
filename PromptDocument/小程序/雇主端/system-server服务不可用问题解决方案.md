# system-server 服务不可用问题解决方案

## 问题描述

小程序请求 `getUserRole` 接口时出现以下错误：

```
feign.FeignException$ServiceUnavailable: [503] during [GET] to [http://system-server/rpc-api/system/tenant/valid?id=1] [TenantCommonApi#validTenant(Long)]: [Load balancer does not contain an instance for the service system-server]
```

## 问题原因

1. **租户验证失败**: `TenantSecurityWebFilter` 在验证租户时调用 `system-server` 的接口
2. **服务不可用**: `system-server` 服务没有启动或者注册中心中没有该服务
3. **影响范围**: 所有需要租户验证的请求都会失败

## 解决方案

### 方案1：启动 system-server 服务（推荐生产环境）

如果您有 `system-server` 服务，需要启动它：

```bash
# 启动 system-server 服务
# 具体启动命令取决于您的部署方式
```

### 方案2：配置禁用租户验证（推荐开发环境）

#### 2.1 修改配置文件

在 `application-local.yaml` 中添加租户配置：

```yaml
bztmaster:
  tenant:
    enable: false  # 禁用租户验证
```

#### 2.2 创建租户配置类

创建 `TenantConfiguration.java` 文件：

```java
@Configuration
@ConditionalOnProperty(name = "bztmaster.tenant.enable", havingValue = "false", matchIfMissing = false)
public class TenantConfiguration {

    @PostConstruct
    public void init() {
        // 设置默认租户ID为1，避免租户验证失败
        TenantContextHolder.setIgnore(true);
        TenantContextHolder.setTenantId(1L);
    }
}
```

### 方案3：在控制器中处理租户问题（临时解决方案）

在 `getUserRole` 方法中添加租户上下文设置：

```java
@PostMapping("/getUserRole")
@Operation(summary = "获取用户角色")
@PermitAll
public CommonResult<GetUserRoleRespVO> getUserRole(@Valid @RequestBody GetUserRoleReqVO reqVO) {
    // 设置租户上下文，避免租户验证失败
    TenantContextHolder.setIgnore(true);
    TenantContextHolder.setTenantId(1L);
    
    try {
        // 原有逻辑...
    } catch (Exception e) {
        log.error("获取用户角色失败", e);
        return CommonResult.error(5000, "服务器内部错误");
    }
}
```

## 验证步骤

1. **重启应用**
   ```bash
   # 重启 publicbiz-server 服务
   ```

2. **测试接口**
   ```bash
   curl -X POST http://localhost:49080/publicbiz/employer/mpuser/getUserRole \
     -H "Content-Type: application/json" \
     -d '{"code": "test_code"}'
   ```

3. **检查日志**
   - 查看是否还有租户验证相关的错误
   - 确认请求是否正常处理

## 配置说明

### 租户相关配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `bztmaster.tenant.enable` | 是否启用租户功能 | `true` |
| `bztmaster.tenant.ignore-urls` | 忽略租户验证的URL | `[]` |

### 安全相关配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `bztmaster.security.mock-enable` | 是否启用模拟登录 | `false` |

## 注意事项

1. **开发环境**: 建议禁用租户验证，简化开发流程
2. **生产环境**: 必须启动 `system-server` 服务，确保租户功能正常
3. **测试环境**: 根据实际需求决定是否启用租户验证

## 常见问题

### 问题1：配置不生效
**原因**: 配置项名称错误或位置不对
**解决**: 检查配置文件中的配置项名称和位置

### 问题2：仍然出现租户验证错误
**原因**: 可能有其他过滤器或拦截器在验证租户
**解决**: 检查是否有其他租户相关的配置

### 问题3：system-server 启动失败
**原因**: 依赖服务不可用或配置错误
**解决**: 检查 system-server 的依赖服务和配置

## 推荐做法

1. **开发阶段**: 使用方案2，禁用租户验证
2. **测试阶段**: 启动 system-server 服务，验证完整流程
3. **生产阶段**: 确保所有服务正常运行，启用租户验证

## 相关文件

- `application-local.yaml` - 本地开发配置
- `TenantConfiguration.java` - 租户配置类
- `MpUserLastLoginIdentityController.java` - 控制器文件 