package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 师资 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免导入有问题
public class TeacherImportExcelVO {

    @ExcelProperty("讲师姓名")
    private String name;

    @ExcelProperty("讲师类型")
    private String type;

    @ExcelProperty("业务模块")
    private String biz;

    @ExcelProperty("关联机构")
    private String org;

    @ExcelProperty("擅长领域")
    private String field;

    @ExcelProperty("联系电话")
    private String phone;

    @ExcelProperty("邮箱地址")
    private String email;

    @ExcelProperty("个人简介")
    private String description;

    @ExcelProperty("合作状态")
    private String status;
} 