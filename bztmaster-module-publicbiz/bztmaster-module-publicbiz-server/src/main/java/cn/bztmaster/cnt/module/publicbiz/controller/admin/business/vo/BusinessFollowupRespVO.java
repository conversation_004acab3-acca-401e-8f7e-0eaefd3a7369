package cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "商机中心 - 商机跟进 Response VO")
public class BusinessFollowupRespVO {
    private Long id;
    private Long tenantId;
    private Long businessId;
    private String content;
    private Date followTime;
    private Long followUserId;
    private String followUserName;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Boolean deleted;
} 