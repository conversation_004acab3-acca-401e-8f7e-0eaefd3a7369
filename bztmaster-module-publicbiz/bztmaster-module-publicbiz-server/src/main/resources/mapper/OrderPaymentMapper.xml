<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.OrderPaymentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.OrderPaymentDO">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="order_id" property="orderId" />
        <result column="order_no" property="orderNo" />
        <result column="payment_no" property="paymentNo" />
        <result column="payment_type" property="paymentType" />
        <result column="payment_amount" property="paymentAmount" />
        <result column="payment_status" property="paymentStatus" />
        <result column="payment_time" property="paymentTime" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_name" property="operatorName" />
        <result column="payment_remark" property="paymentRemark" />
        <result column="transaction_id" property="transactionId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, creator, create_time, updater, update_time, deleted,
        order_id, order_no, payment_no, payment_type, payment_amount, payment_status,
        payment_time, operator_id, operator_name, payment_remark, transaction_id
    </sql>

    <!-- 根据订单ID查询支付记录列表 -->
    <select id="selectListByOrderId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM publicbiz_order_payment
        WHERE deleted = 0
        <if test="orderId != null">
            AND order_id = #{orderId}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据订单号查询支付记录列表 -->
    <select id="selectListByOrderNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM publicbiz_order_payment
        WHERE deleted = 0
        <if test="orderNo != null and orderNo != ''">
            AND order_no = #{orderNo}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据支付单号查询支付记录 -->
    <select id="selectOne" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM publicbiz_order_payment
        WHERE deleted = 0 AND payment_no = #{paymentNo}
        LIMIT 1
    </select>

    <!-- 插入支付记录 -->
    <insert id="insertPayment" parameterType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.OrderPaymentDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO publicbiz_order_payment (
            tenant_id, creator, create_time, updater, update_time, deleted,
            order_id, order_no, payment_no, payment_type, payment_amount, payment_status,
            payment_time, operator_id, operator_name, payment_remark, transaction_id
        ) VALUES (
            #{tenantId}, #{creator}, #{createTime}, #{updater}, #{updateTime}, #{deleted},
            #{orderId}, #{orderNo}, #{paymentNo}, #{paymentType}, #{paymentAmount}, #{paymentStatus},
            #{paymentTime}, #{operatorId}, #{operatorName}, #{paymentRemark}, #{transactionId}
        )
    </insert>

    <!-- 更新支付记录 -->
    <update id="updatePaymentById" parameterType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.OrderPaymentDO">
        UPDATE publicbiz_order_payment
        SET updater = #{updater},
            update_time = #{updateTime},
            payment_type = #{paymentType},
            payment_amount = #{paymentAmount},
            payment_status = #{paymentStatus},
            payment_time = #{paymentTime},
            operator_id = #{operatorId},
            operator_name = #{operatorName},
            payment_remark = #{paymentRemark},
            transaction_id = #{transactionId}
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 根据ID删除支付记录（逻辑删除） -->
    <update id="deletePaymentById">
        UPDATE publicbiz_order_payment
        SET deleted = 1, updater = #{updater}, update_time = #{updateTime}
        WHERE id = #{id} AND deleted = 0
    </update>

</mapper>
