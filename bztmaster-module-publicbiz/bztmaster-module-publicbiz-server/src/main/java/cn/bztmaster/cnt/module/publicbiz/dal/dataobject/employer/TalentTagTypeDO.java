package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 标签类型表 DO
 *
 * <AUTHOR>
 */
@TableName("talent_tag_type")
@KeySequence("talent_tag_type_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TalentTagTypeDO extends BaseDO {

    /**
     * 标签类型ID
     */
    @TableId
    private Long tagTypeId;

    /**
     * 标签类型编码
     */
    private String typeCode;

    /**
     * 标签类型名称
     */
    private String typeName;

    /**
     * 描述
     */
    private String description;
}
