# 阿姨工作台业务方法实现说明

## 概述

本文档说明了阿姨工作台服务中四个核心业务方法的实现逻辑，这些方法用于获取阿姨工作台首页所需的KPI数据和订单统计信息。

## 架构说明

### 分层架构
- **Controller层**: 处理HTTP请求，调用Service层
- **Service层**: 业务逻辑处理，调用Mapper层
- **Mapper层**: 数据访问层，使用注解方式定义SQL查询
- **DO层**: 数据对象，对应数据库表结构

### SQL实现方式
所有SQL语句都使用MyBatis的`@Select`注解直接写在Mapper接口中，这种方式：
- 避免了XML文件配置的复杂性
- 符合项目的现有架构（使用MyBatis Plus）
- 便于维护和调试
- 支持复杂的SQL查询（如`TIMESTAMPDIFF`、`COALESCE`等）

## 实现的方法

### 1. getTodayScheduleCount - 获取今日排班数

**功能描述**: 查询阿姨今日的排班任务数量

**数据来源**: `publicbiz_domestic_task` 表

**Mapper方法**: `DomesticTaskMapper.selectTodayScheduleCount()`

**SQL实现**: 使用`@Select`注解

**查询逻辑**:
```sql
SELECT COUNT(*) FROM publicbiz_domestic_task
WHERE practitioner_oneid = #{practitionerOneId}
  AND DATE(schedule_date) = #{scheduleDate}
  AND deleted = 0
```

**关键字段**:
- `practitioner_oneid`: 阿姨的OneID标识
- `schedule_date`: 排班日期
- `deleted`: 软删除标记

**返回结果**: 今日排班任务数量（Integer）

---

### 2. getMonthlyIncome - 获取本月预估收入

**功能描述**: 查询阿姨本月的预估收入总额

**数据来源**: `publicbiz_settlement` 表

**Mapper方法**: `SettlementMapper.selectMonthlyIncome()`

**SQL实现**: 使用`@Select`注解

**查询逻辑**:
```sql
SELECT COALESCE(SUM(settlement_amount), 0) FROM publicbiz_settlement
WHERE practitioner_oneid = #{practitionerOneId}
  AND settlement_date >= #{startTime}
  AND settlement_date <= #{endTime}
  AND settlement_status IN ('pending', 'processing', 'completed')
  AND deleted = 0
```

**关键字段**:
- `practitioner_oneid`: 阿姨的OneID标识
- `settlement_date`: 结算日期
- `settlement_amount`: 结算金额
- `settlement_status`: 结算状态（待结算、结算中、已结算）

**时间范围**: 当前月份的第一天到月末

**返回结果**: 本月预估收入（BigDecimal）

---

### 3. getMonthlyServiceHours - 获取本月服务时长

**功能描述**: 查询阿姨本月已完成任务的服务时长总和

**数据来源**: `publicbiz_domestic_task` 表

**Mapper方法**: `DomesticTaskMapper.selectMonthlyServiceHours()`

**SQL实现**: 使用`@Select`注解

**查询逻辑**:
```sql
SELECT COALESCE(SUM(
    CASE
        WHEN actual_start_time IS NOT NULL AND actual_end_time IS NOT NULL
        THEN TIMESTAMPDIFF(HOUR, actual_start_time, actual_end_time)
        ELSE 0
    END
), 0) FROM publicbiz_domestic_task
WHERE practitioner_oneid = #{practitionerOneId}
  AND actual_start_time >= #{startTime}
  AND actual_start_time <= #{endTime}
  AND task_status = 'completed'
  AND deleted = 0
```

**关键字段**:
- `practitioner_oneid`: 阿姨的OneID标识
- `actual_start_time`: 实际开始时间
- `actual_end_time`: 实际结束时间
- `task_status`: 任务状态（已完成）

**计算方式**: 使用 `TIMESTAMPDIFF(HOUR, start_time, end_time)` 计算每个任务的小时数

**返回结果**: 本月服务时长（小时，Integer）

---

### 4. getOrderCountByStatus - 根据状态获取订单数量

**功能描述**: 查询阿姨名下不同状态的订单数量

**数据来源**: `publicbiz_order` 表 + `publicbiz_domestic_order` 表（关联查询）

**Mapper方法**: `OrderMapper.selectCountByStatusAndPractitioner()`

**SQL实现**: 使用`@Select`注解

**查询逻辑**:
```sql
SELECT COUNT(*) FROM publicbiz_order o
INNER JOIN publicbiz_domestic_order do ON o.id = do.order_id
WHERE do.practitioner_oneid = #{practitionerOneId}
  AND o.order_status = #{orderStatus}
  AND o.deleted = 0
  AND do.deleted = 0
```

**关键字段**:
- `practitioner_oneid`: 阿姨的OneID标识（来自domestic_order表）
- `order_status`: 订单状态
- `deleted`: 软删除标记

**支持的状态**:
- `pending`: 待确认
- `in_progress`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消

**返回结果**: 指定状态的订单数量（Integer）

---

## 技术实现要点

### 1. 异常处理
所有方法都包含了完整的异常处理机制，确保在查询失败时能够返回合理的默认值。

### 2. 日志记录
每个方法都包含了详细的日志记录，便于问题排查和性能监控。

### 3. 时间处理
使用Java 8的时间API（LocalDate、LocalDateTime、YearMonth）进行时间计算，确保准确性。

### 4. 软删除支持
所有查询都考虑了软删除机制，只查询未删除的记录。

### 5. 空值处理
所有Mapper返回结果都进行了空值检查，避免NPE异常。

### 6. 注解式SQL
使用MyBatis的`@Select`注解，避免了XML配置的复杂性，同时支持复杂的SQL查询。

---

## 文件结构

```
bztmaster-module-publicbiz-server/
├── src/main/java/cn/bztmaster/cnt/module/publicbiz/
│   ├── service/aunt/impl/
│   │   └── AuntWorkbenchServiceImpl.java          # 业务逻辑实现
│   └── dal/
│       ├── dataobject/
│       │   ├── domestic/DomesticTaskDO.java      # 任务数据对象
│       │   ├── settlement/SettlementDO.java      # 结算数据对象
│       │   └── order/OrderDO.java                # 订单数据对象
│       └── mysql/
│           ├── domestic/DomesticTaskMapper.java   # 任务Mapper接口（注解式SQL）
│           ├── settlement/SettlementMapper.java   # 结算Mapper接口（注解式SQL）
│           └── order/OrderMapper.java             # 订单Mapper接口（注解式SQL）
```

---

## 使用示例

```java
@Service
public class AuntWorkbenchService {
    
    @Resource
    private DomesticTaskMapper domesticTaskMapper;
    
    @Resource
    private SettlementMapper settlementMapper;
    
    @Resource
    private OrderMapper orderMapper;
    
    public AuntWorkbenchInfoRespVO getWorkbenchInfo(String auntOneId) {
        // 获取今日排班数
        Integer todaySchedule = getTodayScheduleCount(auntOneId);
        
        // 获取本月预估收入
        BigDecimal monthlyIncome = getMonthlyIncome(auntOneId);
        
        // 获取本月服务时长
        Integer monthlyServiceHours = getMonthlyServiceHours(auntOneId);
        
        // 获取各状态订单数量
        Integer pending = getOrderCountByStatus(auntOneId, "pending");
        Integer inProgress = getOrderCountByStatus(auntOneId, "in_progress");
        Integer completed = getOrderCountByStatus(auntOneId, "completed");
        Integer cancelled = getOrderCountByStatus(auntOneId, "cancelled");
        
        // 组装返回数据...
    }
}
```

---

## 注意事项

1. **性能考虑**: 对于大数据量的查询，建议添加适当的数据库索引
2. **数据一致性**: 确保查询的时间范围和数据状态的一致性
3. **扩展性**: 方法设计考虑了未来可能的扩展需求
4. **安全性**: 所有查询都使用了参数化查询，防止SQL注入攻击
5. **维护性**: SQL语句直接写在Mapper接口中，便于维护和调试
6. **测试性**: 每个Mapper方法都可以独立进行单元测试
7. **兼容性**: 使用注解方式避免了XML配置的兼容性问题 