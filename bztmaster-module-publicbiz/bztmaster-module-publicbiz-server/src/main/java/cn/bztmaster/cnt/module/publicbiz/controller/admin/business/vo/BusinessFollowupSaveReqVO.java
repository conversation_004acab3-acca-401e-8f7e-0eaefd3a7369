package cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Schema(description = "商机中心 - 商机跟进新增/更新 Request VO")
public class BusinessFollowupSaveReqVO {
    private Long id;
    private Long tenantId;
    @NotNull(message = "商机ID不能为空")
    private Long businessId;
    @NotBlank(message = "跟进内容不能为空")
    private String content;
    private Date followTime;
    private Long followUserId;
    private String followUserName;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private Boolean deleted;
} 