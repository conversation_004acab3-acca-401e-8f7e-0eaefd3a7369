package cn.bztmaster.cnt.module.publicbiz.service.employer.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsHomeRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsIncrementViewRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsListRespVO;
import cn.bztmaster.cnt.module.publicbiz.convert.employer.CarouselConvert;
import cn.bztmaster.cnt.module.publicbiz.convert.employer.NewsConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.CarouselDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.NewsDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.NewsMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employer.NewsService;
import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.exception.ErrorCodeConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 资讯 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class NewsServiceImpl implements NewsService {

    @Resource
    private NewsMapper newsMapper;

    // ==================== 管理后台接口实现 ====================

    @Override
    public Long createNews(AdminNewsSaveReqVO createReqVO) {
        // 参数验证
        if (createReqVO == null) {
            throw new IllegalArgumentException("创建请求不能为空");
        }

        // 检查资讯标题是否已存在
        NewsDO existingNews = newsMapper.selectByNewsTitle(createReqVO.getNewsTitle());
        if (existingNews != null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NEWS_TITLE_EXISTS);
        }

        // 创建资讯DO
        NewsDO news = BeanUtils.toBean(createReqVO, NewsDO.class);
        news.setViewCount(0);
        news.setLikeCount(0);
        news.setShareCount(0);
        news.setCommentCount(0L);
        if (news.getSort() == null) {
            news.setSort(0);
        }

        // 保存到数据库
        newsMapper.insert(news);
        return news.getId();
    }

    @Override
    public void updateNews(AdminNewsSaveReqVO updateReqVO) {
        // 参数验证
        if (updateReqVO == null || updateReqVO.getId() == null) {
            throw new IllegalArgumentException("更新请求不能为空且ID不能为空");
        }

        // 检查资讯是否存在
        NewsDO existingNews = newsMapper.selectById(updateReqVO.getId());
        if (existingNews == null || existingNews.getDeleted()) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NEWS_NOT_EXISTS);
        }

        // 检查资讯标题是否已存在（排除自己）
        NewsDO duplicateNews = newsMapper.selectByNewsTitleExcludeId(updateReqVO.getNewsTitle(), updateReqVO.getId());
        if (duplicateNews != null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NEWS_TITLE_EXISTS);
        }

        // 更新资讯DO
        NewsDO news = BeanUtils.toBean(updateReqVO, NewsDO.class);
        news.setUpdateTime(LocalDateTime.now());

        // 更新到数据库
        newsMapper.updateById(news);
    }

    @Override
    public void deleteNews(Long id) {
        // 参数验证
        if (id == null) {
            throw new IllegalArgumentException("资讯ID不能为空");
        }

        // 检查资讯是否存在
        NewsDO existingNews = newsMapper.selectById(id);
        if (existingNews == null || existingNews.getDeleted()) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NEWS_NOT_EXISTS);
        }

        // 逻辑删除
        newsMapper.deleteById(id);
    }

    @Override
    public AdminNewsRespVO getNews(Long id) {
        // 参数验证
        if (id == null) {
            throw new IllegalArgumentException("资讯ID不能为空");
        }

        // 查询资讯详情
        NewsDO news = newsMapper.selectById(id);
        if (news == null || news.getDeleted()) {
            throw new RuntimeException("资讯不存在或已删除");
        }

        // 转换为VO
        return BeanUtils.toBean(news, AdminNewsRespVO.class);
    }

    @Override
    public PageResult<AdminNewsRespVO> getNewsPage(AdminNewsPageReqVO pageReqVO) {
        // 参数验证
        if (pageReqVO == null) {
            throw new IllegalArgumentException("分页查询请求不能为空");
        }

        // 查询资讯分页列表
        PageResult<NewsDO> pageResult = newsMapper.selectAdminNewsPage(pageReqVO);

        return NewsConvert.INSTANCE.convertAdminPage(pageResult);
    }

    @Override
    public void updateNewsStatus(Long id, String status) {
        // 参数验证
        if (id == null) {
            throw new IllegalArgumentException("资讯ID不能为空");
        }
        if (status == null) {
            throw new IllegalArgumentException("状态不能为空");
        }

        // 检查资讯是否存在
        NewsDO existingNews = newsMapper.selectById(id);
        if (existingNews == null || existingNews.getDeleted()) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NEWS_NOT_EXISTS);
        }

        // 验证状态值
        if (!"published".equals(status) && !"offline".equals(status)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NEWS_STATUS_INVALID);
        }

        // 更新状态
        NewsDO news = new NewsDO();
        news.setId(id);
        news.setStatus(status);
        news.setUpdateTime(LocalDateTime.now());
        if ("published".equals(status) && existingNews.getPublishTime() == null) {
            news.setPublishTime(LocalDateTime.now());
        }

        // 更新到数据库
        newsMapper.updateById(news);
    }

    // ==================== 前端接口实现 ====================

    @Override
    public NewsHomeRespVO getHomeNewsList(Integer limit) {
        // 参数验证
        if (limit == null || limit <= 0) {
            limit = 4; // 默认返回4条
        }
        if (limit > 10) {
            limit = 10; // 最大返回10条
        }

        // 查询首页资讯列表
        List<NewsDO> newsList = newsMapper.selectHomeNewsList(limit);

        // 转换为VO
        List<NewsHomeRespVO.NewsItem> items = NewsConvert.INSTANCE.convertHomeItems(newsList);

        NewsHomeRespVO result = new NewsHomeRespVO();
        result.setList(items);
        return result;
    }

    @Override
    public NewsListRespVO getNewsPage(PageParam pageParam, Long categoryId, String keyword, String status) {
        // 参数验证
        if (pageParam == null) {
            pageParam = new PageParam();
        }
        if (pageParam.getPageSize() != null && pageParam.getPageSize() > 50) {
            pageParam.setPageSize(50); // 最大每页50条
        }

        // 查询资讯分页列表
        PageResult<NewsDO> pageResult = newsMapper.selectNewsPage(pageParam, categoryId, keyword, status);

        // 转换为VO
        PageResult<NewsListRespVO.NewsItem> convertedPage = NewsConvert.INSTANCE.convertPage(pageResult);
        NewsListRespVO result = new NewsListRespVO();
        result.setList(convertedPage.getList());
        result.setTotal(convertedPage.getTotal());
        result.setPage(pageParam.getPageNo());
        result.setPageSize(pageParam.getPageSize());
        result.setTotalPages((int) Math.ceil((double) convertedPage.getTotal() / pageParam.getPageSize()));
        return result;
    }

    @Override
    public NewsDetailRespVO getNewsDetail(Long id) {
        // 参数验证
        if (id == null) {
            throw new IllegalArgumentException("资讯ID不能为空");
        }

        // 查询资讯详情
        NewsDO news = newsMapper.selectById(id);
        if (news == null || news.getDeleted()) {
            throw new RuntimeException("资讯不存在或已删除");
        }

        // 检查状态
        if (!"published".equals(news.getStatus())) {
            throw new RuntimeException("资讯未发布或已下架");
        }

        // 转换为VO
        return NewsConvert.INSTANCE.convertDetail(news);
    }

    @Override
    public NewsIncrementViewRespVO incrementViewCount(Long id) {
        // 参数验证
        if (id == null) {
            throw new IllegalArgumentException("资讯ID不能为空");
        }

        // 检查资讯是否存在
        NewsDO news = newsMapper.selectById(id);
        if (news == null || news.getDeleted()) {
            throw new RuntimeException("资讯不存在或已删除");
        }

        // 增加浏览次数
        int updatedRows = newsMapper.incrementViewCount(id);
        if (updatedRows == 0) {
            throw new RuntimeException("增加浏览次数失败");
        }

        // 返回更新后的浏览次数
        NewsIncrementViewRespVO result = new NewsIncrementViewRespVO();
        result.setViewCount(news.getViewCount() + 1);
        return result;
    }

}