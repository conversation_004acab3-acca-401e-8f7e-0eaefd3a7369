package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment;

import cn.bztmaster.cnt.module.publicbiz.api.employment.dto.*;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.WorkOrderConvert;
import cn.bztmaster.cnt.module.publicbiz.service.employment.WorkOrderService;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 任务工单管理 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 任务工单管理")
@RestController
@RequestMapping("/publicbiz/work-order")
@Validated
@Slf4j
public class WorkOrderController {

    @Resource
    private WorkOrderService workOrderService;

    @PostMapping("/page")
    @Operation(summary = "分页查询工单列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:query')")
    public CommonResult<PageResult<WorkOrderRespVO>> pageWorkOrders(@Valid @RequestBody WorkOrderPageReqVO reqVO) {
        // 调用服务，直接传递VO参数
        PageResult<WorkOrderRespVO> pageResult = workOrderService.pageWorkOrders(reqVO);
        
        return success(pageResult);
    }

    @GetMapping("/detail/{workOrderNo}")
    @Operation(summary = "根据工单编号查询工单详情")
    @Parameter(name = "workOrderNo", description = "工单编号", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:query')")
    public CommonResult<WorkOrderRespVO> getWorkOrderDetail(@PathVariable("workOrderNo") String workOrderNo) {
        // 调用服务，直接返回VO
        WorkOrderRespVO respVO = workOrderService.getWorkOrderDetail(workOrderNo);
        
        return success(respVO);
    }

    @PostMapping("/accept")
    @Operation(summary = "接单处理工单")
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:accept')")
    public CommonResult<WorkOrderAcceptRespVO> acceptWorkOrder(@Valid @RequestBody WorkOrderAcceptReqVO reqVO) {
        // 调用服务，直接传递VO参数
        WorkOrderAcceptRespVO respVO = workOrderService.acceptWorkOrder(reqVO);
        
        return success(respVO);
    }

    @PostMapping("/transfer")
    @Operation(summary = "转派工单")
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:transfer')")
    public CommonResult<WorkOrderTransferRespVO> transferWorkOrder(@Valid @RequestBody WorkOrderTransferReqVO reqVO) {
        // 调用服务，直接传递VO参数
        WorkOrderTransferRespVO respVO = workOrderService.transferWorkOrder(reqVO);
        
        return success(respVO);
    }

    @GetMapping("/logs/{workOrderNo}")
    @Operation(summary = "查询工单处理日志")
    @Parameter(name = "workOrderNo", description = "工单编号", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:query')")
    public CommonResult<List<WorkOrderLogRespVO>> getWorkOrderLogs(@PathVariable("workOrderNo") String workOrderNo) {
        // 调用服务，直接返回VO
        List<WorkOrderLogRespVO> logList = workOrderService.getWorkOrderLogs(workOrderNo);
        
        return success(logList);
    }



    @GetMapping("/attachments/{workOrderNo}")
    @Operation(summary = "查询工单附件列表")
    @Parameter(name = "workOrderNo", description = "工单编号", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:query')")
    public CommonResult<List<WorkOrderAttachmentRespVO>> getAttachments(@PathVariable("workOrderNo") String workOrderNo) {
        // 调用服务，直接返回VO
        List<WorkOrderAttachmentRespVO> pageResult = workOrderService.getAttachments(workOrderNo);
        
        return success(pageResult);
    }

    @GetMapping("/workOrderPractitioners")
    @Operation(summary = "获取待岗阿姨列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:query')")
    public CommonResult<List<WorkOrderPractitionerRespVO>> getWorkOrderPractitioners() {
        // 调用服务，直接返回VO
        List<WorkOrderPractitionerRespVO> practitionerList = workOrderService.getWorkOrderPractitioners();
        
        return success(practitionerList);
    }

    @GetMapping("/taskDetail/{orderNo}")
    @Operation(summary = "获取服务任务详情")
    @Parameter(name = "orderNo", description = "订单号", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:query')")
    public CommonResult<WorkOrderTaskDetailRespVO> getTaskDetail(@PathVariable("orderNo") String orderNo) {
        // 调用服务，直接返回VO
        WorkOrderTaskDetailRespVO respVO = workOrderService.getTaskDetail(orderNo);
        
        return success(respVO);
    }

    @PutMapping("/taskList/{orderNo}")
    @Operation(summary = "获取任务列表")
    @Parameter(name = "orderNo", description = "订单号", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:query')")
    public CommonResult<List<WorkOrderTaskRespVO>> getTaskList(
            @PathVariable("orderNo") String orderNo,
            @RequestBody WorkOrderTaskListReqVO reqVO) {
        
        // 调用服务，直接返回VO
        List<WorkOrderTaskRespVO> taskList = workOrderService.getTaskList(orderNo, reqVO.getTaskStatus(), reqVO.getPractitionerName(), reqVO.getPractitionerOneid());
        
        return success(taskList);
    }

    @PostMapping("/reassignTask")
    @Operation(summary = "重新指派任务")
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:reassign')")
    public CommonResult<WorkOrderReassignTaskRespVO> reassignTask(@Valid @RequestBody WorkOrderReassignTaskReqVO reqVO) {
        // 调用服务，直接传递VO参数
        WorkOrderReassignTaskRespVO respVO = workOrderService.reassignTask(reqVO);
        
        return success(respVO);
    }

    @PostMapping("/submitResolution")
    @Operation(summary = "提交工单处理结果")
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:submit')")
    public CommonResult<WorkOrderSubmitResolutionRespVO> submitResolution(@Valid @RequestBody WorkOrderSubmitResolutionReqVO reqVO) {
        // 调用服务，直接传递VO参数
        WorkOrderSubmitResolutionRespVO respVO = workOrderService.submitResolution(reqVO);
        
        return success(respVO);
    }

    @PostMapping("/assignAunt")
    @Operation(summary = "指派阿姨")
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:assign')")
    public CommonResult<WorkOrderAssignAuntRespVO> assignAunt(@Valid @RequestBody WorkOrderAssignAuntReqVO reqVO) {
        // 调用服务，直接传递VO参数
        WorkOrderAssignAuntRespVO respVO = workOrderService.assignAunt(reqVO);
        
        return success(respVO);
    }

    @GetMapping("/typeStats")
    @Operation(summary = "获取工单类型统计")
    @PreAuthorize("@ss.hasPermission('publicbiz:work-order:query')")
    public CommonResult<WorkOrderTypeStatsRespVO> getTypeStats() {
        // 调用服务，直接返回VO
        WorkOrderTypeStatsRespVO respVO = workOrderService.getTypeStats();
        
        return success(respVO);
    }
} 