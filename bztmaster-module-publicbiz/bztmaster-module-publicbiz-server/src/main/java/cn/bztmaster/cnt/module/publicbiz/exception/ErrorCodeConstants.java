package cn.bztmaster.cnt.module.publicbiz.exception;

import cn.bztmaster.cnt.framework.common.exception.ErrorCode;

/**
 * 公共业务模块错误码枚举类
 *
 * 公共业务模块，使用 1-001-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 资讯管理 1-001-001-000 ==========
    ErrorCode NEWS_NOT_EXISTS = new ErrorCode(1_001_001_000, "资讯不存在");
    ErrorCode NEWS_TITLE_EXISTS = new ErrorCode(1_001_001_001, "资讯标题已存在");
    ErrorCode NEWS_STATUS_INVALID = new ErrorCode(1_001_001_002, "资讯状态无效");
    ErrorCode NEWS_CATEGORY_NOT_EXISTS = new ErrorCode(1_001_001_003, "资讯分类不存在");
    ErrorCode NEWS_MATERIAL_NOT_EXISTS = new ErrorCode(1_001_001_004, "关联素材文章不存在");
    ErrorCode NEWS_OPERATION_NOT_ALLOWED = new ErrorCode(1_001_001_005, "当前资讯状态不允许该操作");

} 