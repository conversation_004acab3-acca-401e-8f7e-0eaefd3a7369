package cn.bztmaster.cnt.module.publicbiz.controller.admin.employer;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.CarouselAdminRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminCarouselSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminCarouselPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.CarouselStatusUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.CarouselService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 轮播图
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 轮播图")
@RestController
@RequestMapping("/publicbiz/carousel")
@Validated
public class AdminCarouselController {

    @Resource
    private CarouselService carouselService;

    @PostMapping("/create")
    @Operation(summary = "创建轮播图")
    @PreAuthorize("@ss.hasPermission('publicbiz:carousel:create')")
    public CommonResult<Long> createCarousel(@Valid @RequestBody AdminCarouselSaveReqVO createReqVO) {
        return success(carouselService.createCarousel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新轮播图")
    @PreAuthorize("@ss.hasPermission('publicbiz:carousel:update')")
    public CommonResult<Boolean> updateCarousel(@Valid @RequestBody AdminCarouselSaveReqVO updateReqVO) {
        carouselService.updateCarousel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除轮播图")
    @Parameter(name = "id", description = "轮播图ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:carousel:delete')")
    public CommonResult<Boolean> deleteCarousel(@PathVariable("id") Long id) {
        carouselService.deleteCarousel(id);
        return success(true);
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "获得轮播图")
    @Parameter(name = "id", description = "轮播图ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:carousel:query')")
    public CommonResult<CarouselAdminRespVO> getCarousel(@PathVariable("id") Long id) {
        CarouselAdminRespVO carousel = carouselService.getCarousel(id);
        return success(carousel);
    }

    @GetMapping("/page")
    @Operation(summary = "获得轮播图分页")
    @PreAuthorize("@ss.hasPermission('publicbiz:carousel:query')")
    public CommonResult<PageResult<CarouselAdminRespVO>> getCarouselPage(@Valid AdminCarouselPageReqVO pageReqVO) {
        PageResult<CarouselAdminRespVO> pageResult = carouselService.getCarouselPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/list")
    @Operation(summary = "获得轮播图列表")
    @Parameter(name = "platform", description = "平台：employer-雇主端，aunt-阿姨端", required = true, example = "employer")
    @PreAuthorize("@ss.hasPermission('publicbiz:carousel:query')")
    public CommonResult<List<CarouselAdminRespVO>> getCarouselList(@RequestParam("platform") String platform) {
        List<CarouselAdminRespVO> list = carouselService.getCarouselList(platform);
        return success(list);
    }

    @PutMapping("/updateStatus")
    @Operation(summary = "更新轮播图状态")
    @PreAuthorize("@ss.hasPermission('publicbiz:carousel:update')")
    public CommonResult<Boolean> updateCarouselStatus(@Valid @RequestBody CarouselStatusUpdateReqVO reqVO) {
        carouselService.updateCarouselStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }
} 