-- 商机主表
CREATE TABLE `publicbiz_business` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `name` VARCHAR(128) NOT NULL COMMENT '商机名称',
  `customer_id` BIGINT  COMMENT '关联客户ID',
  `customer_name` VARCHAR(128) NOT NULL COMMENT '关联客户名称',
  `business_type` VARCHAR(32) NOT NULL COMMENT '业务模块（高校/培训/认证）',
  `total_price` DECIMAL(18,2) DEFAULT 0 COMMENT '商机金额(元)',
  `business_stage` VARCHAR(32) NOT NULL COMMENT '销售阶段（方案报价/商务谈判/需求分析）',
  `owner_user_id` BIGINT NOT NULL COMMENT '销售负责人ID',
  `owner_user_name` VARCHAR(64) NOT NULL COMMENT '销售负责人姓名',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  UNIQUE KEY `uk_business_name` (`name`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_owner_user_id` (`owner_user_id`)
) COMMENT='商机主表';

-- 商机跟进表
CREATE TABLE `publicbiz_business_followup` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `business_id` BIGINT NOT NULL COMMENT '商机ID',
  `content` TEXT NOT NULL COMMENT '跟进内容',
  `follow_time` DATETIME NOT NULL COMMENT '跟进时间',
  `follow_user_id` BIGINT NOT NULL COMMENT '跟进人ID',
  `follow_user_name` VARCHAR(64) NOT NULL COMMENT '跟进人姓名',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  KEY `idx_business_id` (`business_id`)
) COMMENT='商机跟进表';

-- 商机操作日志表（可选）
CREATE TABLE `publicbiz_business_log` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `business_id` BIGINT NOT NULL COMMENT '商机ID',
  `action` VARCHAR(32) NOT NULL COMMENT '操作类型（新建/编辑/删除/跟进等）',
  `content` TEXT COMMENT '详情',
  `action_time` DATETIME NOT NULL COMMENT '操作时间',
  `action_user_id` BIGINT NOT NULL COMMENT '操作人ID',
  `action_user_name` VARCHAR(64) NOT NULL COMMENT '操作人姓名',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  KEY `idx_business_id` (`business_id`)
) COMMENT='商机操作日志表';

ALTER TABLE `publicbiz_business`
  ADD COLUMN `expected_deal_date` DATE COMMENT '预计成交日期' AFTER `total_price`,
  ADD COLUMN `description` TEXT COMMENT '商机描述' AFTER `business_stage`;