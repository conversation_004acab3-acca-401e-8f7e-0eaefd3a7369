package cn.bztmaster.cnt.module.publicbiz.controller.admin.business;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.business.BusinessLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/publicbiz/business/log")
@Tag(name = "商机中心-商机日志管理")
public class BusinessLogController {

    @Resource
    private BusinessLogService businessLogService;

    @GetMapping("/list")
    @Operation(summary = "商机日志分页列表")
    public CommonResult<PageResult<BusinessLogRespVO>> list(BusinessLogPageReqVO reqVO) {
        return CommonResult.success(businessLogService.getBusinessLogPage(reqVO));
    }

    @PostMapping("/create")
    @Operation(summary = "新增商机日志")
    public CommonResult<Long> create(@RequestBody BusinessLogSaveReqVO reqVO) {
        return CommonResult.success(businessLogService.createBusinessLog(reqVO));
    }

    @GetMapping("/detail")
    @Operation(summary = "商机日志详情")
    public CommonResult<BusinessLogRespVO> detail(@RequestParam("id") Long id) {
        return CommonResult.success(businessLogService.getBusinessLogDetail(id));
    }
} 