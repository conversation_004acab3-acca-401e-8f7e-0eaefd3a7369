package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.mzt.logapi.starter.annotation.DiffLogField;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.DecimalMax;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "就业服务-阿姨更新 Request VO")
public class PractitionerUpdateReqVO {
    @Schema(description = "阿姨ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "阿姨ID不能为空")
    private Long id;

    @Schema(description = "阿姨姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "阿姨姓名不能为空")
    @DiffLogField(name = "阿姨姓名")
    private String name;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "手机号不能为空")
    @DiffLogField(name = "手机号")
    private String phone;

    @Schema(description = "身份证号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "身份证号不能为空")
    @DiffLogField(name = "身份证号")
    private String idCard;

    @Schema(description = "籍贯")
    @DiffLogField(name = "籍贯")
    private String hometown;

    @Schema(description = "年龄")
    @DiffLogField(name = "年龄")
    private Integer age;

    @Schema(description = "性别")
    @DiffLogField(name = "性别")
    private String gender;

    @Schema(description = "头像URL")
    @DiffLogField(name = "头像")
    private String avatar;

    @Schema(description = "主要服务类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "主要服务类型不能为空")
    @DiffLogField(name = "主要服务类型")
    private String serviceType;

    @Schema(description = "从业年限", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "从业年限不能为空")
    @DiffLogField(name = "从业年限")
    private Integer experienceYears;

    @Schema(description = "平台状态")
    @DiffLogField(name = "平台状态")
    private String platformStatus;

    @Schema(description = "评级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "评级不能为空")
    @DecimalMin(value = "1.0", message = "评级不能小于1.0")
    @DecimalMax(value = "5.0", message = "评级不能大于5.0")
    @DiffLogField(name = "评级")
    private BigDecimal rating;

    @Schema(description = "所属机构ID")
    @DiffLogField(name = "所属机构ID")
    private Long agencyId;

    @Schema(description = "所属机构名称")
    @DiffLogField(name = "所属机构名称")
    private String agencyName;

    @Schema(description = "状态")
    @DiffLogField(name = "状态")
    private String status;

    @Schema(description = "当前状态")
    @DiffLogField(name = "当前状态")
    private String currentStatus;

    @Schema(description = "当前服务订单ID")
    @DiffLogField(name = "当前服务订单ID")
    private String currentOrderId;

    @Schema(description = "累计服务单数")
    @DiffLogField(name = "累计服务单数")
    private Integer totalOrders;

    @Schema(description = "累计收入")
    @DiffLogField(name = "累计收入")
    private BigDecimal totalIncome;

    @Schema(description = "客户满意度评分")
    @DiffLogField(name = "客户满意度评分")
    private BigDecimal customerSatisfaction;

    @Schema(description = "资质文件列表")
    // 不参与 _DIFF 处理，由自定义逻辑处理
    private List<PractitionerQualificationSaveReqVO> qualifications;
} 