package cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplatePageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.convert.certificateTemplate.CertificateTemplateConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.certificateTemplate.CertificateTemplateDO;
import cn.bztmaster.cnt.module.publicbiz.service.certificateTemplate.CertificateTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 证书模板
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 证书模板")
@RestController
@RequestMapping("/publicbiz/certificate/template")
@Validated
public class CertificateTemplateController {

    @Resource
    private CertificateTemplateService certificateTemplateService;

    @GetMapping("/page")
    @Operation(summary = "分页查询证书模板")
    @PreAuthorize("@ss.hasPermission('publicbiz:certificate-template:query')")
    public CommonResult<PageResult<CertificateTemplateRespVO>> pageCertificateTemplate(@Valid CertificateTemplatePageReqVO pageReqVO) {
        PageResult<CertificateTemplateDO> pageResult = certificateTemplateService.getCertificateTemplatePage(pageReqVO);
        return success(CertificateTemplateConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/detail")
    @Operation(summary = "获得证书模板详情")
    @Parameter(name = "id", description = "模板编号", example = "1024", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:certificate-template:query')")
    public CommonResult<CertificateTemplateRespVO> getCertificateTemplateDetail(@RequestParam("id") Long id) {
        CertificateTemplateDO certificateTemplate = certificateTemplateService.getCertificateTemplate(id);
        return success(CertificateTemplateConvert.INSTANCE.convert(certificateTemplate));
    }

    @PostMapping("/create")
    @Operation(summary = "创建证书模板")
    @PreAuthorize("@ss.hasPermission('publicbiz:certificate-template:create')")
    public CommonResult<Long> createCertificateTemplate(@Valid @RequestBody CertificateTemplateSaveReqVO createReqVO) {
        return success(certificateTemplateService.createCertificateTemplate(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新证书模板")
    @PreAuthorize("@ss.hasPermission('publicbiz:certificate-template:update')")
    public CommonResult<Boolean> updateCertificateTemplate(@Valid @RequestBody CertificateTemplateSaveReqVO updateReqVO) {
        certificateTemplateService.updateCertificateTemplate(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除证书模板")
    @Parameter(name = "id", description = "模板编号", example = "1024", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:certificate-template:delete')")
    public CommonResult<Boolean> deleteCertificateTemplate(@RequestParam("id") Long id) {
        certificateTemplateService.deleteCertificateTemplate(id);
        return success(true);
    }

    @PostMapping("/update-status")
    @Operation(summary = "更新证书模板状态")
    @PreAuthorize("@ss.hasPermission('publicbiz:certificate-template:update')")
    public CommonResult<Boolean> updateCertificateTemplateStatus(@RequestParam("id") Long id, @RequestParam("status") String status) {
        certificateTemplateService.updateCertificateTemplateStatus(id, status);
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "获得证书模板列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:certificate-template:query')")
    public CommonResult<List<CertificateTemplateRespVO>> listCertificateTemplate(@Valid CertificateTemplateListReqVO listReqVO) {
        List<CertificateTemplateDO> list = certificateTemplateService.getCertificateTemplateList(listReqVO);
        return success(CertificateTemplateConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/statistics")
    @Operation(summary = "获得证书模板统计")
    @PreAuthorize("@ss.hasPermission('publicbiz:certificate-template:query')")
    public CommonResult<CertificateTemplateStatisticsRespVO> getCertificateTemplateStatistics() {
        return success(certificateTemplateService.getCertificateTemplateStatistics());
    }

}