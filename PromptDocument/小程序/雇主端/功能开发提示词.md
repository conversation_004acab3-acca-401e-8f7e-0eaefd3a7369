1. 小程序授权登录需求说明
    ### 一键授权 

    @一键授权手机号验证码登录接口文档.md 小程序需要实现一键授权登录和手机号发送验证码登录授权的功能。
        接口地址：
            一键授权 /publicbiz/employer/auth/weixin-mini-app-login 
            手机号发送验证码登录授权 /publicbiz/employer/auth/sms-login  
            发送验证码和校验验证码功能参考cn/bztmaster/cnt/module/member/controller/app/auth/AppAuthController.java文件中的方法：/member/auth/send-sms-code、/member/auth/validate-sms-code

2. 一键授权和手机验证码登录接口需要实现的功能如下：
    1.  需要给小程序用户表新增或更新一条信息，根据openid查找是否存在用户信息，不存在则新增；
        小程序用户表结构如下：
        CREATE TABLE `mp_user` (
                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                    `oneid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'OneID GUID',
                    `openid` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户标识',
                    `union_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信生态唯一标识',
                    `subscribe_status` tinyint(4) NOT NULL COMMENT '关注状态',
                    `subscribe_time` datetime NOT NULL COMMENT '关注时间',
                    `mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号（仅小程序使用）',
                    `nickname` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
                    `head_image_url` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像地址',
                    `unsubscribe_time` datetime DEFAULT NULL COMMENT '取消关注时间',
                    `language` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言',
                    `country` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家',
                    `province` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '省份',
                    `city` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '城市',
                    `remark` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                    `tag_ids` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签编号数组',
                    `account_id` bigint(20) NOT NULL COMMENT '微信公众号ID',
                    `app_id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '微信公众号 appid',
                    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                    PRIMARY KEY (`id`) USING BTREE
                    ) ENGINE=InnoDB AUTO_INCREMENT=57 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信用户表（小程序+公众号）';