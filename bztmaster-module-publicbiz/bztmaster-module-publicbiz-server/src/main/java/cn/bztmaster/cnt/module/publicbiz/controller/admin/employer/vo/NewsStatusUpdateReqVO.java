package cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 资讯状态更新请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "资讯状态更新请求 VO")
@Data
public class NewsStatusUpdateReqVO {

    @Schema(description = "资讯ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "资讯ID不能为空")
    private Long id;

    @Schema(description = "目标状态：published-已发布/offline-已下架", requiredMode = Schema.RequiredMode.REQUIRED, example = "published")
    @NotBlank(message = "状态不能为空")
    private String status;
} 