package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "就业服务-服务套餐特色标签保存 Request VO")
public class ServicePackageFeatureSaveReqVO {
    @Schema(description = "特色标签ID（更新时使用）")
    private Long id;

    @Schema(description = "特色标签名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "特色标签名称不能为空")
    private String featureName;

    @Schema(description = "排序")
    private Integer sortOrder;
} 