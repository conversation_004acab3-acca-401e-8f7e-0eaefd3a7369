package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "就业服务-服务套餐审核状态 Request VO")
public class ServicePackageAuditStatusReqVO {
    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "审核状态不能为空")
    private String auditStatus;

    @Schema(description = "拒绝原因")
    private String rejectReason;
}