package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class PractitionerExcelVO {
    @ExcelProperty("阿姨姓名")
    private String name;

    @ExcelProperty("综合评级")
    private String rating;

    @ExcelProperty("服务类型")
    private String serviceType;

    @ExcelProperty("累计单数")
    private Integer totalOrders;

    @ExcelProperty("当前状态")
    private String currentStatus;

    @ExcelProperty("平台状态")
    private String platformStatus;
} 