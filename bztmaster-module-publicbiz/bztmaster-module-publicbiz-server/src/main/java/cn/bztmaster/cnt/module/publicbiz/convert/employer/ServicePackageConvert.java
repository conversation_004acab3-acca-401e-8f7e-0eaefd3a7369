package cn.bztmaster.cnt.module.publicbiz.convert.employer;

import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServicePackageRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

/**
 * 服务套餐 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ServicePackageConvert {

    ServicePackageConvert INSTANCE = Mappers.getMapper(ServicePackageConvert.class);

    @Mapping(target = "id", source = "bean.id")
    @Mapping(target = "status", source = "bean.status")
    @Mapping(target = "agencyName", source = "agency.agencyName")
    @Mapping(target = "agencyLongitude", source = "agency.longitude")
    @Mapping(target = "agencyLatitude", source = "agency.latitude")
    @Mapping(target = "partnerId", source = "bean.partnerId")
    @Mapping(target = "partnerName", source = "bean.partnerName")
    ServicePackageRespVO convert(ServicePackageDO bean, AgencyDO agency, List<String> features, Double distance,
            BigDecimal satisfactionRate);

}
