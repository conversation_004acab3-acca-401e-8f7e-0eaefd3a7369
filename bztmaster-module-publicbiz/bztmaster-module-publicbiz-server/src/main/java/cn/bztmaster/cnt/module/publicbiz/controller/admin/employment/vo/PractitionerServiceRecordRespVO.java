package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "就业服务-阿姨服务记录 Response VO")
public class PractitionerServiceRecordRespVO {
    @Schema(description = "服务记录ID")
    private Long id;

    @Schema(description = "阿姨ID")
    private Long practitionerId;

    @Schema(description = "订单ID")
    private String orderId;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "服务类型")
    private String serviceType;

    @Schema(description = "服务开始时间")
    private Date serviceStartTime;

    @Schema(description = "服务结束时间")
    private Date serviceEndTime;

    @Schema(description = "服务时长")
    private String serviceDuration;

    @Schema(description = "服务地址")
    private String serviceAddress;

    @Schema(description = "服务金额")
    private BigDecimal serviceAmount;

    @Schema(description = "阿姨收入")
    private BigDecimal practitionerIncome;

    @Schema(description = "平台收入")
    private BigDecimal platformIncome;

    @Schema(description = "订单状态")
    private String orderStatus;

    @Schema(description = "客户评分")
    private BigDecimal customerRating;

    @Schema(description = "客户评价")
    private String customerComment;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
} 