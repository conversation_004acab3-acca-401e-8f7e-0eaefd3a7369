package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

/**
 * 课程附件 - Response VO
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/components/ManagementCourse.vue
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "课程附件 - Response VO")
public class CourseAttachmentRespVO {

    @Schema(description = "附件ID", example = "1")
    private Long id;

    @Schema(description = "课程ID", example = "123")
    private Long courseId;

    @Schema(description = "附件名称", example = "开课须知.md")
    private String attachmentName;

    @Schema(description = "附件类型", example = "文档")
    private String attachmentType;

    @Schema(description = "文件URL", example = "https://example.com/notice.md")
    private String fileUrl;

    @Schema(description = "文件大小", example = "2048")
    private Long fileSize;

    @Schema(description = "创建时间")
    private Date createTime;
}
