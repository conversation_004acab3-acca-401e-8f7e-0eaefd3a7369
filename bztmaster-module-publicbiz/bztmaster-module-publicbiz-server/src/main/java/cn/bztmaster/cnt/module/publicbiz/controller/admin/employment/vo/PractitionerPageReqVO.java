package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "就业服务-阿姨分页 Request VO")
public class PractitionerPageReqVO extends PageParam {
    @Schema(description = "阿姨姓名或手机号关键词")
    private String keyword;

    @Schema(description = "服务类型")
    private String serviceType;

    @Schema(description = "平台状态")
    private String platformStatus;

    @Schema(description = "评级")
    private String rating;

    @Schema(description = "机构ID")
    private Long agencyId;
} 