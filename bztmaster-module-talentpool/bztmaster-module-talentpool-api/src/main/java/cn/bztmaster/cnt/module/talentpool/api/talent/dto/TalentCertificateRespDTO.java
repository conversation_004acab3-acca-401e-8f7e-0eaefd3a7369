package cn.bztmaster.cnt.module.talentpool.api.talent.dto;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 人才资质证书响应 DTO
 *
 * <AUTHOR>
 */
@Data
public class TalentCertificateRespDTO {

    /**
     * 资质证书列表
     */
    private List<Certificate> certificates;

    /**
     * 资质证书
     */
    @Data
    public static class Certificate {
        /**
         * 证书ID
         */
        private Long certificateId;

        /**
         * 证书编号
         */
        private String certificateNo;

        /**
         * 证书名称
         */
        private String name;

        /**
         * 发证机构
         */
        private String issuer;

        /**
         * 颁发日期
         */
        private Date issueDate;

        /**
         * 到期时间
         */
        private Date expiryDate;

        /**
         * 记录来源（可选值：PLATFORM、AGENCY、SELF）
         */
        private String source;

        /**
         * 审核状态（可选值：VERIFIED、PENDING_VERIFICATION、REJECTED）
         */
        private String status;

        /**
         * 证书图片
         */
        private String certificateImageUrl;
    }
}
