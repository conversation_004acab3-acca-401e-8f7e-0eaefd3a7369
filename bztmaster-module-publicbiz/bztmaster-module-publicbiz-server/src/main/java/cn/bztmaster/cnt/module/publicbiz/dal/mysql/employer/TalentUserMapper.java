package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.TalentUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 人才用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TalentUserMapper extends BaseMapperX<TalentUserDO> {

    /**
     * 根据oneid查询用户信息
     *
     * @param oneid OneID
     * @return 用户信息
     */
    @Select("SELECT * FROM talent_user WHERE TRIM(oneid) = #{oneid} AND deleted = 0")
    TalentUserDO selectByOneid(@Param("oneid") String oneid);
}

