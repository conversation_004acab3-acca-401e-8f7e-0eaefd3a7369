package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import cn.bztmaster.cnt.module.publicbiz.util.DateOnlyLocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考题分类响应 VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "资源中心 - 考题分类响应 VO")
public class QuestionCategoryRespVO {

    /**
     * 分类ID
     */
    @Schema(description = "分类ID", example = "1")
    private Long id;

    /**
     * 一级分类名称
     */
    @Schema(description = "一级分类名称", example = "职业技能等级认定")
    private String level1Name;

    /**
     * 一级分类代码
     */
    @Schema(description = "一级分类代码", example = "ZY001")
    private String level1Code;

    /**
     * 二级分类名称
     */
    @Schema(description = "二级分类名称", example = "家政服务类")
    private String level2Name;

    /**
     * 二级分类代码
     */
    @Schema(description = "二级分类代码", example = "JZ001")
    private String level2Code;

    /**
     * 三级分类名称
     */
    @Schema(description = "三级分类名称", example = "家政服务员")
    private String level3Name;

    /**
     * 三级分类代码
     */
    @Schema(description = "三级分类代码", example = "JZFW001")
    private String level3Code;

    /**
     * 认定点名称
     */
    @Schema(description = "认定点名称", example = "职业道德基础")
    private String certName;

    /**
     * 认定点代码
     */
    @Schema(description = "认定点代码", example = "KP001")
    private String certCode;

    /**
     * 业务模块
     */
    @Schema(description = "业务模块", example = "家政业务")
    private String biz;

    /**
     * 业务模块名称
     */
    @Schema(description = "业务模块名称", example = "家政服务业务")
    private String bizName;

    /**
     * 父级分类ID
     */
    @Schema(description = "父级分类ID", example = "0")
    private Long parentId;

    /**
     * 分类层级：1-一级，2-二级，3-三级
     */
    @Schema(description = "分类层级", example = "1")
    private Integer level;

    /**
     * 排序序号
     */
    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;

    /**
     * 分类描述
     */
    @Schema(description = "分类描述", example = "职业技能等级认定相关分类")
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态", example = "1")
    private Integer status;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "admin")
    private String creator;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "王五老师")
    private String creatorName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2024-01-17")
    @JsonSerialize(using = DateOnlyLocalDateTimeSerializer.class)
    private LocalDateTime createTime;

}
