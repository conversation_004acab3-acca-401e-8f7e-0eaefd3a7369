package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "就业服务-服务套餐特色标签 Response VO")
public class ServicePackageFeatureRespVO {
    @Schema(description = "特色标签ID")
    private Long id;

    @Schema(description = "套餐ID")
    private Long packageId;

    @Schema(description = "特色标签名称")
    private String featureName;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
} 