package cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 轮播图管理后台响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 轮播图响应")
@Data
public class CarouselAdminRespVO {

    @Schema(description = "轮播图ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "平台：employer-雇主端，aunt-阿姨端", requiredMode = Schema.RequiredMode.REQUIRED, example = "employer")
    private String platform;

    @Schema(description = "轮播图标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "专业月嫂服务")
    private String carouselTitle;

    @Schema(description = "轮播图片URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com/images/carousel1.jpg")
    private String carouselImageUrl;

    @Schema(description = "跳转链接URL", example = "/service/maternity")
    private String carouselLinkUrl;

    @Schema(description = "排序值，数字越小越靠前", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer sortOrder;

    @Schema(description = "状态：1-启用，0-禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
    private LocalDateTime endTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;
} 