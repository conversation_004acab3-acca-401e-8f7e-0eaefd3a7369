package cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalTime;

import static cn.bztmaster.cnt.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 管理后台 - 场地预约新增/修改 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 场地预约新增/修改 Request VO")
@Data
public class SiteAppointmentSaveReqVO {

    @Schema(description = "预约ID", example = "1")
    private Long id;

    @Schema(description = "场地ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "场地ID不能为空")
    private Long siteId;

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "家政服务员技能培训")
    @NotEmpty(message = "活动名称不能为空")
    @Size(max = 30, message = "活动名称长度不能超过30个字符")
    private String activityName;

    @Schema(description = "活动类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "培训")
    @NotEmpty(message = "活动类型不能为空")
    private String activityType;

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-08-15")
    @NotNull(message = "开始日期不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-08-15")
    @NotNull(message = "结束日期不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "09:00")
    @NotNull(message = "开始时间不能为空")
    @DateTimeFormat(pattern = "HH:mm")
    private LocalTime startTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "17:00")
    @NotNull(message = "结束时间不能为空")
    @DateTimeFormat(pattern = "HH:mm")
    private LocalTime endTime;

    @Schema(description = "预计人数", requiredMode = Schema.RequiredMode.REQUIRED, example = "35")
    @NotNull(message = "预计人数不能为空")
    private Integer peopleCount;

    @Schema(description = "联系人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张老师")
    @NotEmpty(message = "联系人姓名不能为空")
    @Size(max = 10, message = "联系人姓名长度不能超过10个字符")
    private String contactName;

    @Schema(description = "联系电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "13812345678")
    @NotEmpty(message = "联系电话不能为空")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String contactPhone;

    @Schema(description = "预约状态", example = "已确认")
    private String status;

    @Schema(description = "备注信息", example = "需要投影设备和音响")
    @Size(max = 200, message = "备注信息长度不能超过200个字符")
    private String remark;

}
