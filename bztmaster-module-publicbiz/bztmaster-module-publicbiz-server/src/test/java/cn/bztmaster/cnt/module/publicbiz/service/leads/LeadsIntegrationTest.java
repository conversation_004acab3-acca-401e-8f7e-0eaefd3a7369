package cn.bztmaster.cnt.module.publicbiz.service.leads;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.test.core.ut.BaseDbUnitTest;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadFollowUpLogSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadsAssignReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadsPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadsSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadFollowUpLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.BusinessModuleEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.CreateMethodEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadSourceEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.service.leads.impl.LeadFollowUpLogServiceImpl;
import cn.bztmaster.cnt.module.publicbiz.service.leads.impl.LeadsServiceImpl;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 线索中心集成测试
 * 
 * <AUTHOR>
 */
@Import({LeadsServiceImpl.class, LeadFollowUpLogServiceImpl.class})
public class LeadsIntegrationTest extends BaseDbUnitTest {

    @Resource
    private LeadsService leadsService;
    
    @Resource
    private LeadFollowUpLogService leadFollowUpLogService;
    
    @Test
    @DisplayName("测试线索完整流程")
    public void testLeadFlow() {
        // 1. 创建线索
        LeadsSaveReqDTO createReqDTO = new LeadsSaveReqDTO()
                .setCustomerName("测试客户")
                .setCustomerPhone("***********")
                .setLeadSource(LeadSourceEnum.WEBSITE_REGISTRATION.getType())
                .setBusinessModule(BusinessModuleEnum.UNIVERSITY_BUSINESS.getType())
                .setCreateMethod(CreateMethodEnum.MANUAL_CREATION.getType())
                .setRemark("测试备注");
        
        Long leadId = leadsService.createLead(createReqDTO);
        assertNotNull(leadId);
        
        // 2. 查询线索
        LeadInfoDO lead = leadsService.getLead(leadId);
        assertNotNull(lead);
        assertEquals("测试客户", lead.getCustomerName());
        assertEquals("***********", lead.getCustomerPhone());
        
        // 3. 分配线索
        LeadsAssignReqDTO assignReqDTO = new LeadsAssignReqDTO()
                .setLeadId(leadId.toString())
                .setLeadId("测试跟进人");
        leadsService.assignLead(assignReqDTO);
        
        // 4. 查询分配后的线索
        lead = leadsService.getLead(leadId);
        assertEquals("测试跟进人", lead.getCurrentOwner());
        
        // 5. 添加跟进记录
        LeadFollowUpLogSaveReqDTO followUpReqDTO = new LeadFollowUpLogSaveReqDTO()
                .setLeadId(lead.getLeadId())
                .setFollowUpContent("测试跟进内容");
        Long followUpId = leadFollowUpLogService.createLeadFollowUpLog(followUpReqDTO);
        assertNotNull(followUpId);
        
        // 6. 查询跟进记录
        List<LeadFollowUpLogDO> followUpLogs = leadFollowUpLogService.getLeadFollowUpLogListByLeadId(lead.getLeadId());
        assertFalse(followUpLogs.isEmpty());
        assertEquals("测试跟进内容", followUpLogs.get(0).getFollowUpContent());
        
        // 7. 更新线索状态
        createReqDTO.setId(leadId);
        createReqDTO.setLeadStatus(LeadStatusEnum.FOLLOWING_UP.getType());
        leadsService.updateLead(createReqDTO);
        
        // 8. 查询更新后的线索
        lead = leadsService.getLead(leadId);
        assertEquals(LeadStatusEnum.FOLLOWING_UP.getType(), lead.getLeadStatus());
        
        // 9. 分页查询
        LeadsPageReqDTO pageReqDTO = new LeadsPageReqDTO();
        pageReqDTO.setCustomerName("测试");
        PageResult<LeadInfoDO> pageResult = leadsService.getLeadPage(pageReqDTO);
        assertFalse(pageResult.getList().isEmpty());
        
        // 10. 删除线索
        leadsService.deleteLead(leadId);
        
        // 11. 确认删除成功
        lead = leadsService.getLead(leadId);
        assertNull(lead);
    }
}