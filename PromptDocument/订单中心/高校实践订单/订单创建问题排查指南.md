# 高校实践订单创建问题排查指南

## 问题描述

新增高校实践订单失败，接口：`POST /publicbiz/order/create`

## 常见问题及解决方案

### 1. 参数验证失败

#### 问题现象
- 接口返回400错误
- 错误信息包含参数验证失败

#### 常见原因
1. **managerId为0或负数**
   - 错误：`managerId` 不能为0或负数
   - 解决：确保 `managerId` 为正整数

2. **必填字段为空**
   - 项目名称、高校名称、企业名称为空
   - 开始日期、结束日期为空
   - 订单总金额为0或负数

3. **日期格式错误**
   - 日期格式应为 `YYYY-MM-DD`
   - 开始日期不能晚于结束日期

4. **收款信息不完整**
   - 当 `paymentStatus` 为 `"paid"` 时，以下字段必填：
     - `collectionAmount`：收款金额
     - `collectionMethod`：收款方式
     - `collectionDate`：收款日期
     - `operatorName`：操作人姓名

#### 解决方案
```json
{
  "projectName": "ZZ科技大学AI产业认知项目商机",
  "universityName": "ZZ科技大学",
  "enterpriseName": "AI产业公司",
  "startDate": "2025-07-30",
  "endDate": "2028-08-18",
  "totalAmount": 350000,
  "managerId": 1001,  // 修改：不能为0，必须为正整数
  "managerName": "李1",
  "contractType": "electronic",
  "paymentStatus": "paid",
  "collectionAmount": 350000,
  "collectionMethod": "cash",
  "collectionDate": "2025-08-12",
  "operatorName": "李1",
  "collectionRemark": "2222"
}
```

### 2. 数据库连接问题

#### 问题现象
- 接口返回500错误
- 错误日志显示数据库连接异常

#### 解决方案
1. 检查数据库连接配置
2. 确认数据库服务是否正常运行
3. 检查数据库用户权限

### 3. 事务回滚问题

#### 问题现象
- 接口返回500错误
- 错误日志显示事务回滚

#### 常见原因
1. 数据库表结构不匹配
2. 字段类型转换失败
3. 唯一约束冲突

#### 解决方案
1. 检查数据库表结构
2. 确认字段类型匹配
3. 检查唯一约束

## 调试步骤

### 1. 检查请求参数
```bash
# 使用curl测试接口
curl -X POST "http://localhost:8080/publicbiz/order/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "projectName": "测试项目",
    "universityName": "测试大学",
    "enterpriseName": "测试企业",
    "startDate": "2025-01-01",
    "endDate": "2025-12-31",
    "totalAmount": 100000,
    "managerId": 1001,
    "managerName": "测试负责人"
  }'
```

### 2. 检查日志
```bash
# 查看应用日志
tail -f logs/application.log | grep "高校实践订单"
```

### 3. 检查数据库
```sql
-- 检查订单表结构
DESC publicbiz_order;

-- 检查高校实践订单详情表结构
DESC publicbiz_practice_order;

-- 检查是否有数据
SELECT * FROM publicbiz_order WHERE project_name LIKE '%ZZ科技大学%';
```

## 预防措施

### 1. 前端验证
- 在提交前进行参数验证
- 确保必填字段不为空
- 验证日期格式和范围
- 验证金额格式

### 2. 后端验证
- 使用 `@Valid` 注解进行参数验证
- 在Service层进行业务逻辑验证
- 提供详细的错误信息

### 3. 日志记录
- 记录详细的请求参数
- 记录验证失败的原因
- 记录数据库操作结果

## 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 1009   | 项目名称不能为空 | 填写项目名称 |
| 1010   | 高校名称不能为空 | 填写高校名称 |
| 1011   | 企业名称不能为空 | 填写企业名称 |
| 1012   | 开始日期不能为空 | 选择开始日期 |
| 1013   | 结束日期不能为空 | 选择结束日期 |
| 1014   | 日期范围无效 | 确保开始日期早于结束日期 |
| 1015   | 订单总金额无效 | 填写有效的订单金额 |
| 1016   | 负责人ID无效 | 选择有效的负责人 |
| 1017   | 负责人姓名不能为空 | 填写负责人姓名 |
| 1018   | 收款金额无效 | 填写有效的收款金额 |
| 1019   | 收款方式不能为空 | 选择收款方式 |
| 1020   | 收款日期不能为空 | 选择收款日期 |
| 1021   | 操作人姓名不能为空 | 填写操作人姓名 |

## 联系支持

如果问题仍然存在，请联系技术支持并提供以下信息：

1. 完整的请求参数
2. 错误响应信息
3. 应用日志
4. 数据库错误日志
5. 系统环境信息
