package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 工单指派阿姨请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单指派阿姨请求 VO")
@Data
public class WorkOrderAssignAuntReqVO {

    @Schema(description = "工单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "WO202312010001")
    @NotBlank(message = "工单编号不能为空")
    private String workOrderNo;

    @Schema(description = "订单号", example = "ORD202312010001")
    private String orderNo;

    @Schema(description = "阿姨OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "a1b2c3d4-e5f6-7890-abcd-ef1234567890")
    @NotBlank(message = "阿姨OneID不能为空")
    private String auntOneid;

    @Schema(description = "阿姨姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李阿姨")
    @NotBlank(message = "阿姨姓名不能为空")
    private String auntName;

    @Schema(description = "指派原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "原阿姨请假，临时指派")
    @NotBlank(message = "指派原因不能为空")
    private String assignmentReason;

    @Schema(description = "重新指派时间", example = "2023-12-01 10:00:00")
    private String reassignmentTime;
} 