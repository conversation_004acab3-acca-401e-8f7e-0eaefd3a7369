# 雇主端小程序授权登录接口文档

## 概述

本文档描述了雇主端小程序授权登录相关的接口，包括微信小程序一键授权登录和手机验证码登录功能。

## 基础信息

- **接口前缀**: `/publicbiz/employer/auth`
- **请求方式**: POST
- **数据格式**: JSON
- **字符编码**: UTF-8

## 接口列表

### 1. 微信小程序一键授权登录

**接口地址**: `/publicbiz/employer/auth/weixin-mini-app-login`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| code | String | 是 | 微信登录临时code，小程序通过 wx.login 方法获得 | "001frTkl21JUf94VGxol2hSlff1frTkR" |
| nickname | String | 否 | 用户昵称 | "张三" |
| headImageUrl | String | 否 | 用户头像地址 | "https://example.com/avatar.jpg" |
| mobile | String | 否 | 用户手机号 | "13800138000" |

**请求示例**:
```json
{
  "code": "001frTkl21JUf94VGxol2hSlff1frTkR",
  "nickname": "张三",
  "headImageUrl": "https://example.com/avatar.jpg",
  "mobile": "13800138000"
}
```

**响应参数**:

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| code | Integer | 响应码，0表示成功 | 0 |
| data | Object | 响应数据 | - |
| data.userId | Long | 用户编号 | 1024 |
| data.accessToken | String | 访问令牌 | "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." |
| data.refreshToken | String | 刷新令牌 | "refresh_token_123456" |
| data.expiresTime | String | 过期时间 | "2024-01-01T12:00:00" |
| data.nickname | String | 用户昵称 | "张三" |
| data.headImageUrl | String | 用户头像地址 | "https://example.com/avatar.jpg" |
| data.mobile | String | 用户手机号 | "13800138000" |
| data.openid | String | 微信openid | "oH_Tu5EtrrF6n7u8v9w0x1y2z3" |
| data.role | String | 用户角色 | "employer" |

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "userId": 1024,
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_123456",
    "expiresTime": "2024-01-01T12:00:00",
    "nickname": "张三",
    "headImageUrl": "https://example.com/avatar.jpg",
    "mobile": "13800138000",
    "openid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
    "role": "employer"
  },
  "msg": "操作成功"
}
```

### 2. 发送手机验证码

**接口地址**: `/publicbiz/employer/auth/send-sms-code`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| mobile | String | 是 | 手机号 | "13800138000" |

**请求示例**:
```json
{
  "mobile": "13800138000"
}
```

**响应参数**:

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| code | Integer | 响应码，0表示成功 | 0 |
| data | Boolean | 响应数据，true表示发送成功 | true |
| msg | String | 响应消息 | "操作成功" |

**响应示例**:
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

### 3. 校验手机验证码

**接口地址**: `/publicbiz/employer/auth/validate-sms-code`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| mobile | String | 是 | 手机号 | "13800138000" |
| code | String | 是 | 手机验证码，4-6位数字 | "123456" |

**请求示例**:
```json
{
  "mobile": "13800138000",
  "code": "123456"
}
```

**响应参数**:

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| code | Integer | 响应码，0表示成功 | 0 |
| data | Boolean | 响应数据，true表示校验成功 | true |
| msg | String | 响应消息 | "操作成功" |

**响应示例**:
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

### 4. 手机验证码登录

**接口地址**: `/publicbiz/employer/auth/sms-login`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| mobile | String | 是 | 手机号 | "13800138000" |
| code | String | 是 | 手机验证码，4-6位数字 | "123456" |
| nickname | String | 否 | 用户昵称 | "张三" |
| headImageUrl | String | 否 | 用户头像地址 | "https://example.com/avatar.jpg" |

**请求示例**:
```json
{
  "mobile": "13800138000",
  "code": "123456",
  "nickname": "张三",
  "headImageUrl": "https://example.com/avatar.jpg"
}
```

**响应参数**:

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| code | Integer | 响应码，0表示成功 | 0 |
| data | Object | 响应数据 | - |
| data.userId | Long | 用户编号 | 1024 |
| data.accessToken | String | 访问令牌 | "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." |
| data.refreshToken | String | 刷新令牌 | "refresh_token_123456" |
| data.expiresTime | String | 过期时间 | "2024-01-01T12:00:00" |
| data.nickname | String | 用户昵称 | "张三" |
| data.headImageUrl | String | 用户头像地址 | "https://example.com/avatar.jpg" |
| data.mobile | String | 用户手机号 | "13800138000" |
| data.openid | String | 微信openid | "oH_Tu5EtrrF6n7u8v9w0x1y2z3" |
| data.role | String | 用户角色 | "employer" |

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "userId": 1024,
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_123456",
    "expiresTime": "2024-01-01T12:00:00",
    "nickname": "张三",
    "headImageUrl": "https://example.com/avatar.jpg",
    "mobile": "13800138000",
    "openid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
    "role": "employer"
  },
  "msg": "操作成功"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1002 | 微信登录失败 |
| 1003 | 用户不存在，请先注册 |
| 1004 | 用户被禁用，请联系客服 |
| 5000 | 服务器内部错误 |

## 注意事项

1. **微信小程序一键授权登录**：
   - 需要先调用微信小程序的 `wx.login()` 方法获取临时code
   - 如果用户首次登录，系统会自动创建用户记录
   - 如果用户已存在，系统会更新用户信息

2. **手机验证码登录**：
   - 需要先调用发送验证码接口获取验证码
   - 验证码有效期为5分钟
   - 如果用户首次登录，系统会自动创建用户记录

3. **访问令牌**：
   - 登录成功后返回的 `accessToken` 需要在后续请求的 Header 中携带
   - 格式：`Authorization: Bearer {accessToken}`
   - 令牌有效期为2小时，过期后需要使用 `refreshToken` 刷新

4. **用户角色**：
   - 所有通过雇主端小程序登录的用户角色均为 "employer"

## 小程序端调用示例

### 微信小程序一键授权登录

```javascript
// 1. 获取微信登录临时code
wx.login({
  success: (res) => {
    if (res.code) {
      // 2. 调用后端登录接口
      wx.request({
        url: 'https://api.example.com/publicbiz/employer/auth/weixin-mini-app-login',
        method: 'POST',
        data: {
          code: res.code,
          nickname: userInfo.nickName,
          headImageUrl: userInfo.avatarUrl,
          mobile: phoneNumber
        },
        success: (response) => {
          if (response.data.code === 0) {
            // 登录成功，保存token
            wx.setStorageSync('accessToken', response.data.data.accessToken);
            wx.setStorageSync('refreshToken', response.data.data.refreshToken);
          }
        }
      });
    }
  }
});
```

### 手机验证码登录

```javascript
// 1. 发送验证码
wx.request({
  url: 'https://api.example.com/publicbiz/employer/auth/send-sms-code',
  method: 'POST',
  data: {
    mobile: phoneNumber
  }
});

// 2. 验证码登录
wx.request({
  url: 'https://api.example.com/publicbiz/employer/auth/sms-login',
  method: 'POST',
  data: {
    mobile: phoneNumber,
    code: smsCode,
    nickname: userInfo.nickName,
    headImageUrl: userInfo.avatarUrl
  },
  success: (response) => {
    if (response.data.code === 0) {
      // 登录成功，保存token
      wx.setStorageSync('accessToken', response.data.data.accessToken);
      wx.setStorageSync('refreshToken', response.data.data.refreshToken);
    }
  }
});
```

## 配置说明

### 微信小程序配置

接口中使用的 `appid` 和 `secret` 通过 `wxMiniProgramConfig.getAppId()` 和 `wxMiniProgramConfig.getSecret()` 获取，配置在以下文件中：

1. **application-wx.yml** (推荐)
```yaml
# 微信小程序配置
wx:
  miniprogram:
    app-id: wxf0cb5e5168f30b6a
    secret: c4add048842fa0d7149d82f51f2d1852
    code2-session-url: https://api.weixin.qq.com/sns/jscode2session
```

2. **application-local.yaml** 或其他环境配置文件
```yaml
wx:
  miniprogram:
    app-id: your_app_id
    secret: your_app_secret
    code2-session-url: https://api.weixin.qq.com/sns/jscode2session
```

### OAuth2客户端配置

需要在 `system_oauth2_client` 表中添加对应的客户端配置，使用微信小程序的appid作为客户端ID：

```sql
INSERT INTO system_oauth2_client (
    client_id, 
    secret, 
    name, 
    logo, 
    description, 
    status, 
    access_token_validity_seconds, 
    refresh_token_validity_seconds, 
    redirect_uris, 
    authorized_grant_types, 
    scopes, 
    auto_approve_scopes, 
    authorities, 
    resource_ids, 
    additional_information, 
    creator, 
    create_time, 
    updater, 
    update_time, 
    deleted
) VALUES (
    'wxf0cb5e5168f30b6a', -- 微信小程序appid作为客户端ID
    'c4add048842fa0d7149d82f51f2d1852', -- 微信小程序secret作为客户端密钥
    '雇主端小程序', 
    'https://example.com/logo.png', 
    '雇主端小程序OAuth2客户端',
    0, -- 状态：0-启用，1-禁用
    7200, -- 访问令牌有效期2小时
    604800, -- 刷新令牌有效期7天
    'https://example.com/callback', 
    'password,refresh_token', 
    'read', 
    'read', 
    'read', -- 权限
    '', -- 资源
    '{"app_type": "miniprogram", "user_type": "employer"}', -- 附加信息
    'system', 
    NOW(), 
    'system', 
    NOW(), 
    0
) ON DUPLICATE KEY UPDATE
    secret = VALUES(secret),
    name = VALUES(name),
    description = VALUES(description),
    status = VALUES(status),
    access_token_validity_seconds = VALUES(access_token_validity_seconds),
    refresh_token_validity_seconds = VALUES(refresh_token_validity_seconds),
    authorized_grant_types = VALUES(authorized_grant_types),
    scopes = VALUES(scopes),
    auto_approve_scopes = VALUES(auto_approve_scopes),
    authorities = VALUES(authorities),
    additional_information = VALUES(additional_information),
    updater = VALUES(updater),
    update_time = VALUES(update_time);
```

**注意**：`client_id` 和 `client_secret` 需要与微信小程序的 `app-id` 和 `secret` 保持一致。

## 数据库表结构

小程序用户表 `mp_user` 结构：

```sql
CREATE TABLE `mp_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `oneid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'OneID GUID',
  `openid` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户标识',
  `union_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信生态唯一标识',
  `subscribe_status` tinyint(4) NOT NULL COMMENT '关注状态',
  `subscribe_time` datetime NOT NULL COMMENT '关注时间',
  `mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号（仅小程序使用）',
  `nickname` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
  `head_image_url` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像地址',
  `unsubscribe_time` datetime DEFAULT NULL COMMENT '取消关注时间',
  `language` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言',
  `country` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家',
  `province` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '省份',
  `city` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '城市',
  `remark` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `tag_ids` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签编号数组',
  `account_id` bigint(20) NOT NULL COMMENT '微信公众号ID',
  `app_id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '微信公众号 appid',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=57 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信用户表（小程序+公众号）';
``` 