package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 标签库表 DO
 *
 * <AUTHOR>
 */
@TableName("talent_tag_library")
@KeySequence("talent_tag_library_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TalentTagLibraryDO extends BaseDO {

    /**
     * 标签ID
     */
    @TableId
    private Long tagId;

    /**
     * 标签类型ID
     */
    private Long tagTypeId;

    /**
     * 标签编码
     */
    private String tagCode;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签描述
     */
    private String description;
}
