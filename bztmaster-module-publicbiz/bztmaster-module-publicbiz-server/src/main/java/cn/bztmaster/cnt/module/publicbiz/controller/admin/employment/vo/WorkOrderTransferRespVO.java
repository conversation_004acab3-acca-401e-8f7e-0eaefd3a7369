package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工单转派响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单转派响应 VO")
@Data
public class WorkOrderTransferRespVO {

    @Schema(description = "工单编号", example = "WO202312010001")
    private String workOrderNo;

    @Schema(description = "转派时间")
    private LocalDateTime transferTime;

    @Schema(description = "新负责人ID", example = "456")
    private Long newAssigneeId;

    @Schema(description = "新负责人姓名", example = "李四")
    private String newAssigneeName;

    @Schema(description = "转派是否成功", example = "true")
    private Boolean success;
} 