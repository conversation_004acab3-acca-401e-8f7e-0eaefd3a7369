package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.NewsDO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo.AdminNewsPageReqVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 资讯 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NewsMapper extends BaseMapperX<NewsDO> {

    /**
     * 获取首页资讯列表（前N条）
     *
     * @param limit 返回数量
     * @return 资讯列表
     */
    default List<NewsDO> selectHomeNewsList(Integer limit) {
        LambdaQueryWrapper<NewsDO> queryWrapper = new LambdaQueryWrapper<NewsDO>()
                .eq(NewsDO::getDeleted, false)
                .eq(NewsDO::getStatus, "published")
                .le(NewsDO::getPublishTime, LocalDateTime.now())
                .orderByAsc(NewsDO::getSort)
                .orderByDesc(NewsDO::getPublishTime)
                .last("LIMIT " + limit);
        return selectList(queryWrapper);
    }

    /**
     * 分页查询资讯列表
     *
     * @param pageParam  分页参数
     * @param categoryId 分类ID
     * @param keyword    关键词
     * @param status     状态
     * @return 分页结果
     */
    default PageResult<NewsDO> selectNewsPage(PageParam pageParam, Long categoryId, String keyword, String status) {
        LambdaQueryWrapper<NewsDO> queryWrapper = new LambdaQueryWrapper<NewsDO>()
                .eq(NewsDO::getDeleted, false)
                .eq(categoryId != null, NewsDO::getCategoryId, categoryId)
                .like(keyword != null, NewsDO::getNewsTitle, keyword)
                .eq(status != null, NewsDO::getStatus, status)
                .orderByAsc(NewsDO::getSort)
                .orderByDesc(NewsDO::getPublishTime);

        return selectPage(pageParam, queryWrapper);
    }

    /**
     * 增加浏览次数
     *
     * @param id 资讯ID
     * @return 更新行数
     */
    @Update("UPDATE publicbiz_news SET view_count = view_count + 1 WHERE id = #{id} AND deleted = 0")
    int incrementViewCount(@Param("id") Long id);

    // ==================== 管理后台新增方法 ====================

    /**
     * 根据资讯标题查询资讯
     *
     * @param newsTitle 资讯标题
     * @return 资讯DO
     */
    default NewsDO selectByNewsTitle(String newsTitle) {
        LambdaQueryWrapper<NewsDO> queryWrapper = new LambdaQueryWrapper<NewsDO>()
                .eq(NewsDO::getDeleted, false)
                .eq(NewsDO::getNewsTitle, newsTitle);
        return selectOne(queryWrapper);
    }

    /**
     * 根据资讯标题查询资讯（排除指定ID）
     *
     * @param newsTitle 资讯标题
     * @param excludeId 排除的ID
     * @return 资讯DO
     */
    default NewsDO selectByNewsTitleExcludeId(String newsTitle, Long excludeId) {
        LambdaQueryWrapper<NewsDO> queryWrapper = new LambdaQueryWrapper<NewsDO>()
                .eq(NewsDO::getDeleted, false)
                .eq(NewsDO::getNewsTitle, newsTitle)
                .ne(NewsDO::getId, excludeId);
        return selectOne(queryWrapper);
    }

    /**
     * 管理后台分页查询资讯列表
     *
     * @param pageReqVO 分页查询请求
     * @return 分页结果
     */
    default PageResult<NewsDO> selectAdminNewsPage(AdminNewsPageReqVO pageReqVO) {
        LambdaQueryWrapper<NewsDO> queryWrapper = new LambdaQueryWrapper<NewsDO>()
                .eq(NewsDO::getDeleted, false)
                .like(pageReqVO.getNewsTitle() != null, NewsDO::getNewsTitle, pageReqVO.getNewsTitle())
                .eq(pageReqVO.getCategoryId() != null, NewsDO::getCategoryId, pageReqVO.getCategoryId())
                .eq(pageReqVO.getStatus() != null, NewsDO::getStatus, pageReqVO.getStatus())
                .ge(pageReqVO.getStartTime() != null, NewsDO::getCreateTime, pageReqVO.getStartTime())
                .le(pageReqVO.getEndTime() != null, NewsDO::getCreateTime, pageReqVO.getEndTime())
                .orderByAsc(NewsDO::getSort)
                .orderByDesc(NewsDO::getCreateTime);

        // 根据内容来源设置不同的查询条件
        if (pageReqVO.getContentSource() != null) {
            if ("material".equals(pageReqVO.getContentSource())) {
                // 关联素材库：material_id 不为空
                queryWrapper.isNotNull(NewsDO::getMaterialId);
            } else if ("manual".equals(pageReqVO.getContentSource())) {
                // 手动编写：material_id 为空
                queryWrapper.isNull(NewsDO::getMaterialId);
            }
        }

        return selectPage(pageReqVO, queryWrapper);
    }

}