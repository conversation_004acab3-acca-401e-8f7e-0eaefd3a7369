# 轮播图管理API接口文档

## 1. 接口概述

### 1.1 基础信息

- **基础路径**: `/publicbiz/carousel`
- **数据格式**: `application/json`
- **字符编码**: `UTF-8`

### 1.2 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

## 2. 接口列表

### 2.1 分页查询轮播图列表

**接口地址**: `GET /publicbiz/carousel/page`

**功能说明**: 分页查询轮播图列表，支持按标题、状态、平台等条件筛选

**请求参数**:

| 参数名        | 类型    | 必填 | 说明                               |
| ------------- | ------- | ---- | ---------------------------------- |
| page          | Integer | 是   | 页码，从1开始                      |
| size          | Integer | 是   | 每页大小，最大100                  |
| carouselTitle | String  | 否   | 轮播图标题，支持模糊查询           |
| status        | Integer | 否   | 状态：1-启用，0-禁用               |
| platform      | String  | 否   | 平台：employer-雇主端，aunt-阿姨端 |

**请求示例**:

```http
GET /publicbiz/carousel/page?page=1&size=10&carouselTitle=专业月嫂&status=1&platform=employer
```

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "carouselTitle": "专业月嫂服务",
      "carouselImageUrl": "https://example.com/images/carousel1.jpg",
      "carouselLinkUrl": "/service/maternity",
      "sortOrder": 1,
      "status": 1,
      "createTime": "2024-06-20 10:00:00"
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10
}
```

### 2.2 新增轮播图

**接口地址**: `POST /publicbiz/carousel/create`

**功能说明**: 新增轮播图信息

**请求参数**:

| 参数名           | 类型    | 必填 | 说明                               |
| ---------------- | ------- | ---- | ---------------------------------- |
| platform         | String  | 是   | 平台：employer-雇主端，aunt-阿姨端 |
| carouselTitle    | String  | 是   | 轮播图标题，最大200字符            |
| carouselImageUrl | String  | 是   | 轮播图片URL，最大500字符           |
| carouselLinkUrl  | String  | 否   | 跳转链接URL，最大500字符           |
| sortOrder        | Integer | 否   | 排序值，默认0                      |
| status           | Integer | 否   | 状态：1-启用，0-禁用，默认1        |

**请求示例**:

```json
{
  "platform": "employer",
  "carouselTitle": "专业月嫂服务",
  "carouselImageUrl": "https://example.com/images/carousel1.jpg",
  "carouselLinkUrl": "/service/maternity",
  "sortOrder": 1,
  "status": 1
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "新增成功",
  "data": {
    "id": 1
  }
}
```

### 2.3 更新轮播图

**接口地址**: `PUT /publicbiz/carousel/update`

**功能说明**: 更新轮播图信息

**请求参数**:

| 参数名           | 类型    | 必填 | 说明                               |
| ---------------- | ------- | ---- | ---------------------------------- |
| id               | Long    | 是   | 轮播图ID                           |
| platform         | String  | 否   | 平台：employer-雇主端，aunt-阿姨端 |
| carouselTitle    | String  | 否   | 轮播图标题，最大200字符            |
| carouselImageUrl | String  | 否   | 轮播图片URL，最大500字符           |
| carouselLinkUrl  | String  | 否   | 跳转链接URL，最大500字符           |
| sortOrder        | Integer | 否   | 排序值                             |
| status           | Integer | 否   | 状态：1-启用，0-禁用               |

**请求示例**:

```json
{
  "id": 1,
  "carouselTitle": "专业月嫂服务-更新版",
  "sortOrder": 2
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

### 2.4 删除轮播图

**接口地址**: `DELETE /publicbiz/carousel/delete/{id}`

**功能说明**: 根据ID删除轮播图（逻辑删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明               |
| ------ | ---- | ---- | ------------------ |
| id     | Long | 是   | 轮播图ID，路径参数 |

**请求示例**:

```http
DELETE /publicbiz/carousel/delete/1
```

**响应示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 2.5 更新轮播图状态

**接口地址**: `PUT /publicbiz/carousel/updateStatus`

**功能说明**: 启用或禁用轮播图

**请求参数**:

| 参数名 | 类型    | 必填 | 说明                 |
| ------ | ------- | ---- | -------------------- |
| id     | Long    | 是   | 轮播图ID             |
| status | Integer | 是   | 状态：1-启用，0-禁用 |

**请求示例**:

```json
{
  "id": 1,
  "status": 0
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": null
}
```

## 3. 数据字典

### 3.1 平台枚举值

| 值       | 说明   |
| -------- | ------ |
| employer | 雇主端 |
| aunt     | 阿姨端 |

### 3.2 状态枚举值

| 值  | 说明 |
| --- | ---- |
| 1   | 启用 |
| 0   | 禁用 |

## 4. 错误码说明

| 状态码 | 说明             |
| ------ | ---------------- |
| 200    | 请求成功         |
| 400    | 参数验证错误     |
| 401    | 未授权，需要登录 |
| 403    | 权限不足         |
| 404    | 资源不存在       |
| 500    | 服务器内部错误   |

## 5. 注意事项

### 5.1 图片要求

- 支持格式：JPG、PNG、GIF
- 建议尺寸：750x400px
- 文件大小：不超过2MB

### 5.2 权限要求

- 查询接口：需要登录权限
- 新增接口：需要轮播图管理权限
- 更新接口：需要轮播图管理权限
- 删除接口：需要轮播图管理权限

### 5.3 数据安全

- 所有接口都需要进行权限验证
- 敏感操作需要记录操作日志
- 支持软删除，删除后数据不会物理删除

### 5.4 排序规则

- 排序值越小，显示越靠前
- 相同排序值的轮播图按创建时间倒序排列
- 排序值范围：0-999

## 6. 接口调用示例

### 6.1 JavaScript调用示例

```javascript
// 查询轮播图列表
const getCarouselList = async (params) => {
  try {
    const response = await fetch('/publicbiz/carousel/page?' + new URLSearchParams(params), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + token
      }
    })
    return await response.json()
  } catch (error) {
    console.error('查询失败:', error)
  }
}

// 新增轮播图
const createCarousel = async (data) => {
  try {
    const response = await fetch('/publicbiz/carousel/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + token
      },
      body: JSON.stringify(data)
    })
    return await response.json()
  } catch (error) {
    console.error('新增失败:', error)
  }
}
```

### 6.2 前端Vue组件调用示例

```vue
<template>
  <div>
    <el-button @click="fetchCarouselList">查询列表</el-button>
    <el-button @click="addCarousel">新增轮播图</el-button>
  </div>
</template>

<script>
import { ref } from 'vue'
import axios from 'axios'

export default {
  setup() {
    const carouselList = ref([])

    const fetchCarouselList = async () => {
      try {
        const response = await axios.get('/publicbiz/carousel/page', {
          params: {
            page: 1,
            size: 10
          }
        })
        carouselList.value = response.data
      } catch (error) {
        console.error('查询失败:', error)
      }
    }

    const addCarousel = async (carouselData) => {
      try {
        await axios.post('/publicbiz/carousel/create', carouselData)
        ElMessage.success('新增成功')
        fetchCarouselList()
      } catch (error) {
        console.error('新增失败:', error)
      }
    }

    return {
      carouselList,
      fetchCarouselList,
      addCarousel
    }
  }
}
</script>
```

## 7. 更新日志

| 版本  | 日期       | 更新内容                       |
| ----- | ---------- | ------------------------------ |
| 1.0.0 | 2024-06-20 | 初始版本，包含基础的CRUD接口   |
| 1.1.0 | 2024-06-25 | 新增批量排序接口，优化错误码   |
| 1.2.0 | 2024-06-30 | 新增时间范围筛选，完善参数验证 |
