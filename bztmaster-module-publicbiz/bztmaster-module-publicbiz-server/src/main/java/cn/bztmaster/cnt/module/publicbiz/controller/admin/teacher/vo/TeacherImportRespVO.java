package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "师资库 - 批量导入响应 VO")
public class TeacherImportRespVO {
    
    @Schema(description = "总记录数")
    private Integer totalCount;
    
    @Schema(description = "成功导入数")
    private Integer successCount;
    
    @Schema(description = "失败数")
    private Integer failCount;
    
    @Schema(description = "失败原因列表")
    private String failReasons;
} 