package cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

import static cn.bztmaster.cnt.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 管理后台 - 根据场地和日期查询预约 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 根据场地和日期查询预约 Request VO")
@Data
public class SiteAppointmentBySiteAndDateReqVO {

    @Schema(description = "场地ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "场地ID不能为空")
    private Long siteId;

    @Schema(description = "单个日期查询", example = "2025-08-15")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate date;

    @Schema(description = "开始日期", example = "2025-08-15")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2025-08-20")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

}
