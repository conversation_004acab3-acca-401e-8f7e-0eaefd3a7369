package cn.bztmaster.cnt.module.publicbiz.api.employer;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.employer.dto.NewsStatusUpdateReqDTO;

/**
 * 资讯 API 接口
 *
 * <AUTHOR>
 */
public interface NewsApi {

    /**
     * 分页查询资讯列表
     *
     * @param reqDTO 查询条件
     * @return 资讯分页结果
     */
    PageResult<NewsRespDTO> pageNews(NewsPageReqDTO reqDTO);

    /**
     * 根据ID获取资讯详情
     *
     * @param id 资讯ID
     * @return 资讯详情
     */
    NewsRespDTO getNews(Long id);

    /**
     * 创建资讯
     *
     * @param reqDTO 创建请求
     * @return 资讯ID
     */
    Long createNews(NewsSaveReqDTO reqDTO);

    /**
     * 更新资讯
     *
     * @param reqDTO 更新请求
     */
    void updateNews(NewsSaveReqDTO reqDTO);

    /**
     * 删除资讯
     *
     * @param id 资讯ID
     */
    void deleteNews(Long id);

    /**
     * 更新资讯状态
     *
     * @param reqDTO 状态更新请求
     */
    void updateNewsStatus(NewsStatusUpdateReqDTO reqDTO);
} 