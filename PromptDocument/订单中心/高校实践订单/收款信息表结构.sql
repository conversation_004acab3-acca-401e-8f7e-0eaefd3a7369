-- ==================== 高校实践订单收款信息相关表 ====================

-- 收款信息记录表
CREATE TABLE `publicbiz_order_collection` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
  `collection_no` VARCHAR(50) NOT NULL COMMENT '收款单号',
  `collection_type` VARCHAR(20) NOT NULL COMMENT '收款类型：full-全额收款/partial-部分收款/refund-退款',
  `collection_amount` DECIMAL(12,2) NOT NULL COMMENT '收款金额',
  `collection_method` VARCHAR(20) NOT NULL COMMENT '收款方式：cash-现金/wechat-微信支付/alipay-支付宝/bank_transfer-银行转账/pos-POS机刷卡/other-其他',
  `collection_date` DATE NOT NULL COMMENT '收款日期',
  `collection_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '收款状态：pending-待确认/confirmed-已确认/cancelled-已取消',
  
  -- 操作人信息
  `operator_id` BIGINT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `operator_role` VARCHAR(50) COMMENT '操作人角色',
  
  -- 备注信息
  `collection_remark` TEXT COMMENT '收款备注',
  `transaction_id` VARCHAR(100) COMMENT '第三方交易号',
  `bank_account` VARCHAR(100) COMMENT '银行账户信息',
  `bank_name` VARCHAR(100) COMMENT '银行名称',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_collection_no` (`collection_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_collection_type` (`collection_type`),
  KEY `idx_collection_method` (`collection_method`),
  KEY `idx_collection_status` (`collection_status`),
  KEY `idx_collection_date` (`collection_date`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='订单收款信息记录表';

-- 收款方式配置表
CREATE TABLE `publicbiz_collection_method_config` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `method_code` VARCHAR(20) NOT NULL COMMENT '收款方式编码',
  `method_name` VARCHAR(50) NOT NULL COMMENT '收款方式名称',
  `method_description` VARCHAR(200) COMMENT '收款方式描述',
  `is_enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `icon_url` VARCHAR(500) COMMENT '图标URL',
  
  -- 配置信息
  `config_json` JSON COMMENT '配置信息JSON',
  `remark` TEXT COMMENT '备注',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_method_code` (`method_code`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='收款方式配置表';

-- 插入默认收款方式数据
INSERT INTO `publicbiz_collection_method_config` (`method_code`, `method_name`, `method_description`, `is_enabled`, `sort_order`, `remark`) VALUES
('cash', '现金', '现金收款方式', 1, 1, '适用于现场现金收款'),
('wechat', '微信支付', '微信支付收款方式', 1, 2, '适用于线上微信支付'),
('alipay', '支付宝', '支付宝收款方式', 1, 3, '适用于线上支付宝支付'),
('bank_transfer', '银行转账', '银行转账收款方式', 1, 4, '适用于对公转账'),
('pos', 'POS机刷卡', 'POS机刷卡收款方式', 1, 5, '适用于现场刷卡'),
('other', '其他', '其他收款方式', 1, 6, '适用于其他特殊收款方式');

-- 收款统计表（可选，用于统计报表）
CREATE TABLE `publicbiz_collection_statistics` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 统计字段
  `statistics_date` DATE NOT NULL COMMENT '统计日期',
  `order_type` VARCHAR(30) NOT NULL COMMENT '订单类型',
  `business_line` VARCHAR(50) NOT NULL COMMENT '业务线',
  `collection_method` VARCHAR(20) NOT NULL COMMENT '收款方式',
  `collection_count` INT NOT NULL DEFAULT 0 COMMENT '收款笔数',
  `collection_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '收款金额',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_statistics_date_order_type_business_line_collection_method` (`statistics_date`, `order_type`, `business_line`, `collection_method`),
  KEY `idx_statistics_date` (`statistics_date`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_business_line` (`business_line`),
  KEY `idx_collection_method` (`collection_method`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='收款统计表';

-- 更新订单主表，添加收款相关字段（如果原表没有这些字段）
ALTER TABLE `publicbiz_order` 
ADD COLUMN `collection_amount` DECIMAL(12,2) DEFAULT 0.00 COMMENT '收款金额' AFTER `refund_amount`,
ADD COLUMN `collection_method` VARCHAR(20) COMMENT '收款方式' AFTER `collection_amount`,
ADD COLUMN `collection_date` DATE COMMENT '收款日期' AFTER `collection_method`,
ADD COLUMN `operator_name` VARCHAR(50) COMMENT '操作人姓名' AFTER `collection_date`,
ADD COLUMN `collection_remark` TEXT COMMENT '收款备注' AFTER `operator_name`,
ADD KEY `idx_collection_amount` (`collection_amount`),
ADD KEY `idx_collection_method` (`collection_method`),
ADD KEY `idx_collection_date` (`collection_date`);

-- 创建收款信息视图（可选，用于简化查询）
CREATE VIEW `v_order_collection_info` AS
SELECT 
    o.id as order_id,
    o.order_no,
    o.order_type,
    o.business_line,
    o.project_name,
    o.total_amount,
    o.paid_amount,
    o.refund_amount,
    o.payment_status,
    o.order_status,
    o.collection_amount,
    o.collection_method,
    o.collection_date,
    o.operator_name,
    o.collection_remark,
    c.collection_no,
    c.collection_type,
    c.collection_status,
    c.create_time as collection_create_time,
    c.operator_id,
    c.operator_role
FROM `publicbiz_order` o
LEFT JOIN `publicbiz_order_collection` c ON o.id = c.order_id AND c.deleted = 0
WHERE o.deleted = 0;
