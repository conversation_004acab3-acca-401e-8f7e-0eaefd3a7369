package cn.bztmaster.cnt.module.publicbiz.service.employer;

import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.CarouselListRespVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 轮播图服务测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class CarouselServiceTest {

    @Resource
    private CarouselService carouselService;

    @Test
    public void testGetCarouselList() {
        // 测试获取雇主端轮播图
        CarouselListRespVO result = carouselService.getCarouselList("employer", 1, 10, 1);

        assertNotNull(result);
        assertNotNull(result.getList());
        assertTrue(result.getTotal() >= 0);
        assertEquals(1, result.getPage());
        assertEquals(10, result.getPageSize());
        assertTrue(result.getTotalPages() >= 0);

        System.out.println("雇主端轮播图总数: " + result.getTotal());
        System.out.println("当前页轮播图数量: " + result.getList().size());
    }

    @Test
    public void testGetCarouselListWithInvalidPage() {
        // 测试无效页码
        CarouselListRespVO result = carouselService.getCarouselList("employer", 999, 10, 1);

        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(0, result.getList().size());
        assertEquals(999, result.getPage());
    }

    @Test
    public void testGetCarouselListWithLargePageSize() {
        // 测试超过最大页大小限制
        CarouselListRespVO result = carouselService.getCarouselList("employer", 1, 100, 1);

        assertNotNull(result);
        assertEquals(50, result.getPageSize()); // 应该被限制为50
    }

    @Test
    public void testGetCarouselListForAuntPlatform() {
        // 测试获取阿姨端轮播图
        CarouselListRespVO result = carouselService.getCarouselList("aunt", 1, 10, 1);

        assertNotNull(result);
        assertNotNull(result.getList());

        System.out.println("阿姨端轮播图总数: " + result.getTotal());
        System.out.println("当前页轮播图数量: " + result.getList().size());
    }
}