package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * 考题分类分页查询 Request VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "资源中心 - 考题分类分页查询 Request VO")
public class QuestionCategoryPageReqVO extends PageParam {

    /**
     * 业务模块筛选
     */
    @Schema(description = "业务模块筛选", example = "家政业务")
    private String biz;

    /**
     * 分类层级：1-一级，2-二级，3-三级，支持多个层级查询
     */
    @Schema(description = "分类层级", example = "[1,2,3]")
    private List<Integer> level;

    /**
     * 父级分类ID
     */
    @Schema(description = "父级分类ID", example = "1")
    private Long parentId;

}
