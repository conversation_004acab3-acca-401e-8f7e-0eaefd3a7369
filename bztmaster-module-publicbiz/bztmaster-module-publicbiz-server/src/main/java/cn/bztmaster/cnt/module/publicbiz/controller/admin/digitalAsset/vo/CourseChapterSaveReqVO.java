package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 课程章节 - 新增/更新 Request VO
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/components/ManagementCourseForOnline.vue
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "课程章节 - 新增/更新 Request VO")
public class CourseChapterSaveReqVO {

    @Schema(description = "章节ID", example = "1")
    private Long id;

    @NotNull(message = "课程ID不能为空")
    @Schema(description = "课程ID", example = "123", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long courseId;

    @NotBlank(message = "章节标题不能为空")
    @Schema(description = "章节标题", example = "SWOT分析法基础", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;
}
