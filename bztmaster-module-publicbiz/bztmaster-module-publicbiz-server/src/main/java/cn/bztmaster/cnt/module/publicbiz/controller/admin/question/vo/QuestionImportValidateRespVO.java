package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 考题 - 批量导入校验响应 VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "考题 - 批量导入校验响应 VO")
public class QuestionImportValidateRespVO {
    
    @Schema(description = "校验结果列表")
    private List<QuestionImportValidateItemVO> validateList;
    
    @Schema(description = "总记录数")
    private Integer totalCount;
    
    @Schema(description = "有效记录数")
    private Integer validCount;
    
    @Schema(description = "错误记录数")
    private Integer errorCount;

}
