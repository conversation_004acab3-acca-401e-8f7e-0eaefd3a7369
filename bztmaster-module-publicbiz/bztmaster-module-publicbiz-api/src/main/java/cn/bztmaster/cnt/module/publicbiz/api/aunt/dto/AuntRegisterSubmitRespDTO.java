package cn.bztmaster.cnt.module.publicbiz.api.aunt.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 阿姨注册提交响应 DTO
 *
 * <AUTHOR>
 */
@Data
public class AuntRegisterSubmitRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    private String applicationId;

    /**
     * 阿姨OneID
     */
    private String auntOneId;

    /**
     * 申请状态
     */
    private String status;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 预计审核时间
     */
    private String estimatedReviewTime;
}
