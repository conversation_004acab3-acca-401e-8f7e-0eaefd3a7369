package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 工单响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单响应 VO")
@Data
public class WorkOrderRespVO {

    @Schema(description = "主键", example = "1")
    private Long id;

    @Schema(description = "工单编号", example = "WO202312010001")
    private String workOrderNo;

    @Schema(description = "关联订单号", example = "ORD202312010001")
    private String orderNo;

    @Schema(description = "工单标题", example = "服务质量投诉")
    private String workOrderTitle;

    @Schema(description = "工单内容(投诉内容/请假理由)", example = "阿姨服务态度不好，需要更换")
    private String workOrderContent;

    @Schema(description = "工单类型：complaint-投诉/substitution_request-换人申请/take_leave-请假/顶岗/separation_application-离职申请/leave_adjustment-调休", example = "complaint")
    private String workOrderType;

    @Schema(description = "优先级：low-低/medium-中/high-高/urgent-紧急", example = "medium")
    private String priority;

    @Schema(description = "工单状态：pending-待处理/processing-处理中/resolved-已解决/closed-已关闭/approved-已批准/rejected-已驳回", example = "pending")
    private String workOrderStatus;

    @Schema(description = "负责人ID", example = "123")
    private Long assigneeId;

    @Schema(description = "负责人姓名", example = "张三")
    private String assigneeName;

    @Schema(description = "OneID GUID", example = "a1b2c3d4-e5f6-7890-abcd-ef1234567890")
    private String auntOneid;

    @Schema(description = "阿姨姓名", example = "李阿姨")
    private String auntName;

    @Schema(description = "请假类型(1-请假,2-调休)", example = "1")
    private Integer leaveType;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "请假时长(小时)", example = "8.0")
    private BigDecimal durationHours;

    @Schema(description = "请假天数", example = "1.0")
    private BigDecimal durationDays;

    @Schema(description = "审批状态(0-审批中,1-已批准,2-已驳回)", example = "1")
    private Integer status;

    @Schema(description = "审批时间")
    private LocalDateTime approveTime;

    @Schema(description = "审批备注/处理意见", example = "同意请假申请")
    private String approveRemark;

    @Schema(description = "投诉类型：service_quality-服务质量/attitude-服务态度/punctuality-守时问题/other-其他", example = "service_quality")
    private String complaintType;

    @Schema(description = "投诉等级：low-轻微/medium-中等/high-严重/urgent-紧急", example = "medium")
    private String complaintLevel;

    @Schema(description = "投诉时间")
    private LocalDateTime complaintTime;

    @Schema(description = "客户期望文本内容", example = "希望更换服务态度好的阿姨")
    private String customerExpectation;

    @Schema(description = "投诉人ID", example = "456")
    private Long complainerId;

    @Schema(description = "投诉人姓名", example = "王女士")
    private String complainerName;

    @Schema(description = "机构ID", example = "789")
    private Long agencyId;

    @Schema(description = "机构名称", example = "某某家政公司")
    private String agencyName;

    @Schema(description = "重新指派起始日期", example = "2023-12-02")
    private String reassignmentStartDate;

    @Schema(description = "指派新阿姨OneID", example = "b2c3d4e5-f6g7-8901-bcde-f23456789012")
    private String newAuntOneid;

    @Schema(description = "指派新阿姨名称", example = "赵阿姨")
    private String newAuntName;

    @Schema(description = "指派说明内容", example = "原阿姨请假，临时指派新阿姨")
    private String reassignmentDescription;

    @Schema(description = "指派更新时间")
    private LocalDateTime reassignmentUpdateTime;

    @Schema(description = "转派原因(系统基础数据值)", example = "人员调整")
    private String reassignmentReason;

    @Schema(description = "转派备注", example = "原处理人员工作调整")
    private String reassignmentRemark;

    @Schema(description = "备注", example = "需要重点关注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 