package cn.bztmaster.cnt.module.publicbiz.service.aunt;

import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.*;

import java.util.List;

/**
 * 阿姨注册 Service 接口
 *
 * <AUTHOR>
 */
public interface AuntRegisterService {

    /**
     * 提交阿姨注册申请
     */
    AuntRegisterSubmitRespDTO submitRegister(AuntRegisterSubmitReqDTO reqDTO);

    /**
     * 获取阿姨注册申请状态
     */
    AuntRegisterStatusRespDTO getRegisterStatus(String mobile, String openId);

    /**
     * 获取阿姨注册申请详情
     */
    AuntRegisterDetailRespDTO getRegisterDetail(String applicationId);

    /**
     * 重新提交阿姨注册申请
     */
    AuntRegisterSubmitRespDTO resubmitRegister(AuntRegisterResubmitReqDTO reqDTO);

    /**
     * 获取家政机构列表
     */
    List<AgencyListRespDTO> getAgencyList(String keyword);
}
