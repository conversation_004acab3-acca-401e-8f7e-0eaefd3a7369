package cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo;

import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 轮播图保存请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 轮播图保存请求")
@Data
public class AdminCarouselSaveReqVO {

    @Schema(description = "轮播图ID（更新时必填）", example = "1024")
    private Long id;

    @Schema(description = "平台：employer-雇主端，aunt-阿姨端", requiredMode = Schema.RequiredMode.REQUIRED, example = "employer")
    @NotBlank(message = "平台不能为空")
    @Size(max = 20, message = "平台长度不能超过20个字符")
    @DiffLogField(name = "平台")
    private String platform;

    @Schema(description = "轮播图标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "专业月嫂服务")
    @NotBlank(message = "轮播图标题不能为空")
    @Size(max = 200, message = "轮播图标题长度不能超过200个字符")
    @DiffLogField(name = "轮播图标题")
    private String carouselTitle;

    @Schema(description = "轮播图片URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com/images/carousel1.jpg")
    @NotBlank(message = "轮播图片URL不能为空")
    @Size(max = 500, message = "轮播图片URL长度不能超过500个字符")
    @DiffLogField(name = "轮播图片URL")
    private String carouselImageUrl;

    @Schema(description = "跳转链接URL", example = "/service/maternity")
    @Size(max = 500, message = "跳转链接URL长度不能超过500个字符")
    @DiffLogField(name = "跳转链接URL")
    private String carouselLinkUrl;

    @Schema(description = "排序值，数字越小越靠前", example = "1")
    @DiffLogField(name = "排序值")
    private Integer sortOrder;

    @Schema(description = "状态：1-启用，0-禁用", example = "1")
    @DiffLogField(name = "状态")
    private Integer status;

    @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
    @DiffLogField(name = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
    @DiffLogField(name = "结束时间")
    private LocalDateTime endTime;
} 