<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO">
        <id column="id" property="id"/>
        <result column="work_order_no" property="workOrderNo"/>
        <result column="order_no" property="orderNo"/>
        <result column="work_order_title" property="workOrderTitle"/>
        <result column="work_order_content" property="workOrderContent"/>
        <result column="work_order_type" property="workOrderType"/>
        <result column="priority" property="priority"/>
        <result column="work_order_status" property="workOrderStatus"/>
        <result column="assignee_id" property="assigneeId"/>
        <result column="assignee_name" property="assigneeName"/>
        <result column="aunt_oneid" property="auntOneid"/>
        <result column="aunt_name" property="auntName"/>
        <result column="leave_type" property="leaveType"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="duration_hours" property="durationHours"/>
        <result column="duration_days" property="durationDays"/>
        <result column="status" property="status"/>
        <result column="approve_time" property="approveTime"/>
        <result column="approve_remark" property="approveRemark"/>
        <result column="complaint_type" property="complaintType"/>
        <result column="complaint_level" property="complaintLevel"/>
        <result column="complaint_time" property="complaintTime"/>
        <result column="customer_expectation" property="customerExpectation"/>
        <result column="complainer_id" property="complainerId"/>
        <result column="complainer_name" property="complainerName"/>
        <result column="agency_id" property="agencyId"/>
        <result column="agency_name" property="agencyName"/>
        <result column="reassignment_start_date" property="reassignmentStartDate"/>
        <result column="new_aunt_oneid" property="newAuntOneid"/>
        <result column="new_aunt_name" property="newAuntName"/>
        <result column="reassignment_description" property="reassignmentDescription"/>
        <result column="reassignment_update_time" property="reassignmentUpdateTime"/>
        <result column="reassignment_reason" property="reassignmentReason"/>
        <result column="reassignment_remark" property="reassignmentRemark"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, work_order_no, order_no, work_order_title, work_order_content, work_order_type, priority, 
        work_order_status, assignee_id, assignee_name, aunt_oneid, aunt_name, leave_type, start_time, 
        end_time, duration_hours, duration_days, status, approve_time, approve_remark, complaint_type, 
        complaint_level, complaint_time, customer_expectation, complainer_id, complainer_name, agency_id, 
        agency_name, reassignment_start_date, new_aunt_oneid, new_aunt_name, reassignment_description, 
        reassignment_update_time, reassignment_reason, reassignment_remark, remark, creator, create_time, 
        updater, update_time, deleted, tenant_id
    </sql>

    <!-- 根据工单编号查询工单 -->
    <select id="selectByWorkOrderNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_work_order
        WHERE work_order_no = #{workOrderNo}
        AND deleted = 0
    </select>

    <!-- 根据订单号查询工单列表 -->
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_work_order
        WHERE order_no = #{orderNo}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据机构ID查询工单列表 -->
    <select id="selectByAgencyId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_work_order
        WHERE agency_id = #{agencyId}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询工单列表 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_work_order
        <where>
            deleted = 0
            <if test="query.workOrderType != null and query.workOrderType != ''">
                AND work_order_type = #{query.workOrderType}
            </if>
            <if test="query.workOrderStatus != null and query.workOrderStatus != ''">
                AND work_order_status = #{query.workOrderStatus}
            </if>
            <if test="query.priority != null and query.priority != ''">
                AND priority = #{query.priority}
            </if>
            <if test="query.agencyName != null and query.agencyName != ''">
                AND agency_name LIKE CONCAT('%', #{query.agencyName}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper> 