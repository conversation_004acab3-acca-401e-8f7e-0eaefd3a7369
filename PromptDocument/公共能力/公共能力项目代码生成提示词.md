\---

description: 

globs: 

alwaysApply: false

\---

\## 1. 自动生成方案说明


\### 步骤二：页面设计的相关数据表

-- 商机主表

CREATE TABLE `publicbiz_business` (

  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',

  `tenant_id` BIGINT COMMENT '租户ID',

  `name` VARCHAR(128) NOT NULL COMMENT '商机名称',

  `customer_id` BIGINT NOT NULL COMMENT '关联客户ID',

  `customer_name` VARCHAR(128) NOT NULL COMMENT '关联客户名称',

  `business_type` VARCHAR(32) NOT NULL COMMENT '业务模块（高校/培训/认证）',

  `total_price` DECIMAL(18,2) DEFAULT 0 COMMENT '商机金额(元)',

  `business_stage` VARCHAR(32) NOT NULL COMMENT '销售阶段（方案报价/商务谈判/需求分析）',

  `owner_user_id` BIGINT NOT NULL COMMENT '销售负责人ID',

  `owner_user_name` VARCHAR(64) NOT NULL COMMENT '销售负责人姓名',

  `creator` VARCHAR(64) COMMENT '创建人',

  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

  `updater` VARCHAR(64) COMMENT '更新人',

  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  UNIQUE KEY `uk_business_name` (`name`),

  KEY `idx_customer_id` (`customer_id`),

  KEY `idx_owner_user_id` (`owner_user_id`)

) COMMENT='商机主表';

-- 商机跟进表

CREATE TABLE `publicbiz_business_followup` (

  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',

  `tenant_id` BIGINT COMMENT '租户ID',

  `business_id` BIGINT NOT NULL COMMENT '商机ID',

  `content` TEXT NOT NULL COMMENT '跟进内容',

  `follow_time` DATETIME NOT NULL COMMENT '跟进时间',

  `follow_user_id` BIGINT NOT NULL COMMENT '跟进人ID',

  `follow_user_name` VARCHAR(64) NOT NULL COMMENT '跟进人姓名',

  `creator` VARCHAR(64) COMMENT '创建人',

  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

  `updater` VARCHAR(64) COMMENT '更新人',

  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  KEY `idx_business_id` (`business_id`)

) COMMENT='商机跟进表';

-- 商机操作日志表（可选）

CREATE TABLE `publicbiz_business_log` (

  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',

  `tenant_id` BIGINT COMMENT '租户ID',

  `business_id` BIGINT NOT NULL COMMENT '商机ID',

  `action` VARCHAR(32) NOT NULL COMMENT '操作类型（新建/编辑/删除/跟进等）',

  `content` TEXT COMMENT '详情',

  `action_time` DATETIME NOT NULL COMMENT '操作时间',

  `action_user_id` BIGINT NOT NULL COMMENT '操作人ID',

  `action_user_name` VARCHAR(64) NOT NULL COMMENT '操作人姓名',

  `creator` VARCHAR(64) COMMENT '创建人',

  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

  `updater` VARCHAR(64) COMMENT '更新人',

  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  KEY `idx_business_id` (`business_id`)

) COMMENT='商机操作日志表';





\### 步骤三：确定后端接口与分层结构

\- 每个API和页面功能，生成一个对应的后端Controller（RESTful风格）。

\- 每个业务实体，生成VO/DTO、Service接口与实现、Mapper接口（如涉及数据库）、Convert类、枚举、API接口等。

\- 目录结构严格参考 bztmaster-module-system项目，每一层都需要，具体目录格式如下：

 bztmaster-module-publicbiz/
├── bztmaster-module-publicbiz-api/
│   ├── pom.xml
│   ├── .flattened-pom.xml
│   └── src/
│       └── main/
│           └── java/
│               └── cn/
│                   └── bztmaster/
│                       └── cnt/
│                           └── module/
│                               └── publicbiz/
│                                   └── api/
│                                       └── business/
│                                           ├── BusinessApi.java
│                                           └── dto/
│                                               ├── BusinessRespDTO.java
│                                               ├── BusinessSaveReqDTO.java
│                                               ├── BusinessPageReqDTO.java
│                                               └── ...（其它DTO/VO/ReqVO等）
│                                   └── enums/
│                                       └── ...（如有枚举）
├── bztmaster-module-publicbiz-server/
│   ├── pom.xml
│   ├── .flattened-pom.xml
│   ├── Dockerfile
│   └── src/
│       ├── main/
│       │   ├── java/
│       │   │   └── cn/
│       │   │       └── bztmaster/
│       │   │           └── cnt/
│       │   │               └── module/
│       │   │                   └── publicbiz/
│       │   │                       ├── controller/
│       │   │                       │   └── admin/
│       │   │                       │       └── business/
│       │   │                       │           ├── BusinessController.java
│       │   │                       │           └── vo/
│       │   │                       │               ├── BusinessRespVO.java
│       │   │                       │               ├── BusinessSaveReqVO.java
│       │   │                       │               ├── BusinessPageReqVO.java
│       │   │                       │               └── ...（其它VO/ReqVO等）
│       │   │                       ├── dal/
│       │   │                       │   ├── mysql/
│       │   │                       │   │   └── business/
│       │   │                       │   │       └── BusinessMapper.java
│       │   │                       │   ├── dataobject/
│       │   │                       │   │   └── business/
│       │   │                       │   │       └── BusinessDO.java
│       │   │                       ├── service/
│       │   │                       │   └── business/
│       │   │                       │       ├── impl/
│       │   │                       │       │   └── BusinessServiceImpl.java
│       │   │                       │       └── BusinessService.java
│       │   │                       ├── convert/
│       │   │                       │   └── business/
│       │   │                       │       └── BusinessConvert.java
│       │   │                       └── ...（其它如 util、framework、job、mq 等可选）
│       │   └── resources/
│       │       └── mapper/
│       │           └── BusinessMapper.xml
│       │       └── application.yaml
│       │       └── ...（其它配置文件）
│       └── test/
│           └── java/
│               └── ...（测试代码结构）
├── pom.xml
└── .flattened-pom.xml



\### 步骤四：自动生成代码

\- Controller：每个API生成一个对应的Controller方法，路径、方法、参数与前端一致，带有Swagger注解。

\- VO/DTO：根据前端参数和表单字段生成请求VO、响应VO，带有参数校验注解。

\- Service/Impl：生成接口和实现，包含CRUD和具体的业务方法，方法注释详细。

\- Mapper/DO：如涉及数据库，生成MyBatis Mapper接口和DO实体，字段与VO/DTO一致。

\- Convert：生成VO与DO、DTO的转换类。

\- Enums：如有枚举字段，自动生成枚举类。

\- API接口：如有远程调用需求，生成Feign接口。

\- 所有类和方法都带有详细注释，对应DTO、VO与DO对象也生成详细注释，便于初学者理解。

\- 前端与后端对应的字段信息一定要确保能对应一致。

  

\### 步骤五：自动补全与TODO

\- 对于前端未明确的字段类型、业务逻辑，自动推断或以// TODO注释标记，便于后续补充。

\- 生成后的代码直接更新到指定的项目bztmaster-module-publicbiz模块下对应的目录中。 



