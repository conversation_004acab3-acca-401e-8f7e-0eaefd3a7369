\---

description:

globs:

alwaysApply: false

\---

请结合@PromptDocument/公共能力/资源中心/合作伙伴/合作伙伴接口文档.md接口文档直接帮我生成后端代码，

\### 步骤一：熟悉相关表结构
    
    合作伙伴相关表结构文件地址：PromptDocument/公共能力/资源中心/合作伙伴/合作伙伴表结构.md


\### 步骤二：确定后端接口与分层结构

\- 每个API和页面功能，生成一个对应的后端Controller（RESTful风格）。

\- 每个业务实体，生成VO/DTO、Service接口与实现、Mapper接口（如涉及数据库）、Convert类、枚举、API接口等。

\- 目录结构严格参考 bztmaster-module-system项目，每一层都需要，具体目录格式如下：

bztmaster-module-publicbiz/
├── bztmaster-module-publicbiz-api/
│   ├── pom.xml
│   ├── .flattened-pom.xml
│   └── src/
│       └── main/
│           └── java/
│               └── cn/
│                   └── bztmaster/
│                       └── cnt/
│                           └── module/
│                               └── publicbiz/
│                                   └── api/
│                                       └── partner/
│                                           ├── PublicBizPartnerApi.java
│                                           └── dto/
│                                               ├── PartnerRespDTO.java
│                                               ├── PartnerSaveReqDTO.java
│                                               ├── PartnerPageReqDTO.java
│                                               └── ...（其它DTO/VO/ReqVO等）
│                                   └── enums/
│                                       └── ...（如有枚举）
├── bztmaster-module-publicbiz-server/
│   ├── pom.xml
│   ├── .flattened-pom.xml
│   ├── Dockerfile
│   └── src/
│       ├── main/
│       │   ├── java/
│       │   │   └── cn/
│       │   │       └── bztmaster/
│       │   │           └── cnt/
│       │   │               └── module/
│       │   │                   └── publicbiz/
│       │   │                       ├── controller/
│       │   │                       │   └── admin/
│       │   │                       │       └── partner/
│       │   │                       │           ├── PartnerController.java
│       │   │                       │           └── vo/
│       │   │                       │               ├── PartnerRespVO.java
│       │   │                       │               ├── PartnerSaveReqVO.java
│       │   │                       │               ├── PartnerPageReqVO.java
│       │   │                       │               └── ...（其它VO/ReqVO等）
│       │   │                       ├── dal/
│       │   │                       │   ├── mysql/
│       │   │                       │   │   └── partner/
│       │   │                       │   │       └── PartnerMapper.java
│       │   │                       │   ├── dataobject/
│       │   │                       │   │   └── partner/
│       │   │                       │   │       └── PartnerDO.java
│       │   │                       ├── service/
│       │   │                       │   └── partner/
│       │   │                       │       ├── impl/
│       │   │                       │       │   └── PartnerServiceImpl.java
│       │   │                       │       └── PartnerService.java
│       │   │                       ├── convert/
│       │   │                       │   └── partner/
│       │   │                       │       └── PartnerConvert.java
│       │   │                       └── ...（其它如 util、framework、job、mq 等可选）
│       │   └── resources/
│       │       └── mapper/
│       │           └── PartnerMapper.xml
│       │           └── ...（其它xml文件）
│       │       └── application.yaml
│       │       └── ...（其它配置文件）
│       └── test/
│           └── java/
│               └── ...（测试代码结构）
├── pom.xml
└── .flattened-pom.xml

按照以上目录结构在 bztmaster-module-publicbiz 模块下新增合作伙伴的相关文件
注意：确保所有 DTO、Service、ServiceImpl、Mapper、Convert 等其它层 风格、注解、结构与 business 目录完全一致

\### 步骤三：自动生成代码

\- Controller：每个API生成一个对应的Controller方法，路径、方法、参数与前端一致，带有Swagger注解。

\- VO/DTO：根据前端参数和表单字段生成请求VO、响应VO，带有参数校验注解。

\- Service/Impl：生成接口和实现，包含CRUD和具体的业务方法，方法注释详细。

\- Mapper/DO：如涉及数据库，生成MyBatis Mapper接口和DO实体，字段与VO/DTO一致。

\- Convert：生成VO与DO、DTO的转换类。

\- Enums：如有枚举字段，自动生成枚举类。

\- API接口：如有远程调用需求，生成Feign接口。

\- 所有类和方法都带有详细注释，对应DTO、VO与DO对象也生成详细注释，便于初学者理解。

\- 前端与后端对应的字段信息一定要确保能对应一致。



\### 步骤四：自动补全与TODO

\- 对于前端未明确的字段类型、业务逻辑，自动推断或以// TODO注释标记，便于后续补充。

\- 生成后的代码直接更新到指定的项目bztmaster-module-publicbiz模块下对应的目录中。 


现在需要实现系统操作日志的功能：
1. 请先仔细阅读文件：PromptDocument\系统日志\操作日志.md，整理出操作日志的实现方式,严格按照文档内的写法以及文件路径来实现合作伙伴的相关日志记录。
2. 合作伙伴的枚举值写入文件路径：src/main/java/cn/bztmaster/cnt/module/publicbiz/enums/LogRecordConstants.java
3.合作伙伴表结构：
    
CREATE TABLE `publicbiz_partner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(128) NOT NULL DEFAULT '' COMMENT '机构名称',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '机构类型',
  `biz` varchar(32) NOT NULL DEFAULT '' COMMENT '业务模块',
  `status` varchar(32) NOT NULL DEFAULT '' COMMENT '合作状态',
  `risk` varchar(16) NOT NULL DEFAULT '' COMMENT '风险等级',
  `owner` bigint(20) NOT NULL DEFAULT '0' COMMENT '我方负责人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作伙伴';
```
4. 先实现新增合作伙伴时的操作日志记录，需要记录的字段如下：
    name、type

