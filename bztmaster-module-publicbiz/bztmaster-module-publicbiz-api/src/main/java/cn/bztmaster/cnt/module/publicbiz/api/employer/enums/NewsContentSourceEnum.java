package cn.bztmaster.cnt.module.publicbiz.api.employer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资讯内容来源枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum NewsContentSourceEnum {

    MANUAL("manual", "手动编写"),
    MATERIAL("material", "关联素材库");

    /**
     * 来源值
     */
    private final String value;

    /**
     * 来源描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 来源值
     * @return 来源枚举
     */
    public static NewsContentSourceEnum getByValue(String value) {
        for (NewsContentSourceEnum source : values()) {
            if (source.getValue().equals(value)) {
                return source;
            }
        }
        return null;
    }

    /**
     * 判断值是否有效
     *
     * @param value 来源值
     * @return 是否有效
     */
    public static boolean isValidValue(String value) {
        return getByValue(value) != null;
    }
} 