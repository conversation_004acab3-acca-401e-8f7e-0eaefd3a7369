package cn.bztmaster.cnt.module.publicbiz.service.question;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionOptionVO;
import cn.bztmaster.cnt.module.publicbiz.convert.question.QuestionConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 考题操作日志调试测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
public class QuestionLogDebugTest {

    @Resource
    private QuestionService questionService;

    @Test
    public void testAnswerFieldChange() {
        // 1. 创建一个测试考题
        QuestionSaveReqVO createReqVO = createBasicQuestion();
        createReqVO.setAnswer("A"); // 初始答案为 A
        Long questionId = questionService.createQuestion(createReqVO);
        System.out.println("创建考题成功，ID: " + questionId);

        // 2. 更新考题，只修改答案字段
        QuestionSaveReqVO updateReqVO = createBasicQuestion();
        updateReqVO.setId(questionId);
        updateReqVO.setAnswer("B"); // 修改答案为 B
        
        System.out.println("准备更新考题，修改答案从 A 到 B");
        questionService.updateQuestion(updateReqVO);
        System.out.println("更新考题完成");
        
        System.out.println("请检查操作日志表，应该记录：参考答案由【A】修改为【B】");
    }

    @Test
    public void testMultipleFieldsChange() {
        // 1. 创建一个测试考题
        QuestionSaveReqVO createReqVO = createBasicQuestion();
        createReqVO.setTitle("原始题目标题");
        createReqVO.setAnswer("A");
        createReqVO.setType("单选题");
        Long questionId = questionService.createQuestion(createReqVO);
        System.out.println("创建考题成功，ID: " + questionId);

        // 2. 更新多个字段
        QuestionSaveReqVO updateReqVO = createBasicQuestion();
        updateReqVO.setId(questionId);
        updateReqVO.setTitle("修改后的题目标题");
        updateReqVO.setAnswer("C");
        updateReqVO.setType("多选题");
        
        System.out.println("准备更新考题，修改多个字段");
        questionService.updateQuestion(updateReqVO);
        System.out.println("更新考题完成");
        
        System.out.println("请检查操作日志表，应该记录多个字段的变更");
    }

    @Test
    public void testConvertMethod() {
        // 测试转换方法是否正常工作
        QuestionDO questionDO = new QuestionDO();
        questionDO.setId(1L);
        questionDO.setTitle("测试题目");
        questionDO.setAnswer("A");
        questionDO.setType("单选题");
        questionDO.setLevel1Name("一级分类");
        questionDO.setLevel2Name("二级分类");
        questionDO.setLevel3Name("三级分类");
        
        try {
            QuestionSaveReqVO converted = QuestionConvert.INSTANCE.convertToSaveReqVO(questionDO);
            System.out.println("转换成功:");
            System.out.println("Title: " + converted.getTitle());
            System.out.println("Answer: " + converted.getAnswer());
            System.out.println("Type: " + converted.getType());
            System.out.println("Level1Name: " + converted.getLevel1Name());
        } catch (Exception e) {
            System.err.println("转换失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private QuestionSaveReqVO createBasicQuestion() {
        QuestionSaveReqVO reqVO = new QuestionSaveReqVO();
        reqVO.setLevel1Name("职业技能等级认定");
        reqVO.setLevel1Code("ZY001");
        reqVO.setLevel2Name("家政服务类");
        reqVO.setLevel2Code("JZ001");
        reqVO.setLevel3Name("家政服务员");
        reqVO.setLevel3Code("JZFW001");
        reqVO.setCertName("职业道德基础");
        reqVO.setCertCode("KP001");
        reqVO.setTitle("测试考题标题");
        reqVO.setType("单选题");
        reqVO.setAnswer("A");
        reqVO.setBiz("家政业务");
        reqVO.setBizName("家政服务业务");
        reqVO.setDifficulty(1);
        reqVO.setScore(new BigDecimal("2.0"));

        // 添加选项
        QuestionOptionVO optionA = new QuestionOptionVO();
        optionA.setOptionKey("A");
        optionA.setOptionContent("选项A");
        optionA.setIsCorrect(true);
        optionA.setOptionType("choice");
        optionA.setSortOrder(1);

        QuestionOptionVO optionB = new QuestionOptionVO();
        optionB.setOptionKey("B");
        optionB.setOptionContent("选项B");
        optionB.setIsCorrect(false);
        optionB.setOptionType("choice");
        optionB.setSortOrder(2);

        reqVO.setOptions(Arrays.asList(optionA, optionB));
        return reqVO;
    }
}
