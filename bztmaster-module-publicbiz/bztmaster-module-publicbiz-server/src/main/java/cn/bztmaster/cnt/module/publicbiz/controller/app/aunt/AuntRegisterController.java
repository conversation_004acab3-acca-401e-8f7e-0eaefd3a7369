package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.*;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.aunt.AuntRegisterConvert;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntRegisterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 阿姨注册")
@RestController
@RequestMapping("/publicbiz/aunt/register")
@Validated
public class AuntRegisterController {

    @Resource
    private AuntRegisterService auntRegisterService;

    @PostMapping("/submit")
    @Operation(summary = "提交阿姨注册申请")
    @PermitAll
    public CommonResult<AuntRegisterSubmitRespVO> submitRegister(@Valid @RequestBody AuntRegisterSubmitReqVO reqVO) {
        // 转换为DTO并调用服务
        AuntRegisterSubmitRespDTO respDTO = auntRegisterService.submitRegister(AuntRegisterConvert.INSTANCE.convert(reqVO));
        return success(AuntRegisterConvert.INSTANCE.convert(respDTO));
    }

    @GetMapping("/status")
    @Operation(summary = "获取阿姨注册申请状态")
    @Parameter(name = "mobile", description = "手机号", required = true)
    @Parameter(name = "openId", description = "微信openId", required = true)
    @PermitAll
    public CommonResult<AuntRegisterStatusRespVO> getRegisterStatus(@RequestParam("mobile") String mobile,
                                                                   @RequestParam("openId") String openId) {
        // 调用服务并转换
        AuntRegisterStatusRespDTO respDTO = auntRegisterService.getRegisterStatus(mobile, openId);
        return success(AuntRegisterConvert.INSTANCE.convert(respDTO));
    }

    @GetMapping("/detail")
    @Operation(summary = "获取阿姨注册申请详情")
    @Parameter(name = "applicationId", description = "申请ID", required = true)
    @PermitAll
    public CommonResult<AuntRegisterDetailRespVO> getRegisterDetail(@RequestParam("applicationId") String applicationId) {
        // 调用服务并转换
        AuntRegisterDetailRespDTO respDTO = auntRegisterService.getRegisterDetail(applicationId);
        return success(AuntRegisterConvert.INSTANCE.convert(respDTO));
    }

    @PostMapping("/resubmit")
    @Operation(summary = "重新提交阿姨注册申请")
    @PermitAll
    public CommonResult<AuntRegisterSubmitRespVO> resubmitRegister(@Valid @RequestBody AuntRegisterResubmitReqVO reqVO) {
        // 转换为DTO并调用服务
        AuntRegisterSubmitRespDTO respDTO = auntRegisterService.resubmitRegister(AuntRegisterConvert.INSTANCE.convert(reqVO));
        return success(AuntRegisterConvert.INSTANCE.convert(respDTO));
    }

    @GetMapping("/agencies")
    @Operation(summary = "获取家政机构列表")
    @Parameter(name = "keyword", description = "搜索关键词")
    @PermitAll
    public CommonResult<List<AgencyListRespVO>> getAgencyList(@RequestParam(value = "keyword", required = false) String keyword) {
        // 调用服务并转换
        List<AgencyListRespDTO> respDTOList = auntRegisterService.getAgencyList(keyword);
        return success(AuntRegisterConvert.INSTANCE.convertList(respDTOList));
    }
}
