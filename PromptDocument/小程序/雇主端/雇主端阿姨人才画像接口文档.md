# 雇主端阿姨人才画像接口文档

## 1. 接口概述

本文档描述了雇主端查看阿姨人才画像相关的接口，包括阿姨基本信息、资质证书、服务评价等功能。

## 2. 接口列表

### 2.1 获取阿姨详情信息

**接口地址：** `/publicbiz/employer/aunt/detail`

**请求方式：** GET

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| auntId | string | 是 | 阿姨ID |

**请求示例：**
```
GET /publicbiz/employer/aunt/detail?auntId=11
```

**响应参数：**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "12345678-1234-1234-1234-123456789012",
    "name": "王阿姨",
    "avatar": "https://example.com/avatar.jpg",
    "rating": 4.5,
    "isVerified": true,
    "isPlatformContract": true,
    "serviceTags": [
      {
        "tagId": 1,
        "tagName": "高级育婴师证",
        "tagType": "certificate"
      },
      {
        "tagId": 2,
        "tagName": "健康证",
        "tagType": "certificate"
      },
      {
        "tagId": 3,
        "tagName": "职业技能证",
        "tagType": "certificate"
      }
    ],
    "certificates": [
      {
        "name": "高级育婴师证",
        "number": "YY123456787654",
        "validUntil": "2025-12-31"
      },
      {
        "name": "健康证",
        "number": "JK123456787654",
        "validUntil": "2025-12-31"
      }
    ],
    "stats": {
      "families": 15,
      "years": 5,
      "responseTime": 3,
      "repurchaseRate": 98
    },
    "profile": {
      "introduction": "从事家政行业5年,服务超过200个家庭。持有高级育婴师证、健康证。擅长高端住宅保洁、深度收纳整理与0-3岁婴幼儿看护。为人耐心、细致,责任心强。",
      "hometown": "河南",
      "age": 35,
      "experience": 5
    }
  }
}
```

**响应字段说明：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 阿姨ID |
| name | string | 阿姨姓名 |
| avatar | string | 头像URL |
| rating | decimal | 评分(1.0-5.0) |
| isVerified | boolean | 是否实名认证 |
| isPlatformContract | boolean | 是否平台自营签约 |
| serviceTags | array | 服务标签列表，包含tagId、tagName、tagType字段 |
| certificates | array | 资质证书列表 |
| stats | object | 统计数据 |
| profile | object | 个人简介信息 |

### 2.2 获取阿姨评价列表

**接口地址：** `/publicbiz/employer/aunt/reviews`

**请求方式：** GET

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| auntId | string | 是 | 阿姨ID |
| page | integer | 否 | 页码，默认1 |
| size | integer | 否 | 每页数量，默认10 |
| rating | integer | 否 | 评分筛选(1-5) |

**请求示例：**
```
GET /publicbiz/employer/aunt/reviews?auntId=1
```

**响应参数：**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "pages": 3,
    "current": 1,
    "size": 10,
    "records": [
      {
        "id": 1,
        "reviewerName": "李女士",
        "reviewerAvatar": "https://example.com/user1.jpg",
        "rating": 5.0,
        "date": "2024-05-15",
        "tags": ["专业", "细心", "守时"],
        "content": "王阿姨非常专业，照顾宝宝很细心，服务态度也很好，非常满意！",
        "images": [],
        "isAnonymous": false,
        "likeCount": 3
      },
      {
        "id": 2,
        "reviewerName": "张先生",
        "rating": 5.0,
        "date": "2024-04-20",
        "tags": ["经验丰富", "耐心"],
        "content": "阿姨经验很丰富，育儿知识很专业，宝宝很喜欢她，会继续合作。",
        "images": [],
        "isAnonymous": false,
        "likeCount": 2
      }
    ]
  }
}
```

**响应字段说明：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| total | integer | 总记录数 |
| pages | integer | 总页数 |
| current | integer | 当前页码 |
| size | integer | 每页数量 |
| records | array | 评价记录列表 |

**评价记录字段说明：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | integer | 评价ID |
| reviewerName | string | 评价人姓名 |
| reviewerAvatar | string | 评价人头像URL |
| rating | decimal | 评分(1.0-5.0) |
| date | string | 评价日期 |
| tags | array | 评价标签 |
| content | string | 评价内容 |
| images | array | 评价图片URL列表 |
| isAnonymous | boolean | 是否匿名评价 |
| likeCount | integer | 点赞数 |

### 2.3 获取阿姨资质证书

**接口地址：** `/publicbiz/employer/aunt/certificates`

**请求方式：** GET

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| auntId | string | 是 | 阿姨ID |

**请求示例：**
```
GET /publicbiz/employer/aunt/certificates?auntId=1
```

**响应参数：**

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "fileType": "skill_cert",
      "fileName": "高级育婴师证",
      "fileUrl": "https://example.com/cert1.jpg",
      "certNumber": "YY123456787654",
      "validUntil": "2025-12-31",
      "sortOrder": 1,
      "status": 1
    },
    {
      "id": 2,
      "fileType": "health_cert",
      "fileName": "健康证",
      "fileUrl": "https://example.com/cert2.jpg",
      "certNumber": "JK123456787654",
      "validUntil": "2025-12-31",
      "sortOrder": 2,
      "status": 1
    }
  ]
}
```

**响应字段说明：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | integer | 证书ID |
| fileType | string | 文件类型(id_card/health_cert/skill_cert/other) |
| fileName | string | 证书名称 |
| fileUrl | string | 证书图片URL |
| certNumber | string | 证书编号 |
| validUntil | string | 有效期至 |
| sortOrder | integer | 排序 |
| status | integer | 状态(1-有效,0-无效) |



## 3. 数据库表结构说明

### 3.1 阿姨基本信息表 (publicbiz_practitioner)

存储阿姨的基本信息，包括个人资料、服务统计等。

**主要字段：**
- `aunt_oneid`: 阿姨OneID (GUID)
- `name`: 阿姨姓名
- `avatar`: 头像URL
- `rating`: 评级 (1.0-5.0)
- `experience_years`: 从业年限
- `total_orders`: 累计服务单数
- `customer_satisfaction`: 客户满意度评分
- `profile`: 阿姨简介

### 3.2 阿姨资质文件表 (publicbiz_practitioner_qualification)

存储阿姨的各类资质证书和文件。

**主要字段：**
- `practitioner_oneId`: 阿姨OneID
- `file_type`: 文件类型 (id_card/health_cert/skill_cert/other)
- `file_name`: 文件名
- `file_url`: 文件URL
- `sort_order`: 排序
- `status`: 状态 (1-有效,0-无效)

### 3.3 阿姨评价表 (publicbiz_aunt_review)

存储雇主对阿姨服务的评价信息。

**主要字段：**
- `aunt_id`: 阿姨OneID
- `reviewer_id`: 评价人用户ID
- `reviewer_name`: 评价人姓名
- `rating`: 评分 (1.0-5.0)
- `review_tags`: 评价标签 (JSON格式)
- `review_content`: 评价内容
- `review_images`: 评价图片 (JSON格式)
- `is_anonymous`: 是否匿名评价

### 3.4 用户标签关联表 (talent_user_tag)

存储用户与标签的关联关系。

**主要字段：**
- `user_id`: 用户ID
- `tag_id`: 标签ID
- `tag_type_id`: 标签类型ID

## 4. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 404 | 阿姨信息不存在 |
| 500 | 服务器内部错误 |

## 5. 注意事项

1. 所有接口需要验证用户登录状态
2. 阿姨ID为必填参数，为数字类型
3. 评价列表支持分页查询
4. 资质证书按sort_order字段排序
5. 评价内容支持图片上传，图片URL为数组格式
6. 匿名评价不显示评价人真实姓名
