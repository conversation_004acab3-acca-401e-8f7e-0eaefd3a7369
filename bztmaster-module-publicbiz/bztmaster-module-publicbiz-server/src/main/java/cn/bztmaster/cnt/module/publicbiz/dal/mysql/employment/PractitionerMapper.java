package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PractitionerMapper extends BaseMapperX<PractitionerDO> {

    /**
     * 根据阿姨OneID查询阿姨信息
     *
     * @param auntOneId 阿姨OneID
     * @return 阿姨信息
     */
    PractitionerDO selectByAuntOneId(@Param("auntOneId") String auntOneId);

    /**
     * 根据微信用户openId查询家政人员信息
     * 通过mp_user表的oneid与publicbiz_practitioner表的aunt_oneid关联查询
     *
     * @param openId 微信用户openId
     * @return 家政人员信息
     */
    PractitionerDO selectByOpenId(@Param("openId") String openId);

    /**
     * 根据手机号查询家政人员信息
     * 通过mp_user表的mobile与publicbiz_practitioner表的aunt_oneid关联查询
     *
     * @param mobile 手机号
     * @return 家政人员信息
     */
    PractitionerDO selectByMobile(@Param("mobile") String mobile);

    /**
     * 查询首页阿姨列表
     * 按评级降序排序，获取前N名阿姨
     *
     * @param limit 返回数据条数
     * @return 阿姨列表
     */
    List<PractitionerDO> selectHomeAuntList(@Param("limit") Integer limit);

    /**
     * 根据身份证号查询家政人员信息
     *
     * @param idCard 身份证号
     * @return 家政人员信息
     */
    PractitionerDO selectByIdCard(@Param("idCard") String idCard);
}