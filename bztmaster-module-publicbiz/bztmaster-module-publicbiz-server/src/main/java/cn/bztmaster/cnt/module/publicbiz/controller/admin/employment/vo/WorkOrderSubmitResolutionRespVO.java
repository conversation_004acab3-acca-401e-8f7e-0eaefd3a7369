package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工单提交处理结果响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单提交处理结果响应 VO")
@Data
public class WorkOrderSubmitResolutionRespVO {

    @Schema(description = "工单编号", example = "WO202312010001")
    private String workOrderNo;

    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "提交是否成功", example = "true")
    private Boolean success;
} 