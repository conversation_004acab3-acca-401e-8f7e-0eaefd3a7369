# 高校实践订单收款信息接口文档

## 1. 接口概述和基础信息

### 1.1 基础信息

- **基础路径**: `/publicbiz/order`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token

### 1.2 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-06-21T10:30:00Z"
}
```

## 2. 接口详细说明

### 2.1 确认高校实践订单收款

**接口地址**: `POST /publicbiz/order/confirm-collection`

**功能说明**: 确认高校实践订单的收款信息，将订单状态从待支付更新为执行中

**请求参数**:

| 参数名             | 类型    | 必填 | 说明                                        |
| ------------------ | ------- | ---- | ------------------------------------------- |
| orderId            | Long    | 是   | 订单ID                                      |
| orderNo            | String  | 是   | 订单号                                      |
| paymentStatus      | String  | 是   | 支付状态（paid）                             |
| collectionAmount   | Decimal | 是   | 收款金额                                    |
| collectionMethod   | String  | 是   | 收款方式                                    |
| collectionDate     | String  | 是   | 收款日期（YYYY-MM-DD）                      |
| operatorName       | String  | 是   | 操作人姓名                                  |
| collectionRemark   | String  | 否   | 收款备注                                    |

**响应字段**:

| 字段名  | 类型    | 说明         |
| ------- | ------- | ------------ |
| success | Boolean | 确认是否成功 |

**请求示例**:

```json
{
  "orderId": 1,
  "orderNo": "HP202406001",
  "paymentStatus": "paid",
  "collectionAmount": 580000.00,
  "collectionMethod": "bank_transfer",
  "collectionDate": "2024-06-21",
  "operatorName": "李四",
  "collectionRemark": "银行转账收款，已确认到账"
}
```

**返回示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "success": true
  }
}
```

### 2.2 更新高校实践订单收款信息

**接口地址**: `POST /publicbiz/order/update-collection`

**功能说明**: 更新已收款的高校实践订单的收款信息

**请求参数**:

| 参数名             | 类型    | 必填 | 说明                                        |
| ------------------ | ------- | ---- | ------------------------------------------- |
| orderId            | Long    | 是   | 订单ID                                      |
| orderNo            | String  | 是   | 订单号                                      |
| paymentStatus      | String  | 是   | 支付状态（paid）                             |
| collectionAmount   | Decimal | 是   | 收款金额                                    |
| collectionMethod   | String  | 是   | 收款方式                                    |
| collectionDate     | String  | 是   | 收款日期（YYYY-MM-DD）                      |
| operatorName       | String  | 是   | 操作人姓名                                  |
| collectionRemark   | String  | 否   | 收款备注                                    |

**响应字段**:

| 字段名  | 类型    | 说明         |
| ------- | ------- | ------------ |
| success | Boolean | 更新是否成功 |

**请求示例**:

```json
{
  "orderId": 1,
  "orderNo": "HP202406001",
  "paymentStatus": "paid",
  "collectionAmount": 600000.00,
  "collectionMethod": "wechat",
  "collectionDate": "2024-06-21",
  "operatorName": "王五",
  "collectionRemark": "微信支付收款，金额调整"
}
```

**返回示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "success": true
  }
}
```

### 2.3 获取高校实践订单收款信息

**接口地址**: `GET /publicbiz/order/collection-info`

**功能说明**: 获取高校实践订单的收款信息详情

**请求参数**:

| 参数名 | 类型   | 必填 | 说明   |
| ------ | ------ | ---- | ------ |
| id     | Long   | 是   | 订单ID |

**响应字段**:

| 字段名               | 类型    | 说明             |
| -------------------- | ------- | ---------------- |
| orderId              | Long    | 订单ID           |
| orderNo              | String  | 订单号           |
| paymentStatus        | String  | 支付状态         |
| collectionAmount     | Decimal | 收款金额         |
| collectionMethod     | String  | 收款方式编码     |
| collectionMethodName | String  | 收款方式名称     |
| collectionDate       | String  | 收款日期         |
| operatorName         | String  | 操作人姓名       |
| collectionRemark     | String  | 收款备注         |

**请求示例**:

```http
GET /publicbiz/order/collection-info?id=1
```

**返回示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "orderId": 1,
    "orderNo": "HP202406001",
    "paymentStatus": "paid",
    "collectionAmount": 580000.00,
    "collectionMethod": "bank_transfer",
    "collectionMethodName": "银行转账",
    "collectionDate": "2024-06-21",
    "operatorName": "李四",
    "collectionRemark": "银行转账收款，已确认到账"
  }
}
```

## 3. 数据字典

### 3.1 收款方式（collectionMethod）

| 值            | 说明      |
| ------------- | --------- |
| cash          | 现金      |
| wechat        | 微信支付  |
| alipay        | 支付宝    |
| bank_transfer | 银行转账  |
| pos           | POS机刷卡 |
| other         | 其他      |

### 3.2 支付状态（paymentStatus）

| 值        | 说明   |
| --------- | ------ |
| pending   | 待支付 |
| paid      | 已支付 |
| refunded  | 已退款 |
| cancelled | 已取消 |

### 3.3 订单状态（orderStatus）

| 值               | 说明   |
| ---------------- | ------ |
| draft            | 草稿   |
| pending_approval | 待审批 |
| approving        | 审批中 |
| approved         | 已批准 |
| rejected         | 已拒绝 |
| pending_payment  | 待支付 |
| executing        | 执行中 |
| completed        | 已完成 |
| cancelled        | 已取消 |

## 4. 错误码说明

| 错误码 | 说明                     | 解决方案                     |
| ------ | ------------------------ | ---------------------------- |
| 200    | 操作成功                 | -                            |
| 400    | 请求参数错误             | 检查请求参数格式和必填项     |
| 401    | 未授权                   | 检查认证token是否有效        |
| 403    | 权限不足                 | 检查用户是否有操作权限       |
| 404    | 资源不存在               | 检查订单ID或订单号是否正确   |
| 409    | 业务冲突                 | 检查订单状态是否允许当前操作 |
| 500    | 服务器内部错误           | 联系技术支持                 |
| 1008   | 订单状态不允许收款       | 检查订单当前状态             |
| 1009   | 订单未支付               | 检查订单支付状态             |
| 1010   | 收款金额超过订单总金额   | 检查收款金额是否合理         |

## 5. 业务流程说明

### 5.1 收款确认流程

1. **订单创建**: 创建高校实践订单，状态为"草稿"
2. **订单审批**: 发起审批流程，状态变为"待审批"
3. **审批通过**: 审批通过后，状态变为"已批准"
4. **确认收款**: 确认收款后，状态变为"执行中"，支付状态变为"已支付"
5. **订单执行**: 执行订单内容
6. **订单完成**: 执行完成后，状态变为"已完成"

### 5.2 收款信息验证规则

1. **收款金额验证**: 收款金额不能超过订单总金额
2. **订单状态验证**: 只有"待支付"或"已批准"状态的订单才能确认收款
3. **必填字段验证**: 确认收款时，收款金额、收款方式、收款日期、操作人为必填字段
4. **收款方式验证**: 收款方式必须在系统配置的收款方式范围内

## 6. 注意事项

1. **权限控制**: 收款操作需要"收款"权限
2. **状态流转**: 订单状态变更需要严格按照业务流程进行
3. **日志记录**: 所有收款操作都会记录操作日志，便于审计和追踪
4. **数据一致性**: 收款金额与订单总金额需要保持一致性
5. **操作人记录**: 收款操作必须记录操作人信息，便于责任追溯
6. **收款方式**: 支持多种收款方式，可根据业务需要进行配置
7. **收款备注**: 建议填写详细的收款说明，便于后续查询和核对

## 7. 接口调用示例

### 7.1 确认收款示例

```javascript
// 确认高校实践订单收款
const confirmCollection = async () => {
  const response = await fetch('/publicbiz/order/confirm-collection', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: 'Bearer ' + token
    },
    body: JSON.stringify({
      orderId: 1,
      orderNo: 'HP202406001',
      paymentStatus: 'paid',
      collectionAmount: 580000.00,
      collectionMethod: 'bank_transfer',
      collectionDate: '2024-06-21',
      operatorName: '李四',
      collectionRemark: '银行转账收款，已确认到账'
    })
  })
  return response.json()
}
```

### 7.2 更新收款信息示例

```javascript
// 更新高校实践订单收款信息
const updateCollection = async () => {
  const response = await fetch('/publicbiz/order/update-collection', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: 'Bearer ' + token
    },
    body: JSON.stringify({
      orderId: 1,
      orderNo: 'HP202406001',
      paymentStatus: 'paid',
      collectionAmount: 600000.00,
      collectionMethod: 'wechat',
      collectionDate: '2024-06-21',
      operatorName: '王五',
      collectionRemark: '微信支付收款，金额调整'
    })
  })
  return response.json()
}
```

### 7.3 查询收款信息示例

```javascript
// 获取高校实践订单收款信息
const getCollectionInfo = async (orderId) => {
  const response = await fetch(`/publicbiz/order/collection-info?id=${orderId}`, {
    method: 'GET',
    headers: {
      Authorization: 'Bearer ' + token
    }
  })
  return response.json()
}
```

这份接口文档涵盖了高校实践订单收款信息的所有核心功能，包括确认收款、更新收款信息、查询收款信息等。文档结构清晰，字段说明详细，便于开发者理解和对接使用。
