# mp_user_last_login_identity 表映射完成说明

## 概述

已按照项目规范在 `bztmaster-module-publicbiz` 模块下为 `mp_user_last_login_identity` 表创建了完整的映射结构，所有文件均放在 `employer` 包下。

## 创建的文件列表

### 1. 数据对象层 (DO)
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/dal/dataobject/employer/MpUserLastLoginIdentityDO.java`
- **功能**: 映射数据库表结构，包含所有字段和注解

### 2. 数据访问层 (Mapper)
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/dal/mysql/employer/MpUserLastLoginIdentityMapper.java`
- **功能**: 数据库操作接口，继承 BaseMapperX，提供自定义查询方法

### 3. 枚举类
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/enums/IdentityTypeEnum.java`
- **功能**: 身份类型枚举（EMPLOYER-雇主，AUNT-阿姨，AGENCY-机构）

- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/enums/LoginStatusEnum.java`
- **功能**: 登录状态枚举（1-正常，0-异常）

### 4. 视图对象 (VO)
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/vo/MpUserLastLoginIdentityRespVO.java`
- **功能**: 响应 VO，包含枚举描述字段

- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/vo/MpUserLastLoginIdentitySaveReqVO.java`
- **功能**: 保存请求 VO，包含参数校验注解

### 5. 对象转换层 (Convert)
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/convert/employer/MpUserLastLoginIdentityConvert.java`
- **功能**: MapStruct 对象转换，自动处理枚举描述转换

### 6. 业务逻辑层 (Service)
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/service/employer/MpUserLastLoginIdentityService.java`
- **功能**: 业务逻辑接口定义

- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/service/employer/impl/MpUserLastLoginIdentityServiceImpl.java`
- **功能**: 业务逻辑实现，包含完整的 CRUD 操作

### 7. 控制器层 (Controller)
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/MpUserLastLoginIdentityController.java`
- **功能**: REST API 控制器，提供完整的接口

### 8. HTTP 测试文件
- **文件路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/MpUserLastLoginIdentityController.http`
- **功能**: 接口测试文件，包含所有接口的测试用例

### 9. 错误码常量
- **修改文件**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/enums/ErrorCodeConstants.java`
- **新增错误码**: `MP_USER_LAST_LOGIN_IDENTITY_NOT_EXISTS`

## 主要功能特性

### 1. 完整的 CRUD 操作
- 创建小程序用户最后登录身份记录
- 更新小程序用户最后登录身份记录
- 删除小程序用户最后登录身份记录
- 查询小程序用户最后登录身份记录

### 2. 多种查询方式
- 根据主键 ID 查询
- 根据用户 ID 和 openid 查询
- 根据 openid 查询
- 根据用户 ID 查询

### 3. 智能保存或更新
- `saveOrUpdateMpUserLastLoginIdentity` 方法支持智能判断是创建还是更新

### 4. 枚举自动转换
- 身份类型和登录状态自动转换为中文描述
- 使用 MapStruct 的 `@Named` 注解实现自定义转换逻辑

### 5. 完整的参数校验
- 使用 `@NotNull`、`@Valid` 等注解进行参数校验
- 详细的错误提示信息

### 6. 标准的 Swagger 文档
- 完整的接口文档注解
- 详细的参数说明和示例

## 接口列表

### 基础 CRUD 接口
1. `POST /publicbiz/app/employer/mp-user-last-login-identity/create` - 创建记录
2. `PUT /publicbiz/app/employer/mp-user-last-login-identity/update` - 更新记录
3. `DELETE /publicbiz/app/employer/mp-user-last-login-identity/delete` - 删除记录
4. `GET /publicbiz/app/employer/mp-user-last-login-identity/get` - 根据ID查询

### 业务查询接口
5. `GET /publicbiz/app/employer/mp-user-last-login-identity/get-by-user-id-and-openid` - 根据用户ID和openid查询
6. `GET /publicbiz/app/employer/mp-user-last-login-identity/get-by-openid` - 根据openid查询
7. `GET /publicbiz/app/employer/mp-user-last-login-identity/get-by-user-id` - 根据用户ID查询

### 智能操作接口
8. `POST /publicbiz/app/employer/mp-user-last-login-identity/save-or-update` - 保存或更新记录

## 使用示例

### 创建记录
```json
{
  "userId": 1024,
  "openid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "identityType": "EMPLOYER",
  "identityName": "张先生",
  "lastLoginTime": "2024-01-01T12:00:00",
  "loginStatus": 1
}
```

### 查询记录
```http
GET /publicbiz/app/employer/mp-user-last-login-identity/get-by-openid?openid=oH_Tu5EtrrF6n7u8v9w0x1y2z3
```

## 注意事项

1. 所有文件都遵循项目的命名规范和代码风格
2. 使用了项目标准的注解和依赖
3. 错误处理使用统一的 `ServiceExceptionUtil`
4. 对象转换使用 MapStruct 框架
5. 接口返回统一的 `CommonResult<T>` 格式
6. 所有接口都有完整的 Swagger 文档注解

## 后续工作

1. 根据实际业务需求调整字段和接口
2. 添加单元测试用例
3. 完善业务逻辑和异常处理
4. 根据前端需求调整接口参数和返回值 