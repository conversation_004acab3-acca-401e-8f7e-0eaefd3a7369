<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.question.QuestionMapper">

    <!-- 查询考题统计数据 -->
    <select id="selectStatistics" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO">
        SELECT id, type
        FROM publicbiz_question
        WHERE deleted = 0
        <if test="biz != null and biz != ''">
            AND biz = #{biz}
        </if>
        <if test="level1Name != null and level1Name != ''">
            AND level1_name = #{level1Name}
        </if>
        <if test="level2Name != null and level2Name != ''">
            AND level2_name = #{level2Name}
        </if>
        <if test="level3Name != null and level3Name != ''">
            AND level3_name = #{level3Name}
        </if>
    </select>

    <!-- 批量插入考题 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO publicbiz_question (
            tenant_id, level1_name, level1_code, level2_name, level2_code, level3_name, level3_code,
            cert_name, cert_code, title, type, answer, biz, biz_name, difficulty, score, time_limit,
            explanation, keywords, status, creator, creator_name, create_time, update_time, deleted
        ) VALUES
        <foreach collection="questions" item="item" separator=",">
            (
                #{item.tenantId}, #{item.level1Name}, #{item.level1Code}, #{item.level2Name}, #{item.level2Code},
                #{item.level3Name}, #{item.level3Code}, #{item.certName}, #{item.certCode}, #{item.title},
                #{item.type}, #{item.answer}, #{item.biz}, #{item.bizName}, #{item.difficulty}, #{item.score},
                #{item.timeLimit}, #{item.explanation}, #{item.keywords}, #{item.status}, #{item.creator},
                #{item.creatorName}, #{item.createTime}, #{item.updateTime}, #{item.deleted}
            )
        </foreach>
    </insert>

</mapper>
