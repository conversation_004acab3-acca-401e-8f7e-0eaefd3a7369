package cn.bztmaster.cnt.module.talentpool.service.talent;

import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserPageReqDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserRespDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserUpdateReqDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserTagDetailRespDTO;
import cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentCertificateRespDTO;
import java.util.List;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;

/**
 * 人才用户 Service 接口
 *
 * 提供人才库用户的核心业务操作。
 *
 * <AUTHOR>
 */
public interface TalentUserService {

    /**
     * 分页查询人才库用户列表
     * 
     * @param reqDTO 分页请求参数
     * @return 分页结果
     */
    PageResult<TalentUserRespDTO> getTalentUserPage(TalentUserPageReqDTO reqDTO);

    /**
     * 根据ID获取人才详细信息
     * 
     * @param id 用户ID
     * @return 详细信息
     */
    TalentUserRespDTO getTalentUser(Long id);

    /**
     * 根据OneID获取人才详细信息
     * 
     * @param oneid OneID
     * @return 详细信息
     */
    TalentUserRespDTO getTalentUserByOneid(String oneid);

    /**
     * 根据OneID获取人才详细标签信息
     * 
     * @param oneid OneID
     * @return 详细标签信息列表
     */
    List<TalentUserTagDetailRespDTO> getTalentUserTagsByOneid(String oneid);

    /**
     * 编辑人才基本信息
     * 
     * @param reqDTO 编辑请求参数
     */
    void updateTalentUser(TalentUserUpdateReqDTO reqDTO);

    /**
     * 停用人才，将状态设为已禁用
     * 
     * @param id 用户ID
     */
    void disableTalentUser(Long id);

    /**
     * 合并人才，严格按照多级识别规则
     * 
     * @param id 待合并用户ID
     */
    void mergeTalentUser(Long id);

    /**
     * 新增人才档案，主表+子表批量插入
     * 
     * @param reqDTO 新增请求参数
     * @return 新增主表ID
     */
    Long createTalentUser(cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserCreateReqDTO reqDTO);

    /**
     * 获取人才画像详情
     * 
     * @param id 用户ID
     * @return 画像详情
     */
    cn.bztmaster.cnt.module.talentpool.api.talent.dto.TalentUserDetailRespDTO getTalentUserDetail(Long id);

    /**
     * 变更人才状态（启用/停用/禁用等）
     */
    void changeTalentUserStatus(Long id, String status);

    /**
     * 根据OneID获取人才资质证书信息
     * 
     * @param oneid OneID
     * @return 资质证书信息
     */
    TalentCertificateRespDTO getTalentCertificatesByOneid(String oneid);
}