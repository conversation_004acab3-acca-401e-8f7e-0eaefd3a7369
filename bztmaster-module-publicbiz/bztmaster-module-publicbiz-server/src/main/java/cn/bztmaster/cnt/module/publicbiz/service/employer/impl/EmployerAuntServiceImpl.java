package cn.bztmaster.cnt.module.publicbiz.service.employer.impl;

import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerQualificationDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.AuntReviewDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerQualificationMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.AuntReviewMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerAuntService;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.TalentUserMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.TalentCertificateMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.TalentUserTagMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.TalentUserDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.TalentCertificateDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.TalentUserTagDetailDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 雇主端阿姨 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class EmployerAuntServiceImpl implements EmployerAuntService {

    @Resource
    private PractitionerMapper practitionerMapper;

    @Resource
    private AgencyMapper agencyMapper;

    @Resource
    private PractitionerQualificationMapper practitionerQualificationMapper;

    @Resource
    private AuntReviewMapper auntReviewMapper;

    @Resource
    private TalentUserMapper talentUserMapper;

    @Resource
    private TalentCertificateMapper talentCertificateMapper;

    @Resource
    private TalentUserTagMapper talentUserTagMapper;

    @Resource
    private DomesticOrderMapper domesticOrderMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public AuntHomeListRespVO getHomeAuntList(Integer limit) {
        log.info("获取首页阿姨列表，limit: {}", limit);

        // 1. 参数验证和默认值设置
        if (limit == null || limit <= 0) {
            limit = 8;
        }
        if (limit > 8) {
            limit = 8;
        }

        // 2. 查询符合条件的阿姨列表（按评级降序排序）
        List<PractitionerDO> practitionerList = practitionerMapper.selectHomeAuntList(limit);

        // 3. 获取机构信息
        List<Long> agencyIds = practitionerList.stream()
                .filter(p -> p.getAgencyId() != null)
                .map(PractitionerDO::getAgencyId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, String> agencyShortNameMap;
        if (!agencyIds.isEmpty()) {
            List<AgencyDO> agencyList = agencyMapper.selectBatchIds(agencyIds);
            agencyShortNameMap = agencyList.stream()
                    .collect(Collectors.toMap(
                            AgencyDO::getId,
                            agency -> agency.getAgencyShortName() != null ? agency.getAgencyShortName()
                                    : agency.getAgencyName()));
        } else {
            agencyShortNameMap = new java.util.HashMap<>();
        }

        // 4. 构建阿姨信息列表
        List<AuntHomeListRespVO.AuntInfo> auntInfoList = practitionerList.stream()
                .map(practitioner -> buildAuntInfo(practitioner, agencyShortNameMap))
                .collect(Collectors.toList());

        // 5. 分配金牌阿姨和金牌月嫂
        AuntHomeListRespVO result = new AuntHomeListRespVO();

        int halfSize = auntInfoList.size() / 2;
        result.setGoldAunts(auntInfoList.subList(0, halfSize));
        result.setMaternityAunts(auntInfoList.subList(halfSize, auntInfoList.size()));

        log.info("首页阿姨列表获取成功，金牌阿姨: {}个，金牌月嫂: {}个",
                result.getGoldAunts().size(), result.getMaternityAunts().size());

        return result;
    }

    @Override
    public AuntDetailRespVO getAuntDetail(String auntId) {
        log.info("获取阿姨详情，auntId: {}", auntId);

        // 1. 参数验证
        if (auntId == null || auntId.trim().isEmpty()) {
            log.error("阿姨ID不能为空");
            return null;
        }

        // 2. 将String类型的auntId转换为Long类型的主键ID
        Long practitionerId;
        try {
            practitionerId = Long.valueOf(auntId);
        } catch (NumberFormatException e) {
            log.error("阿姨ID格式错误，auntId: {}", auntId);
            return null;
        }

        // 3. 根据主键ID查询阿姨基本信息
        PractitionerDO practitioner = practitionerMapper.selectById(practitionerId);
        if (practitioner == null) {
            log.error("阿姨信息不存在，auntId: {}", auntId);
            return null;
        }

        // 4. 构建阿姨详情信息
        AuntDetailRespVO detailVO = new AuntDetailRespVO();
        detailVO.setId(practitioner.getAuntOneid());
        detailVO.setName(practitioner.getName());
        detailVO.setAvatar(practitioner.getAvatar());
        detailVO.setRating(practitioner.getRating());

        // 5. 设置实名认证状态（身份证号不为空则代表已实名）
        detailVO.setIsVerified(StringUtils.hasText(practitioner.getIdCard()));

        // 6. 设置平台自营签约状态（agency_id为空则代表平台自营签约）
        detailVO.setIsPlatformContract(practitioner.getAgencyId() == null);

        // 7. 获取服务标签
        detailVO.setServiceTags(getServiceTags(practitioner.getAuntOneid()));

        // 8. 获取资质证书
        detailVO.setCertificates(getCertificates(practitioner.getAuntOneid()));

        // 9. 构建统计数据
        detailVO.setStats(buildStats(practitioner));

        // 10. 构建个人简介
        detailVO.setProfile(buildProfile(practitioner));

        log.info("阿姨详情获取成功，auntId: {}", auntId);
        return detailVO;
    }

    @Override
    public AuntReviewListRespVO getAuntReviews(String auntId, Integer page, Integer size, Integer rating) {
        log.info("获取阿姨评价列表，auntId: {}, page: {}, size: {}, rating: {}", auntId, page, size, rating);

        // 1. 参数验证和默认值设置
        if (page == null || page <= 0) {
            page = 1;
        }
        if (size == null || size <= 0) {
            size = 10;
        }

        // 2. 根据主键ID获取阿姨信息，获取oneID
        String auntOneId = getAuntOneId(auntId);
        if (auntOneId == null) {
            log.error("阿姨信息不存在，auntId: {}", auntId);
            return null;
        }

        // 3. 构建查询条件
        LambdaQueryWrapperX<AuntReviewDO> queryWrapper = new LambdaQueryWrapperX<AuntReviewDO>()
                .eq(AuntReviewDO::getAuntId, auntId)
                .eq(AuntReviewDO::getStatus, 1)
                .eq(AuntReviewDO::getDeleted, false)
                .orderByDesc(AuntReviewDO::getCreateTime);

        // 4. 评分筛选
        if (rating != null && rating >= 1 && rating <= 5) {
            queryWrapper.eq(AuntReviewDO::getRating, rating);
        }

        // 5. 分页查询
        List<AuntReviewDO> reviewList = auntReviewMapper.selectList(queryWrapper);
        Long total = auntReviewMapper.selectCount(queryWrapper);

        // 手动分页
        int startIndex = (page - 1) * size;
        int endIndex = Math.min(startIndex + size, reviewList.size());
        List<AuntReviewDO> pageList = reviewList.subList(startIndex, endIndex);

        // 6. 构建响应数据
        AuntReviewListRespVO respVO = new AuntReviewListRespVO();
        respVO.setTotal(total);
        respVO.setPages(total / size + (total % size > 0 ? 1 : 0));
        respVO.setCurrent((long) page);
        respVO.setSize((long) size);

        // 7. 转换评价记录
        List<AuntReviewListRespVO.ReviewRecord> records = pageList.stream()
                .map(this::convertToReviewRecord)
                .collect(Collectors.toList());
        respVO.setRecords(records);

        log.info("阿姨评价列表获取成功，总数: {}", total);
        return respVO;
    }

    @Override
    public AuntCertificateRespVO getAuntCertificates(String auntId) {
        log.info("获取阿姨资质证书，auntId: {}", auntId);

        // 1. 根据主键ID获取阿姨信息，获取oneID
        String auntOneId = getAuntOneId(auntId);
        if (auntOneId == null) {
            log.error("阿姨信息不存在，auntId: {}", auntId);
            return null;
        }

        // 2. 根据oneID查询人才用户信息，获取userId
        TalentUserDO talentUser = talentUserMapper.selectByOneid(auntOneId);
        if (talentUser == null) {
            log.error("人才库中未找到对应的用户信息，oneID: {}", auntOneId);
            return null;
        }
        log.info("根据oneID: {} 查询到用户，userId: {}", auntOneId, talentUser.getUserId());

        // 3. 根据userId查询已审核通过的资质证书
        List<TalentCertificateDO> certificates = talentCertificateMapper
                .selectVerifiedListByUserId(talentUser.getUserId());
        log.info("根据userId: {} 查询到资质证书数量: {}", talentUser.getUserId(), certificates.size());

        // 4. 构建响应数据
        AuntCertificateRespVO respVO = new AuntCertificateRespVO();
        List<AuntCertificateRespVO.Certificate> certificateList = certificates.stream()
                .map(this::convertToCertificateFromTalent)
                .collect(Collectors.toList());
        respVO.setCertificates(certificateList);

        log.info("阿姨资质证书获取成功，证书数量: {}", certificateList.size());
        return respVO;
    }

    /**
     * 构建阿姨信息
     */
    private AuntHomeListRespVO.AuntInfo buildAuntInfo(PractitionerDO practitioner,
            Map<Long, String> agencyShortNameMap) {
        AuntHomeListRespVO.AuntInfo auntInfo = new AuntHomeListRespVO.AuntInfo();
        auntInfo.setId(practitioner.getId());
        auntInfo.setName(practitioner.getName());
        auntInfo.setAvatar(practitioner.getAvatar());
        auntInfo.setServiceType(practitioner.getServiceType());
        auntInfo.setRating(practitioner.getRating());
        auntInfo.setExperienceYears(practitioner.getExperienceYears());

        // 设置机构名称（优先使用简称，如果没有则使用全称）
        if (practitioner.getAgencyId() != null) {
            String agencyName = agencyShortNameMap.get(practitioner.getAgencyId());
            auntInfo.setAgencyName(agencyName != null ? agencyName : practitioner.getAgencyName());
        } else {
            auntInfo.setAgencyName(practitioner.getAgencyName());
        }

        return auntInfo;
    }

    /**
     * 获取资质证书
     */
    private List<AuntDetailRespVO.Certificate> getCertificates(String auntOneId) {
        try {
            // 1. 查询阿姨资质证书
            List<PractitionerQualificationDO> qualifications = practitionerQualificationMapper.selectList(
                    new LambdaQueryWrapperX<PractitionerQualificationDO>()
                            .eq(PractitionerQualificationDO::getPractitionerOneId, auntOneId)
                            .eq(PractitionerQualificationDO::getStatus, 1)
                            .eq(PractitionerQualificationDO::getDeleted, false)
                            .orderByAsc(PractitionerQualificationDO::getSortOrder));

            // 2. 转换为证书信息
            return qualifications.stream()
                    .map(qualification -> {
                        AuntDetailRespVO.Certificate certificate = new AuntDetailRespVO.Certificate();
                        certificate.setName(qualification.getFileName());
                        certificate.setNumber(""); // 证书编号需要从其他地方获取
                        certificate.setValidUntil(""); // 有效期需要从其他地方获取
                        return certificate;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取资质证书失败，auntOneId: {}", auntOneId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建统计数据
     */
    private AuntDetailRespVO.Stats buildStats(PractitionerDO practitioner) {
        AuntDetailRespVO.Stats stats = new AuntDetailRespVO.Stats();
        stats.setFamilies(practitioner.getTotalOrders()); // 服务家庭数
        stats.setYears(practitioner.getExperienceYears()); // 从业年限
        stats.setResponseTime(3); // 响应时间，默认3小时
        stats.setRepurchaseRate(calculateRepurchaseRate(practitioner.getAuntOneid())); // 计算复购率
        return stats;
    }

    /**
     * 构建个人简介
     */
    private AuntDetailRespVO.Profile buildProfile(PractitionerDO practitioner) {
        AuntDetailRespVO.Profile profile = new AuntDetailRespVO.Profile();
        profile.setIntroduction(practitioner.getProfile()); // 个人介绍，暂时为空
        profile.setHometown(practitioner.getHometown()); // 籍贯
        profile.setAge(practitioner.getAge()); // 年龄
        profile.setExperience(practitioner.getExperienceYears()); // 从业经验
        return profile;
    }

    /**
     * 转换为评价记录
     */
    private AuntReviewListRespVO.ReviewRecord convertToReviewRecord(AuntReviewDO review) {
        AuntReviewListRespVO.ReviewRecord record = new AuntReviewListRespVO.ReviewRecord();
        record.setId(review.getId());
        record.setReviewerName(review.getReviewerName());
        record.setReviewerAvatar(review.getReviewerAvatar());
        record.setRating(review.getRating());

        // 安全处理创建时间，避免空指针异常
        if (review.getCreateTime() != null) {
            record.setDate(review.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else {
            record.setDate("");
        }

        record.setTags(parseReviewTags(review.getReviewTags()));
        record.setContent(review.getReviewContent());
        record.setImages(parseReviewImages(review.getReviewImages()));
        record.setIsAnonymous(review.getIsAnonymous());
        record.setLikeCount(review.getLikeCount());
        return record;
    }

    /**
     * 转换为证书信息
     */
    private AuntCertificateRespVO.Certificate convertToCertificate(PractitionerQualificationDO qualification) {
        AuntCertificateRespVO.Certificate certificate = new AuntCertificateRespVO.Certificate();
        certificate.setId(qualification.getId());
        certificate.setFileType(qualification.getFileType());
        certificate.setFileName(qualification.getFileName());
        certificate.setFileUrl(qualification.getFileUrl());
        certificate.setCertNumber(""); // 证书编号需要从其他地方获取
        certificate.setValidUntil(""); // 有效期需要从其他地方获取
        certificate.setSortOrder(qualification.getSortOrder());
        certificate.setStatus(qualification.getStatus());
        return certificate;
    }

    /**
     * 将TalentCertificateDO转换为AuntCertificateRespVO.Certificate
     */
    private AuntCertificateRespVO.Certificate convertToCertificateFromTalent(
            TalentCertificateDO talentCertificate) {
        AuntCertificateRespVO.Certificate certificate = new AuntCertificateRespVO.Certificate();
        certificate.setId(talentCertificate.getCertificateId());
        certificate.setFileType("certificate"); // 默认文件类型
        certificate.setFileName(talentCertificate.getName());
        certificate.setFileUrl(talentCertificate.getCertificateImageUrl());
        certificate.setCertNumber(talentCertificate.getCertificateNo());

        // 格式化有效期
        if (talentCertificate.getExpiryDate() != null) {
            certificate.setValidUntil(talentCertificate.getExpiryDate().toString());
        } else {
            certificate.setValidUntil("");
        }

        // 设置排序和状态
        certificate.setSortOrder(1); // 默认排序
        certificate.setStatus("VERIFIED".equals(talentCertificate.getStatus()) ? 1 : 0); // 根据状态设置
        return certificate;
    }

    /**
     * 解析评价标签
     */
    private List<String> parseReviewTags(String reviewTags) {
        try {
            if (StringUtils.hasText(reviewTags)) {
                return objectMapper.readValue(reviewTags, new TypeReference<List<String>>() {
                });
            }
        } catch (Exception e) {
            log.error("解析评价标签失败", e);
        }
        return new ArrayList<>();
    }

    /**
     * 解析评价图片
     */
    private List<String> parseReviewImages(String reviewImages) {
        try {
            if (StringUtils.hasText(reviewImages)) {
                return objectMapper.readValue(reviewImages, new TypeReference<List<String>>() {
                });
            }
        } catch (Exception e) {
            log.error("解析评价图片失败", e);
        }
        return new ArrayList<>();
    }

    /**
     * 根据主键ID获取阿姨的oneID
     */
    private String getAuntOneId(String auntId) {
        try {
            // 1. 参数验证
            if (auntId == null || auntId.trim().isEmpty()) {
                log.error("阿姨ID不能为空");
                return null;
            }

            // 2. 将String类型的auntId转换为Long类型的主键ID
            Long practitionerId = Long.valueOf(auntId);

            // 3. 根据主键ID查询阿姨基本信息
            PractitionerDO practitioner = practitionerMapper.selectById(practitionerId);
            if (practitioner == null) {
                log.error("阿姨信息不存在，auntId: {}", auntId);
                return null;
            }

            return practitioner.getAuntOneid();
        } catch (NumberFormatException e) {
            log.error("阿姨ID格式错误，auntId: {}", auntId);
            return null;
        } catch (Exception e) {
            log.error("获取阿姨oneID失败，auntId: {}", auntId, e);
            return null;
        }
    }

    /**
     * 获取服务标签
     * 根据阿姨的oneID关联人才库表talent_user，再根据talent_user表的user_id关联talent_user_tag表的user_id
     * 再用talent_user_tag表的tag_id字段关联talent_tag_library的tag_id取标签名称
     */
    private List<AuntDetailRespVO.ServiceTag> getServiceTags(String auntOneId) {
        try {
            if (auntOneId == null || auntOneId.trim().isEmpty()) {
                log.warn("阿姨oneID为空，无法获取服务标签");
                return new ArrayList<>();
            }

            // 使用本地数据库查询获取用户标签详情
            List<TalentUserTagDetailDO> tagDetails = talentUserTagMapper.selectTagDetailsByOneid(auntOneId);

            if (tagDetails == null || tagDetails.isEmpty()) {
                log.info("用户没有标签，oneID: {}", auntOneId);
                return new ArrayList<>();
            }

            // 构建ServiceTag对象列表
            return tagDetails.stream()
                    .map(tagDetail -> {
                        AuntDetailRespVO.ServiceTag serviceTag = new AuntDetailRespVO.ServiceTag();
                        serviceTag.setTagId(tagDetail.getTagId());
                        serviceTag.setTagName(tagDetail.getTagName());
                        serviceTag.setTagType(tagDetail.getTypeName()); // 使用标签类型名称作为标签类型
                        return serviceTag;
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取服务标签失败，auntOneId: {}", auntOneId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 计算阿姨的复购率
     * 复购率 = (有复购行为的雇主数量 / 总雇主数量) × 100%
     * 
     * @param auntOneId 阿姨的OneID
     * @return 复购率百分比
     */
    private Integer calculateRepurchaseRate(String auntOneId) {
        try {
            if (auntOneId == null || auntOneId.trim().isEmpty()) {
                log.warn("阿姨OneID为空，无法计算复购率");
                return 0;
            }

            // 1. 查询阿姨的所有历史订单
            List<DomesticOrderDO> orders = domesticOrderMapper.selectByPractitionerOneid(auntOneId);

            if (orders == null || orders.isEmpty()) {
                log.info("阿姨没有历史订单，auntOneId: {}", auntOneId);
                return 0;
            }

            // 2. 按雇主分组统计订单数量
            Map<String, Long> employerOrderCount = orders.stream()
                    .filter(order -> order.getCustomerOneid() != null && !order.getCustomerOneid().trim().isEmpty())
                    .collect(Collectors.groupingBy(
                            DomesticOrderDO::getCustomerOneid,
                            Collectors.counting()));

            if (employerOrderCount.isEmpty()) {
                log.info("阿姨没有有效的雇主订单，auntOneId: {}", auntOneId);
                return 0;
            }

            // 3. 计算复购率
            long totalEmployers = employerOrderCount.size();
            long repurchaseEmployers = employerOrderCount.values().stream()
                    .filter(count -> count > 1)
                    .count();

            if (totalEmployers == 0) {
                return 0;
            }

            int repurchaseRate = (int) Math.round((double) repurchaseEmployers / totalEmployers * 100);

            log.info("阿姨复购率计算完成，auntOneId: {}, 总雇主数: {}, 复购雇主数: {}, 复购率: {}%",
                    auntOneId, totalEmployers, repurchaseEmployers, repurchaseRate);

            return repurchaseRate;

        } catch (Exception e) {
            log.error("计算复购率失败，auntOneId: {}", auntOneId, e);
            return 0;
        }
    }
}
