package cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 轮播图状态更新请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 轮播图状态更新请求")
@Data
public class CarouselStatusUpdateReqVO {

    @Schema(description = "轮播图ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "轮播图ID不能为空")
    private Long id;

    @Schema(description = "状态：1-启用，0-禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;
} 