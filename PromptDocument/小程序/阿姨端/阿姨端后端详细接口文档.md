
## 获得阿姨工作台信息

**接口地址**:`/publicbiz/aunt/workbench/info`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


暂无


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultAuntWorkbenchInfoRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||AuntWorkbenchInfoRespVO|AuntWorkbenchInfoRespVO|
|&emsp;&emsp;auntInfo||AuntInfo|AuntInfo|
|&emsp;&emsp;&emsp;&emsp;oneId|阿姨OneID|string||
|&emsp;&emsp;&emsp;&emsp;name|阿姨姓名|string||
|&emsp;&emsp;&emsp;&emsp;phone|手机号|string||
|&emsp;&emsp;&emsp;&emsp;avatar|头像URL|string||
|&emsp;&emsp;&emsp;&emsp;serviceType|服务类型|string||
|&emsp;&emsp;&emsp;&emsp;experienceYears|从业年限|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;rating|评级|number||
|&emsp;&emsp;&emsp;&emsp;currentStatus|当前状态|string||
|&emsp;&emsp;&emsp;&emsp;agencyName|所属机构|string||
|&emsp;&emsp;kpiData||KPIData|KPIData|
|&emsp;&emsp;&emsp;&emsp;todaySchedule|今日排班数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;monthlyIncome|本月预估收入|number||
|&emsp;&emsp;&emsp;&emsp;monthlyServiceHours|本月服务时长(小时)|integer(int32)||
|&emsp;&emsp;orderStats||OrderStats|OrderStats|
|&emsp;&emsp;&emsp;&emsp;pending|待确认订单数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;inProgress|进行中订单数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;completed|已完成订单数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;cancelled|已取消订单数|integer(int32)||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"auntInfo": {
			"oneId": "550e8400-e29b-41d4-a716-446655440000",
			"name": "李阿姨",
			"phone": "13800138000",
			"avatar": "https://example.com/avatar.jpg",
			"serviceType": "保洁",
			"experienceYears": 5,
			"rating": 4.8,
			"currentStatus": "空闲中",
			"agencyName": "传能家政"
		},
		"kpiData": {
			"todaySchedule": 3,
			"monthlyIncome": 8500,
			"monthlyServiceHours": 88
		},
		"orderStats": {
			"pending": 1,
			"inProgress": 2,
			"completed": 28,
			"cancelled": 2
		}
	},
	"msg": ""
}
```


## 检查用户是否注册了家政人员信息

**接口地址**:`/publicbiz/aunt/user-switch/check-registration`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "openId": "o1234567890abcdef",
  "mobile": "13800138000"
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|auntUserSwitchReqVO|用户 APP - 阿姨用户切换请求 VO|body|true|AuntUserSwitchReqVO|AuntUserSwitchReqVO|
|&emsp;&emsp;openId|微信用户openId||false|string||
|&emsp;&emsp;mobile|手机号||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultAuntUserSwitchRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||AuntUserSwitchRespVO|AuntUserSwitchRespVO|
|&emsp;&emsp;isRegistered|是否已注册家政人员|boolean||
|&emsp;&emsp;auntOneId|阿姨OneID|string||
|&emsp;&emsp;name|阿姨姓名|string||
|&emsp;&emsp;phone|手机号|string||
|&emsp;&emsp;serviceType|主要服务类型|string||
|&emsp;&emsp;experienceYears|从业年限|integer(int32)||
|&emsp;&emsp;platformStatus|平台状态|string||
|&emsp;&emsp;rating|评级|number||
|&emsp;&emsp;status|状态|string||
|&emsp;&emsp;currentStatus|当前状态|string||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"isRegistered": true,
		"auntOneId": "12345678-1234-1234-1234-123456789012",
		"name": "张阿姨",
		"phone": "13800138000",
		"serviceType": "月嫂",
		"experienceYears": 5,
		"platformStatus": "cooperating",
		"rating": 4.5,
		"status": "active",
		"currentStatus": "待岗"
	},
	"msg": ""
}
```

## 记录打卡


**接口地址**:`/publicbiz/aunt/punch/record`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "auntOneId": "550e8400-e29b-41d4-a716-446655440000",
  "scheduleId": "1",
  "punchType": 1,
  "punchLocation": "北京市朝阳区建国路88号",
  "punchLatitude": 39.908823,
  "punchLongitude": 116.39747,
  "photoCount": 2,
  "photoUrls": "https://example.com/photo1.jpg,https://example.com/photo2.jpg",
  "remark": "按时到达服务地点"
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|auntPunchRecordReqVO|用户 APP - 阿姨打卡记录 Request VO|body|true|AuntPunchRecordReqVO|AuntPunchRecordReqVO|
|&emsp;&emsp;auntOneId|阿姨OneID||true|string||
|&emsp;&emsp;scheduleId|排班ID||true|string||
|&emsp;&emsp;punchType|打卡类型||true|integer(int32)||
|&emsp;&emsp;punchLocation|打卡位置||true|string||
|&emsp;&emsp;punchLatitude|打卡纬度||true|number||
|&emsp;&emsp;punchLongitude|打卡经度||true|number||
|&emsp;&emsp;photoCount|照片数量||false|integer(int32)||
|&emsp;&emsp;photoUrls|照片URL列表||false|string||
|&emsp;&emsp;remark|打卡备注||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultBoolean|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||boolean||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```



## 获得打卡记录列表


**接口地址**:`/publicbiz/aunt/punch/record/list`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|scheduleId||query|false|string||
|startDate||query|false|string||
|endDate||query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultAuntPunchRecordRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||AuntPunchRecordRespVO|AuntPunchRecordRespVO|
|&emsp;&emsp;punchRecords|打卡记录列表|array|PunchRecord|
|&emsp;&emsp;&emsp;&emsp;id|记录ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;scheduleId|排班ID|string||
|&emsp;&emsp;&emsp;&emsp;auntOneId|阿姨OneID|string||
|&emsp;&emsp;&emsp;&emsp;auntName|阿姨姓名|string||
|&emsp;&emsp;&emsp;&emsp;punchType|打卡类型|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;punchTime|打卡时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;punchLocation|打卡位置|string||
|&emsp;&emsp;&emsp;&emsp;punchLatitude|打卡纬度|number||
|&emsp;&emsp;&emsp;&emsp;punchLongitude|打卡经度|number||
|&emsp;&emsp;&emsp;&emsp;photoCount|照片数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;photoUrls|照片URL列表|string||
|&emsp;&emsp;&emsp;&emsp;remark|打卡备注|string||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"punchRecords": [
			{
				"id": 1,
				"scheduleId": "1",
				"auntOneId": "550e8400-e29b-41d4-a716-446655440000",
				"auntName": "李阿姨",
				"punchType": 1,
				"punchTime": "",
				"punchLocation": "北京市朝阳区建国路88号",
				"punchLatitude": 39.908823,
				"punchLongitude": 116.39747,
				"photoCount": 2,
				"photoUrls": "https://example.com/photo1.jpg,https://example.com/photo2.jpg",
				"remark": "按时到达服务地点"
			}
		]
	},
	"msg": ""
}
```



## 确认订单


**接口地址**:`/publicbiz/aunt/order/confirm`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "orderId": 1
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|auntOrderConfirmReqVO|用户 APP - 阿姨订单确认 Request VO|body|true|AuntOrderConfirmReqVO|AuntOrderConfirmReqVO|
|&emsp;&emsp;orderId|订单ID||true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultBoolean|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||boolean||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": true,
	"msg": ""
}
```



## 获得阿姨订单列表


**接口地址**:`/publicbiz/aunt/order/list`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|auntOneId|阿姨OneID|query|true|string||
|pageNo|页码，从 1 开始|query|true|string||
|pageSize|每页条数，最大值为 100|query|true|string||
|orderStatus|订单状态|query|false|string||
|customerName|客户姓名|query|false|string||
|serviceAddress|服务地址|query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultPageResultAuntOrderListRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||PageResultAuntOrderListRespVO|PageResultAuntOrderListRespVO|
|&emsp;&emsp;list|数据|array|AuntOrderListRespVO|
|&emsp;&emsp;&emsp;&emsp;orderId|订单ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;orderNo|订单号|string||
|&emsp;&emsp;&emsp;&emsp;customerName|客户姓名|string||
|&emsp;&emsp;&emsp;&emsp;customerPhone|客户电话|string||
|&emsp;&emsp;&emsp;&emsp;serviceAddress|服务地址|string||
|&emsp;&emsp;&emsp;&emsp;servicePackageName|服务套餐名称|string||
|&emsp;&emsp;&emsp;&emsp;serviceStartDate|服务开始日期|string(date)||
|&emsp;&emsp;&emsp;&emsp;serviceEndDate|服务结束日期|string(date)||
|&emsp;&emsp;&emsp;&emsp;serviceDuration|服务时长|string||
|&emsp;&emsp;&emsp;&emsp;serviceAmount|服务金额|number||
|&emsp;&emsp;&emsp;&emsp;orderStatus|订单状态|string||
|&emsp;&emsp;&emsp;&emsp;createTime|创建时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;agencyName|所属机构|string||
|&emsp;&emsp;total|总量|integer(int64)||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"orderId": 1,
				"orderNo": "ORDER20241201001",
				"customerName": "张先生",
				"customerPhone": "13800138000",
				"serviceAddress": "北京市朝阳区建国路88号",
				"servicePackageName": "日常保洁套餐",
				"serviceStartDate": "2024-12-01",
				"serviceEndDate": "2024-12-31",
				"serviceDuration": "4小时",
				"serviceAmount": 200,
				"orderStatus": "pending",
				"createTime": "",
				"agencyName": "传能家政"
			}
		],
		"total": 0
	},
	"msg": ""
}
```


## 获得阿姨订单详情


**接口地址**:`/publicbiz/aunt/order/detail`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|orderId|订单ID|query|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultAuntOrderDetailRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||AuntOrderDetailRespVO|AuntOrderDetailRespVO|
|&emsp;&emsp;orderId|订单ID|integer(int64)||
|&emsp;&emsp;orderNo|订单号|string||
|&emsp;&emsp;orderStatus|订单状态|string||
|&emsp;&emsp;paymentStatus|支付状态|string||
|&emsp;&emsp;settlementStatus|结算状态|string||
|&emsp;&emsp;createTime|创建时间|string(date-time)||
|&emsp;&emsp;customerInfo||CustomerInfo|CustomerInfo|
|&emsp;&emsp;&emsp;&emsp;customerName|客户姓名|string||
|&emsp;&emsp;&emsp;&emsp;customerPhone|客户电话|string||
|&emsp;&emsp;&emsp;&emsp;serviceAddress|服务地址|string||
|&emsp;&emsp;&emsp;&emsp;customerRemark|客户备注|string||
|&emsp;&emsp;serviceInfo||ServiceInfo|ServiceInfo|
|&emsp;&emsp;&emsp;&emsp;servicePackageName|服务套餐名称|string||
|&emsp;&emsp;&emsp;&emsp;servicePackagePrice|套餐价格|number||
|&emsp;&emsp;&emsp;&emsp;serviceStartDate|服务开始日期|string(date)||
|&emsp;&emsp;&emsp;&emsp;serviceEndDate|服务结束日期|string(date)||
|&emsp;&emsp;&emsp;&emsp;serviceDuration|服务时长|string||
|&emsp;&emsp;&emsp;&emsp;serviceDescription|服务描述|string||
|&emsp;&emsp;&emsp;&emsp;serviceDetails|详细服务内容|string||
|&emsp;&emsp;&emsp;&emsp;serviceProcess|服务流程|string||
|&emsp;&emsp;totalAmount|总金额|number||
|&emsp;&emsp;practitionerIncome|阿姨收入|number||
|&emsp;&emsp;platformIncome|平台分成|number||
|&emsp;&emsp;agencyName|所属机构|string||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"orderId": 1,
		"orderNo": "ORDER20241201001",
		"orderStatus": "pending",
		"paymentStatus": "paid",
		"settlementStatus": "pending",
		"createTime": "",
		"customerInfo": {
			"customerName": "张先生",
			"customerPhone": "13800138000",
			"serviceAddress": "北京市朝阳区建国路88号",
			"customerRemark": "请准时到达"
		},
		"serviceInfo": {
			"servicePackageName": "日常保洁套餐",
			"servicePackagePrice": 200,
			"serviceStartDate": "2024-12-01",
			"serviceEndDate": "2024-12-31",
			"serviceDuration": "4小时",
			"serviceDescription": "日常保洁服务，包括房间清洁、物品整理等",
			"serviceDetails": "1. 房间清洁 2. 物品整理 3. 垃圾清理",
			"serviceProcess": "1. 到达现场 2. 开始服务 3. 完成服务 4. 客户确认"
		},
		"totalAmount": 200,
		"practitionerIncome": 160,
		"platformIncome": 40,
		"agencyName": "传能家政"
	},
	"msg": ""
}
```