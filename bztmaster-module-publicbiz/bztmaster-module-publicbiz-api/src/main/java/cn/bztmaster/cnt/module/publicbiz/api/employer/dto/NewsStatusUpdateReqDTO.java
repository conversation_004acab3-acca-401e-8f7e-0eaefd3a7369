package cn.bztmaster.cnt.module.publicbiz.api.employer.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 资讯状态更新请求 DTO
 *
 * <AUTHOR>
 */
@Data
public class NewsStatusUpdateReqDTO {

    /**
     * 资讯ID
     */
    @NotNull(message = "资讯ID不能为空")
    private Long id;

    /**
     * 目标状态：published-已发布/offline-已下架
     */
    @NotBlank(message = "状态不能为空")
    private String status;
} 