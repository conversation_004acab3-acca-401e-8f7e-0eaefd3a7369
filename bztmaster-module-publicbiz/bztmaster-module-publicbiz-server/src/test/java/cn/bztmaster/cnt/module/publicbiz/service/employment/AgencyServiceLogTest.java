package cn.bztmaster.cnt.module.publicbiz.service.employment;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationCreateVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 机构服务操作日志测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class AgencyServiceLogTest {

    @Resource
    private AgencyService agencyService;

    @Test
    public void testCreateAgencyLog() {
        // 测试新增机构时的操作日志记录
        AgencyCreateReqVO createReqVO = new AgencyCreateReqVO();
        createReqVO.setAgencyName("测试机构");
        createReqVO.setAgencyShortName("测试");
        createReqVO.setAgencyNo("AG001");
        createReqVO.setAgencyType("cooperation");
        createReqVO.setLegalRepresentative("李四");
        createReqVO.setUnifiedSocialCreditCode("91110000123456789X");
        createReqVO.setEstablishmentDate(LocalDate.of(2020, 1, 1));
        createReqVO.setRegisteredAddress("四川省成都市高新区");
        createReqVO.setOperatingAddress("四川省成都市高新区");
        createReqVO.setBusinessScope("家政服务");
        createReqVO.setApplicantName("张三");
        createReqVO.setApplicantPhone("***********");
        createReqVO.setApplicationTime(LocalDateTime.now());
        createReqVO.setContactPerson("张三");
        createReqVO.setContactPhone("***********");
        createReqVO.setContactEmail("<EMAIL>");
        createReqVO.setAgencyAddress("四川省成都市高新区");
        createReqVO.setProvince("四川省");
        createReqVO.setCity("成都市");
        createReqVO.setDistrict("高新区");
        createReqVO.setLongitude(new BigDecimal("104.065735"));
        createReqVO.setLatitude(new BigDecimal("30.659462"));
        createReqVO.setLocationAccuracy("high");
        createReqVO.setContractNo("HT001");
        createReqVO.setContractStartDate(LocalDate.of(2024, 1, 1));
        createReqVO.setContractEndDate(LocalDate.of(2024, 12, 31));
        createReqVO.setCommissionRate(new BigDecimal("5.00"));
        createReqVO.setRemark("测试机构");

        // 创建资质文件
        AgencyQualificationCreateVO qualification = new AgencyQualificationCreateVO();
        qualification.setFileName("营业执照.pdf");
        qualification.setFileType("business_license");
        qualification.setFileUrl("https://example.com/file.pdf");
        qualification.setFileSize(1024L);
        qualification.setRemark("营业执照");

        createReqVO.setQualifications(Arrays.asList(qualification));

        try {
            Long agencyId = agencyService.createAgency(createReqVO);
            System.out.println("机构创建成功，ID: " + agencyId);
        } catch (Exception e) {
            System.err.println("机构创建失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testUpdateAgencyLog() {
        // 测试更新机构时的操作日志记录
        AgencyUpdateReqVO updateReqVO = new AgencyUpdateReqVO();
        updateReqVO.setId(1L);
        updateReqVO.setReviewStatus("approved");
        updateReqVO.setReviewRemark("审核通过，资料齐全");

        try {
            agencyService.updateAgency(updateReqVO);
            System.out.println("机构更新成功");
        } catch (Exception e) {
            System.err.println("机构更新失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testDeleteAgencyLog() {
        // 测试删除机构时的操作日志记录
        try {
            agencyService.deleteAgency(1L);
            System.out.println("机构删除成功");
        } catch (Exception e) {
            System.err.println("机构删除失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 