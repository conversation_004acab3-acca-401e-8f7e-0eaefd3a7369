-- ========================================
-- 线索中心数据库表结构
-- 生成时间: 2024-06-19
-- 描述: 根据线索中心.html页面原型生成的MySQL建表SQL
-- ========================================

-- 1. 线索管理表
DROP TABLE IF EXISTS `publicbiz_lead_info`;
CREATE TABLE `publicbiz_lead_info` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
    `lead_id` VARCHAR(32) NOT NULL COMMENT '线索ID，系统生成的唯一标识',
    `customer_name` VARCHAR(100) NOT NULL COMMENT '客户姓名',
    `customer_phone` VARCHAR(20) NOT NULL COMMENT '联系电话，11位手机号',
    `lead_source` VARCHAR(50) NOT NULL DEFAULT '其他' COMMENT '线索来源：官网注册、市场活动、公众号文章、视频号、抖音、入驻商家、小红书、微博、知乎、百度推广、微信朋友圈、QQ群/微信群、线下展会、合作伙伴推荐、老客户推荐、电话营销、短信营销、邮件营销、其他',
    `business_module` VARCHAR(50) NOT NULL COMMENT '业务模块：高校业务、家政业务、培训业务、认证业务',
    `lead_status` VARCHAR(20) NOT NULL DEFAULT '未处理' COMMENT '线索状态：未处理、跟进中、已转化、无意向',
    `create_method` VARCHAR(20) NOT NULL DEFAULT '手动创建' COMMENT '创建方式：手动创建、系统导入、API接入',
    `current_owner` VARCHAR(64) COMMENT '当前跟进人，可为空表示未分配',
    `remark` TEXT COMMENT '备注信息，最大500字符',
    `tenant_id` BIGINT COMMENT '租户ID',
    `creator` VARCHAR(64) COMMENT '创建人',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` VARCHAR(64) COMMENT '更新人',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
    
    -- 索引
    UNIQUE KEY `uk_lead_id` (`lead_id`),
    KEY `idx_customer_phone` (`customer_phone`),
    KEY `idx_lead_source` (`lead_source`),
    KEY `idx_business_module` (`business_module`),
    KEY `idx_lead_status` (`lead_status`),
    KEY `idx_current_owner` (`current_owner`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索管理表';

-- 2. 线索跟进记录表
DROP TABLE IF EXISTS `publicbiz_lead_follow_up_log`;
CREATE TABLE `publicbiz_lead_follow_up_log` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
    `lead_id` VARCHAR(32) NOT NULL COMMENT '关联的线索ID',
    `follow_up_content` TEXT NOT NULL COMMENT '跟进内容详情',
    `tenant_id` BIGINT COMMENT '租户ID',
    `creator` VARCHAR(64) COMMENT '创建人',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` VARCHAR(64) COMMENT '更新人',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
    
    -- 索引
    KEY `idx_lead_id` (`lead_id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索跟进记录表';

-- 为线索管理表添加创建人姓名和当前跟进人姓名字段
ALTER TABLE publicbiz_lead_info
    ADD COLUMN creator_name VARCHAR(64) COMMENT '创建人姓名',
    ADD COLUMN current_owner_name VARCHAR(64) COMMENT '当前跟进人姓名';

-- 为线索跟进记录表添加创建人姓名字段
ALTER TABLE publicbiz_lead_follow_up_log
    ADD COLUMN creator_name VARCHAR(64) COMMENT '创建人姓名';