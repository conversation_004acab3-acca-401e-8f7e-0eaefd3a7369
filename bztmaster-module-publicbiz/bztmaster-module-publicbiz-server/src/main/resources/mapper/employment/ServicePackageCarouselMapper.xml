<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageCarouselMapper">

    <sql id="selectFields">
        id, package_id, image_url, sort_order, status, create_time, update_time, creator, updater, deleted, tenant_id
    </sql>

    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageCarouselDO">
        <result property="id" column="id" />
        <result property="packageId" column="package_id" />
        <result property="imageUrl" column="image_url" />
        <result property="sortOrder" column="sort_order" />
        <result property="status" column="status" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creator" column="creator" />
        <result property="updater" column="updater" />
        <result property="deleted" column="deleted" />
        <result property="tenantId" column="tenant_id" />
    </resultMap>

</mapper> 