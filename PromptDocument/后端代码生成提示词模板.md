# Java 后端接口代码生成提示词模板（超全面版，前后端强联动）

---

## 1. 前端页面分析与接口设计

**提示词：**
- 在生成接口前，**必须先分析对应的前端页面/组件/表单/表格/操作按钮/交互流程**，并与前端开发/UI设计师沟通，确保理解业务场景。
- 明确接口服务的前端页面路径、组件名、业务场景，并在接口注释中标注（如：`// 对应前端页面：src/views/user/UserList.vue`）。
- 记录前端页面涉及的所有字段、类型、校验规则、枚举、默认值、分页、筛选、排序、批量操作等。
- 对于多端（PC/移动/小程序）页面，接口需兼容多端需求。

---

## 2. 字段、类型、枚举、校验与前端组件映射

**提示词：**
- 接口入参、出参字段要与前端表单、表格、下拉、树形、级联、标签等组件**一一对应**，字段类型、枚举值、必填、默认值、校验规则等与前端保持同步。
- 字段命名风格、类型、嵌套结构要与前端数据结构完全一致，便于前端直接渲染和校验。
- 对于前端特殊组件（如图片、文件、富文本、树形、级联、标签等），接口返回结构要直接可用，减少前端二次处理。
- 枚举、状态、下拉选项等要与前端同步，建议统一维护在公共枚举类，并在接口文档中补充说明。

---

## 3. 接口文档与API平台同步

**提示词：**
- 所有接口必须补充**详细的Swagger/OpenAPI注解**，包括接口说明、参数、返回值、字段说明、示例、错误码、权限点等。
- 接口文档要**自动同步到前端API平台**（如Apifox、YApi、Swagger UI等），并在文档中补充前端页面示例、请求/响应示例、字段说明、错误码说明。
- 建议接口文档中增加"前端页面示例截图/交互流程图"，便于开发者理解业务场景。
- 接口文档要与前端API文档、接口定义保持同步，便于前端查阅和联调。

---

## 4. Mock数据与前后端联调

**提示词：**
- 在接口开发初期，**根据前端页面结构和交互流程，生成标准的Mock数据和接口返回示例**，便于前端开发和联调。
- Mock数据结构要与实际接口返回保持一致，字段、类型、嵌套结构、分页格式等完全对齐。
- 建议在接口文档中补充标准的请求/响应示例，便于前端直接复制使用。
- 联调阶段，及时根据前端反馈优化接口设计和数据结构，保证前后端高效协作。

---

## 5. 接口变更同步与多端适配

**提示词：**
- 接口字段、返回结构、错误码、枚举等如有变更，**必须第一时间同步前端**，并在接口文档中标注变更历史。
- 建议建立接口变更通知机制，自动推送到前端开发群或API平台。
- 对于多端（PC/移动/小程序）页面，接口参数、返回值、分页、筛选等要支持多端适配。
- 对于前端有特殊需求（如图片上传、富文本、文件下载等），接口要有专门的设计和注释。

---

## 6. 权限、安全与前端交互

**提示词：**
- 明确接口的权限点、登录态、Token校验、数据脱敏等安全要求，并在接口文档中标注，便于前端处理异常和权限提示。
- 对于敏感数据（如手机号、身份证、邮箱等），接口返回时要根据权限自动脱敏，前端展示时有清晰提示。
- 所有接口返回统一的`CommonResult<T>`结构，包含code、msg、data，便于前端统一处理。
- 业务异常用`ServiceExceptionUtil.exception`抛出，错误码集中在`ErrorCodeConstants`，并在文档中补充错误码说明。

---

## 7. 单元测试与前端联调脚本

**提示词：**
- 每个接口都要补充标准的单元测试用例，覆盖主要业务逻辑和异常分支。
- 建议补充前端联调脚本（如Postman、Apifox、Swagger UI示例），便于前后端自测和联调。
- 测试用例要模拟前端请求参数和返回值，确保接口联通。

---

## 8. 目录结构与分层规范（补充）

**提示词：**
- 生成的模块应包含`api`和`server`两个子模块，分别负责接口声明与实现。
- 典型包结构如下：
  ```
  cn.bztmaster.cnt.module.xxx
    ├── api
    │   └── vo（DTO/VO对象）
    └── server
        ├── controller
        ├── service
        ├── dal
        │   ├── mysql（Mapper接口）
        │   └── dataobject（DO实体）
        ├── convert（MapStruct对象转换）
        ├── enums（枚举类）
        ├── framework（通用扩展）
        └── ...（如util、job、mq等）
  ```
- 每个包下的类命名、注释、分层职责要清晰，禁止跨层直接调用。

---

## 9. 典型新模块生成流程（全流程）

**提示词：**
1. 与前端沟通，分析页面、表单、表格、交互流程，梳理所有字段、类型、校验、枚举、分页、筛选、批量操作等。
2. 在`api`模块下创建VO/DTO对象，定义接口参数和返回值，字段结构与前端表单、表格保持一致。
3. 在`server`模块下创建Controller、Service、Mapper、DO、Convert、Enum等类，按上述规范实现。
4. Controller只负责参数校验、权限控制、调用Service，Service负责业务逻辑，Mapper负责数据库操作，DO映射数据库表，Convert负责对象转换，Enum定义常量。
5. 所有接口返回`CommonResult<T>`，异常统一抛出。
6. 补充单元测试和前端联调脚本，确保主要逻辑和异常分支覆盖。
7. 注释、接口文档、包结构、命名全部符合规范。
8. 与前端联动，接口、数据结构、枚举、错误码等保持同步，接口文档同步到前端API平台。
9. 变更时及时同步前端，记录变更历史。

---

## 10. 最佳实践与建议

- 所有接口、数据结构、枚举、错误码等要与前端保持同步，便于前后端协作。
- 代码风格统一，采用驼峰命名法，包名全小写，类名大写开头。
- 依赖注入优先用`@Resource`。
- 业务分层清晰，禁止Controller直接操作Mapper。
- 重要操作加日志，使用`@Slf4j`。
- 代码中禁止魔法值，统一用常量或枚举。
- 代码中所有中文注释、字符串均为UTF-8编码。
- 生成代码时自动补全注释、Swagger注解、校验注解、分层结构。
- 生成的接口、VO、枚举、错误码等要与前端页面、API文档、数据结构严格对应。
- 如需自动化代码生成，可将本模板内容整理为代码生成器的输入提示词，结合模板引擎一键生成标准代码。
- 如需具体模块的代码生成示例、自动化脚本或有其它需求，随时告诉我！ 