# 传能家政小程序后端接口开发提示词

## 项目背景

基于传能家政小程序阿姨端工作台页面需求，需要开发对应的后端接口。前端页面显示阿姨工作台信息，包括阿姨问候、闲忙状态、排班数据等。

## 核心需求分析

### 1. 工作台模块核心需求
- **阿姨信息获取**: 根据当前登录用户获取阿姨基本信息，用于页面问候显示
- **闲忙状态判断**: 通过查询阿姨名下是否存在进行中的订单来确定当前状态
- **排班数据获取**: 获取阿姨的排班信息，包括今日排班数量、本月服务时长等
- **KPI指标计算**: 计算本月预估收入、服务时长等关键指标

### 2. 订单中心模块需求
- **订单列表查询**: 分页获取阿姨的订单列表，支持状态筛选
- **订单详情查看**: 获取订单的详细信息
- **订单状态管理**: 支持订单确认、状态更新等操作

## 数据库表结构分析

### 核心表关系
1. **阿姨信息表** (`publicbiz_practitioner`)
   - 主键: `id`
   - 唯一标识: `aunt_oneid` (OneID GUID)
   - 关键字段: `name`, `phone`, `current_status`, `rating`, `agency_id`

2. **订单主表** (`publicbiz_order`)
   - 主键: `id`
   - 订单号: `order_no`
   - 订单类型: `order_type` (domestic-家政服务)
   - 订单状态: `order_status`

3. **家政服务订单详情表** (`publicbiz_domestic_order`)
   - 关联订单: `order_id`
   - 阿姨信息: `practitioner_oneid`, `practitioner_name`
   - 服务信息: `service_package_name`, `service_start_date`, `service_end_date`

4. **家政服务任务表** (`publicbiz_domestic_task`)
   - 关联订单: `order_id`
   - 任务信息: `task_no`, `task_name`, `task_status`
   - 时间信息: `planned_start_time`, `actual_start_time`

## 具体接口设计

### 1. 获取阿姨工作台数据接口

**接口路径**: `/app-api/aunt/workbench/info`
**请求方式**: GET
**功能描述**: 获取阿姨工作台首页所需的所有数据

**实现逻辑**:
```java
@GetMapping("/aunt/workbench/info")
public CommonResult<WorkbenchInfoVO> getWorkbenchInfo() {
    // 1. 获取当前登录阿姨OneID
    String auntOneId = getCurrentAuntOneId();
    
    // 2. 查询阿姨基本信息
    PractitionerDO practitioner = practitionerService.getByOneId(auntOneId);
    
    // 3. 查询订单统计
    OrderStatsVO orderStats = orderService.getOrderStats(auntOneId);
    
    // 4. 查询KPI数据（今日排班、本月收入、本月服务时长）
    KPIDataVO kpiData = scheduleService.getKPIData(auntOneId);
    
    // 5. 组装返回数据
    WorkbenchInfoVO workbenchInfo = new WorkbenchInfoVO();
    workbenchInfo.setAuntInfo(convertToAuntInfoVO(practitioner));
    workbenchInfo.setKpiData(kpiData);
    workbenchInfo.setOrderStats(orderStats);
    
    return success(workbenchInfo);
}
```

**关键SQL查询**:
```sql
-- 获取阿姨基本信息
SELECT id, aunt_oneid, name, phone, avatar, service_type, 
       experience_years, rating, current_status, agency_name
FROM publicbiz_practitioner 
WHERE aunt_oneid = ? AND deleted = 0;

-- 获取订单统计
SELECT 
    SUM(CASE WHEN order_status = 'pending' THEN 1 ELSE 0 END) as pending,
    SUM(CASE WHEN order_status = 'in_progress' THEN 1 ELSE 0 END) as inProgress,
    SUM(CASE WHEN order_status = 'completed' THEN 1 ELSE 0 END) as completed,
    SUM(CASE WHEN order_status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
FROM publicbiz_domestic_order 
WHERE practitioner_oneid = ? AND deleted = 0;

-- 获取今日排班数
SELECT COUNT(*) as todaySchedule
FROM publicbiz_domestic_task 
WHERE practitioner_oneid = ? 
  AND DATE(planned_start_time) = CURDATE()
  AND deleted = 0;

-- 获取本月服务时长
SELECT COALESCE(SUM(TIMESTAMPDIFF(HOUR, actual_start_time, actual_end_time)), 0) as monthlyServiceHours
FROM publicbiz_domestic_task 
WHERE practitioner_oneid = ? 
  AND YEAR(actual_start_time) = YEAR(CURDATE())
  AND MONTH(actual_start_time) = MONTH(CURDATE())
  AND task_status = 'completed'
  AND deleted = 0;

-- 获取本月预估收入
SELECT COALESCE(SUM(total_amount), 0) as monthlyIncome
FROM publicbiz_domestic_order 
WHERE practitioner_oneid = ? 
  AND YEAR(create_time) = YEAR(CURDATE())
  AND MONTH(create_time) = MONTH(CURDATE())
  AND order_status IN ('in_progress', 'completed')
  AND deleted = 0;
```

**返回数据结构**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "auntInfo": {
      "oneId": "550e8400-e29b-41d4-a716-446655440000",
      "name": "李阿姨",
      "phone": "13800138000",
      "avatar": "https://example.com/avatar.jpg",
      "serviceType": "保洁",
      "experienceYears": 5,
      "rating": 4.8,
      "currentStatus": "空闲中", // 通过查询进行中订单判断
      "agencyName": "传能家政"
    },
    "kpiData": {
      "todaySchedule": 3, // 今日排班数
      "monthlyIncome": 8500.00, // 本月预估收入
      "monthlyServiceHours": 88 // 本月服务时长(小时)
    },
    "orderStats": {
      "pending": 1, // 待确认订单数
      "inProgress": 2, // 进行中订单数
      "completed": 28, // 已完成订单数
      "cancelled": 2 // 已取消订单数
    }
  }
}
```

### 2. 获取阿姨基本信息接口

**接口路径**: `/app-api/aunt/profile/info`
**请求方式**: GET
**功能描述**: 获取当前登录阿姨的详细信息，用于问候和状态显示

**实现逻辑**:
```java
@GetMapping("/aunt/profile/info")
public CommonResult<AuntProfileVO> getAuntProfile() {
    String auntOneId = getCurrentAuntOneId();
    
    // 查询阿姨详细信息
    PractitionerDO practitioner = practitionerService.getByOneId(auntOneId);
    
    // 检查当前是否有进行中的订单
    boolean hasActiveOrder = orderService.hasActiveOrder(auntOneId);
    
    // 更新当前状态
    String currentStatus = hasActiveOrder ? "服务中" : "空闲中";
    practitioner.setCurrentStatus(currentStatus);
    
    return success(convertToAuntProfileVO(practitioner));
}
```

**关键SQL查询**:
```sql
-- 检查是否有进行中的订单
SELECT COUNT(*) as active_order_count
FROM publicbiz_domestic_order 
WHERE practitioner_oneid = ? 
  AND order_status = 'in_progress' 
  AND deleted = 0;

-- 获取阿姨详细信息
SELECT id, aunt_oneid, name, phone, id_card, hometown, age, gender, 
       avatar, service_type, experience_years, platform_status, rating,
       agency_id, agency_name, status, current_status, current_order_id,
       total_orders, total_income, customer_satisfaction
FROM publicbiz_practitioner 
WHERE aunt_oneid = ? AND deleted = 0;
```

### 3. 获取订单列表接口

**接口路径**: `/app-api/aunt/order/list`
**请求方式**: GET
**功能描述**: 分页获取阿姨的订单列表，支持按状态筛选

**请求参数**:
- status: 订单状态（可选，pending/in_progress/completed/cancelled）
- pageNo: 页码（默认1）
- pageSize: 每页数量（默认10）

**实现逻辑**:
```java
@GetMapping("/aunt/order/list")
public CommonResult<PageResult<OrderListVO>> getOrderList(
        @RequestParam(required = false) String status,
        @RequestParam(defaultValue = "1") Integer pageNo,
        @RequestParam(defaultValue = "10") Integer pageSize) {
    
    String auntOneId = getCurrentAuntOneId();
    
    // 构建查询条件
    OrderQueryReq queryReq = new OrderQueryReq();
    queryReq.setPractitionerOneId(auntOneId);
    queryReq.setStatus(status);
    queryReq.setPageNo(pageNo);
    queryReq.setPageSize(pageSize);
    
    // 查询订单列表
    PageResult<OrderListVO> result = orderService.getOrderList(queryReq);
    
    return success(result);
}
```

**关键SQL查询**:
```sql
-- 订单列表查询
SELECT 
    o.id as orderId,
    o.order_no as orderNo,
    do.customer_name as customerName,
    do.customer_phone as customerPhone,
    do.service_address as serviceAddress,
    do.service_package_name as servicePackageName,
    do.service_start_date as serviceStartDate,
    do.service_end_date as serviceEndDate,
    do.service_duration as serviceDuration,
    do.total_amount as serviceAmount,
    o.order_status as orderStatus,
    o.create_time as createTime,
    do.agency_name as agencyName
FROM publicbiz_order o
LEFT JOIN publicbiz_domestic_order do ON o.id = do.order_id
WHERE do.practitioner_oneid = ?
  AND o.deleted = 0
  AND do.deleted = 0
  AND (? IS NULL OR o.order_status = ?)
ORDER BY o.create_time DESC
LIMIT ?, ?;

-- 查询总数
SELECT COUNT(*)
FROM publicbiz_order o
LEFT JOIN publicbiz_domestic_order do ON o.id = do.order_id
WHERE do.practitioner_oneid = ?
  AND o.deleted = 0
  AND do.deleted = 0
  AND (? IS NULL OR o.order_status = ?);
```

### 4. 获取订单详情接口

**接口路径**: `/app-api/aunt/order/detail`
**请求方式**: GET
**功能描述**: 获取指定订单的详细信息

**请求参数**:
- orderId: 订单ID（必填）

**实现逻辑**:
```java
@GetMapping("/aunt/order/detail")
public CommonResult<OrderDetailVO> getOrderDetail(@RequestParam String orderId) {
    String auntOneId = getCurrentAuntOneId();
    
    // 查询订单详情
    OrderDetailVO orderDetail = orderService.getOrderDetail(orderId, auntOneId);
    
    if (orderDetail == null) {
        return error(1004, "订单不存在");
    }
    
    return success(orderDetail);
}
```

**关键SQL查询**:
```sql
-- 获取订单详情
SELECT 
    o.id as orderId,
    o.order_no as orderNo,
    o.order_type as orderType,
    o.order_status as orderStatus,
    o.payment_status as paymentStatus,
    o.settlement_status as settlementStatus,
    o.create_time as createTime,
    do.customer_name as customerName,
    do.customer_phone as customerPhone,
    do.service_address as serviceAddress,
    do.customer_remark as customerRemark,
    do.service_package_name as servicePackageName,
    do.service_package_price as servicePackagePrice,
    do.service_start_date as serviceStartDate,
    do.service_end_date as serviceEndDate,
    do.service_duration as serviceDuration,
    do.service_description as serviceDescription,
    do.service_details as serviceDetails,
    do.service_process as serviceProcess,
    do.total_amount as totalAmount,
    do.practitioner_income as practitionerIncome,
    do.platform_income as platformIncome,
    do.agency_name as agencyName
FROM publicbiz_order o
LEFT JOIN publicbiz_domestic_order do ON o.id = do.order_id
WHERE o.id = ? 
  AND do.practitioner_oneid = ?
  AND o.deleted = 0
  AND do.deleted = 0;
```

### 5. 确认订单接口

**接口路径**: `/app-api/aunt/order/confirm`
**请求方式**: POST
**功能描述**: 阿姨确认接受订单

**请求参数**:
```json
{
  "orderId": "order_001"
}
```

**实现逻辑**:
```java
@PostMapping("/aunt/order/confirm")
public CommonResult<Void> confirmOrder(@RequestBody OrderConfirmReq req) {
    String auntOneId = getCurrentAuntOneId();
    
    // 验证订单状态
    OrderDO order = orderService.getById(req.getOrderId());
    if (order == null || !"pending".equals(order.getOrderStatus())) {
        return error(1005, "订单状态不正确");
    }
    
    // 验证订单是否属于当前阿姨
    DomesticOrderDO domesticOrder = domesticOrderService.getByOrderId(req.getOrderId());
    if (domesticOrder == null || !auntOneId.equals(domesticOrder.getPractitionerOneid())) {
        return error(1003, "无权操作此订单");
    }
    
    // 更新订单状态
    orderService.confirmOrder(req.getOrderId(), auntOneId);
    
    return success();
}
```

**关键SQL更新**:
```sql
-- 更新订单状态为已确认
UPDATE publicbiz_order 
SET order_status = 'confirmed', 
    update_time = NOW(), 
    updater = ?
WHERE id = ? AND deleted = 0;

-- 更新家政服务订单详情
UPDATE publicbiz_domestic_order 
SET update_time = NOW(), 
    updater = ?
WHERE order_id = ? AND deleted = 0;
```

## 数据模型设计

### VO类设计

```java
// 工作台信息VO
@Data
public class WorkbenchInfoVO {
    private AuntInfoVO auntInfo;
    private KPIDataVO kpiData;
    private OrderStatsVO orderStats;
}

// 阿姨信息VO
@Data
public class AuntInfoVO {
    private String oneId;
    private String name;
    private String phone;
    private String avatar;
    private String serviceType;
    private Integer experienceYears;
    private BigDecimal rating;
    private String currentStatus;
    private String agencyName;
}

// KPI数据VO
@Data
public class KPIDataVO {
    private Integer todaySchedule;
    private BigDecimal monthlyIncome;
    private Integer monthlyServiceHours;
}

// 订单统计VO
@Data
public class OrderStatsVO {
    private Integer pending;
    private Integer inProgress;
    private Integer completed;
    private Integer cancelled;
}

// 订单列表项VO
@Data
public class OrderListVO {
    private String orderId;
    private String orderNo;
    private String customerName;
    private String customerPhone;
    private String serviceAddress;
    private String servicePackageName;
    private String serviceStartDate;
    private String serviceEndDate;
    private String serviceDuration;
    private BigDecimal serviceAmount;
    private String orderStatus;
    private String createTime;
    private String agencyName;
}

// 订单详情VO
@Data
public class OrderDetailVO {
    private String orderId;
    private String orderNo;
    private String orderStatus;
    private String paymentStatus;
    private String settlementStatus;
    private String createTime;
    private CustomerInfoVO customerInfo;
    private ServiceInfoVO serviceInfo;
    private BigDecimal totalAmount;
    private BigDecimal practitionerIncome;
    private BigDecimal platformIncome;
    private String agencyName;
}
```

## 开发注意事项

### 1. 权限控制
- 所有接口都需要验证用户身份
- 确保阿姨只能访问自己的数据
- 使用JWT token进行身份验证

### 2. 数据安全
- 敏感信息（如手机号）需要脱敏处理
- 使用参数化查询防止SQL注入
- 对输入参数进行验证和过滤

### 3. 性能优化
- 合理使用数据库索引
- 避免N+1查询问题
- 对频繁查询的数据进行缓存

### 4. 错误处理
- 统一的异常处理机制
- 详细的错误日志记录
- 友好的错误信息返回

### 5. 业务逻辑
- 订单状态流转的严格控制
- 时间计算的准确性
- 数据一致性的保证

## 接口目录结构

建议按以下目录结构组织接口：

```
/app-api/
├── aunt/
│   ├── workbench/
│   │   └── info (GET) - 获取工作台数据
│   ├── profile/
│   │   └── info (GET) - 获取阿姨信息
│   ├── order/
│   │   ├── list (GET) - 订单列表
│   │   ├── detail (GET) - 订单详情
│   │   ├── confirm (POST) - 确认订单
│   │   └── status/update (POST) - 更新订单状态
│   └── schedule/
│       └── list (GET) - 排班数据
```

## 开发建议

1. **分层架构**: 严格按照Controller -> Service -> Mapper的分层架构
2. **统一响应**: 使用统一的响应格式和错误码
3. **参数验证**: 使用@Valid注解进行参数验证
4. **日志记录**: 记录关键操作的日志信息
5. **单元测试**: 为核心业务逻辑编写单元测试
6. **文档维护**: 及时更新接口文档和注释

## 部署说明

1. **环境配置**: 确保数据库连接配置正确
2. **依赖管理**: 检查Maven依赖是否完整
3. **配置文件**: 根据环境调整application.yml配置
4. **监控告警**: 配置应用监控和告警机制
5. **备份策略**: 制定数据库备份和恢复策略 