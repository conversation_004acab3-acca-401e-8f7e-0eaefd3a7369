<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.NewsMapper">

    <!-- 资讯结果映射 -->
    <resultMap id="NewsResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.NewsDO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="news_title" property="newsTitle"/>
        <result column="news_summary" property="newsSummary"/>
        <result column="news_content" property="newsContent"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="cover_image_url" property="coverImageUrl"/>
        <result column="material_id" property="materialId"/>
        <result column="author" property="author"/>
        <result column="publish_time" property="publishTime"/>
        <result column="status" property="status"/>
        <result column="view_count" property="viewCount"/>
        <result column="like_count" property="likeCount"/>
        <result column="share_count" property="shareCount"/>
        <result column="comment_count" property="commentCount"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, tenant_id, creator, create_time, updater, update_time, deleted,
        news_title, news_summary, news_content, category_id, category_name,
        cover_image_url, material_id, author, publish_time, status,
        view_count, like_count, share_count, comment_count, sort
    </sql>

    <!-- 根据资讯标题查询资讯 -->
    <select id="selectByNewsTitle" resultMap="NewsResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_news
        WHERE deleted = 0
        AND news_title = #{newsTitle}
        LIMIT 1
    </select>

    <!-- 根据资讯标题查询资讯（排除指定ID） -->
    <select id="selectByNewsTitleExcludeId" resultMap="NewsResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_news
        WHERE deleted = 0
        AND news_title = #{newsTitle}
        AND id != #{excludeId}
        LIMIT 1
    </select>

    <!-- 管理后台分页查询资讯列表 -->
    <select id="selectAdminNewsPage" resultMap="NewsResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_news
        WHERE deleted = 0
        <if test="newsTitle != null and newsTitle != ''">
            AND news_title LIKE CONCAT('%', #{newsTitle}, '%')
        </if>
        <if test="categoryId != null">
            AND category_id = #{categoryId}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="contentSource != null and contentSource == 'material'">
            AND material_id IS NOT NULL
        </if>
        <if test="contentSource != null and contentSource == 'manual'">
            AND material_id IS NULL
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 增加浏览次数 -->
    <update id="incrementViewCount">
        UPDATE publicbiz_news
        SET view_count = view_count + 1
        WHERE id = #{id}
        AND deleted = 0
    </update>

</mapper> 