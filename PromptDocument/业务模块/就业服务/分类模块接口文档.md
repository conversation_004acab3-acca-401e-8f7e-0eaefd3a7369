# 分类模块接口文档

## 模块概述

分类模块用于管理系统中的各种分类信息，支持树形结构，包含分类的增删改查功能。

## 数据库表结构

### 服务分类表 (publicbiz_service_category)

```sql
CREATE TABLE `publicbiz_service_category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类编号',
  `parent_id` bigint(20) NOT NULL COMMENT '父分类编号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '移动端分类图',
  `sort` int(11) NULL DEFAULT 0 COMMENT '分类排序',
  `status` tinyint(4) NOT NULL COMMENT '开启状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 86 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品分类' ROW_FORMAT = Dynamic;
```

## 接口列表

### 1. 创建分类

- **接口路径**: `POST /publicbiz/employment/category/create`
- **请求参数**:
  ```json
  {
    "parentId": 0,
    "name": "分类名称",
    "picUrl": "分类图标URL",
    "sort": 0,
    "status": 0
  }
  ```
- **响应数据**:
  ```json
  {
    "code": 0,
    "data": "分类编号",
    "msg": "操作成功"
  }
  ```

### 2. 更新分类

- **接口路径**: `PUT /publicbiz/employment/category/update`
- **请求参数**:
  ```json
  {
    "id": 1,
    "parentId": 0,
    "name": "分类名称",
    "picUrl": "分类图标URL",
    "sort": 0,
    "status": 0
  }
  ```
- **响应数据**:
  ```json
  {
    "code": 0,
    "data": null,
    "msg": "操作成功"
  }
  ```

### 3. 删除分类

- **接口路径**: `DELETE /publicbiz/employment/category/delete`
- **请求参数**: `id` (分类编号)
- **响应数据**:
  ```json
  {
    "code": 0,
    "data": null,
    "msg": "操作成功"
  }
  ```

### 4. 获得分类详情

- **接口路径**: `GET /publicbiz/employment/category/get`
- **请求参数**: `id` (分类编号)
- **响应数据**:
  ```json
  {
    "code": 0,
    "data": {
      "id": 1,
      "parentId": 0,
      "name": "分类名称",
      "picUrl": "分类图标URL",
      "sort": 0,
      "status": 0,
      "createTime": "2024-01-01 00:00:00"
    },
    "msg": "操作成功"
  }
  ```

### 5. 获得分类列表

- **接口路径**: `GET /publicbiz/employment/category/list`
- **请求参数**:
  - `name`: 分类名称（可选）
  - `parentId`: 父分类编号（可选）
  - `status`: 状态（可选）
- **响应数据**:
  ```json
  {
    "code": 0,
    "data": [
      {
        "id": 1,
        "parentId": 0,
        "name": "分类名称",
        "picUrl": "分类图标URL",
        "sort": 0,
        "status": 0,
        "createTime": "2024-01-01 00:00:00"
      }
    ],
    "msg": "操作成功"
  }
  ```

### 6. 获得分类分页

- **接口路径**: `GET /publicbiz/employment/category/page`
- **请求参数**:
  - `pageNo`: 页码
  - `pageSize`: 每页条数
  - `name`: 分类名称（可选）
  - `parentId`: 父分类编号（可选）
  - `status`: 状态（可选）
- **响应数据**:
  ```json
  {
    "code": 0,
    "data": {
      "list": [
        {
          "id": 1,
          "parentId": 0,
          "name": "分类名称",
          "picUrl": "分类图标URL",
          "sort": 0,
          "status": 0,
          "createTime": "2024-01-01 00:00:00"
        }
      ],
      "total": 100
    },
    "msg": "操作成功"
  }
  ```

### 7. 获得分类精简列表

- **接口路径**: `GET /publicbiz/employment/category/simple-list`
- **请求参数**: 无
- **响应数据**:
  ```json
  {
    "code": 0,
    "data": [
      {
        "id": 1,
        "name": "分类名称"
      }
    ],
    "msg": "操作成功"
  }
  ```

## 业务规则

1. **分类层级**: 支持无限级分类，通过 `parentId` 字段建立父子关系
2. **排序规则**: 通过 `sort` 字段控制同级分类的显示顺序
3. **状态管理**: 通过 `status` 字段控制分类的启用/禁用状态
4. **删除规则**: 删除分类时需要检查是否有子分类，有子分类时不允许删除
5. **权限控制**: 需要根据用户权限控制分类的增删改查操作

## 错误码定义

- `0`: 操作成功
- `1001`: 分类不存在
- `1002`: 分类名称已存在
- `1003`: 父分类不存在
- `1004`: 存在子分类，无法删除
- `1005`: 分类状态错误

## 注意事项

1. 分类名称在同一父分类下不能重复
2. 删除分类前需要检查是否有子分类
3. 分类图标建议使用 180x180 像素的图片
4. 分类排序值越小越靠前显示
5. 状态字段：0-禁用，1-启用
