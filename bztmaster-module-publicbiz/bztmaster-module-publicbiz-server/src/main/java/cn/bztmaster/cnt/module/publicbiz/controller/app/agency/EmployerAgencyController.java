package cn.bztmaster.cnt.module.publicbiz.controller.app.agency;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyAuntRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyPackageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyReviewStatsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyQualificationRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyService;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyQualificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotNull;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 机构详情")
@RestController
@RequestMapping("/publicbiz/agency")
@Validated
public class EmployerAgencyController {

    @Resource
    private AgencyService agencyService;

    @Resource
    private AgencyQualificationService agencyQualificationService;

    @GetMapping("/detail")
    @Operation(summary = "获取机构基本信息")
    @ResponseBody
    @PermitAll
    public CommonResult<AgencyDetailRespVO> getAgencyDetail(
            @Parameter(description = "机构ID", required = true) @RequestParam("id") @NotNull Long id) {
        AgencyDetailRespVO agencyDetail = agencyService.getAgencyDetail(id);
        return success(agencyDetail);
    }

    @GetMapping("/aunts")
    @Operation(summary = "获取机构阿姨列表")
    @ResponseBody
    @PermitAll
    public CommonResult<PageResult<AgencyAuntRespVO>> getAgencyAunts(
            @Parameter(description = "机构ID", required = true) @RequestParam("agencyId") @NotNull Long agencyId,
            @Parameter(description = "页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(value = "size", defaultValue = "10") Integer size) {
        PageResult<AgencyAuntRespVO> pageResult = agencyService.getAgencyAunts(agencyId, page, size);
        return success(pageResult);
    }

    @GetMapping("/packages")
    @Operation(summary = "获取机构服务套餐列表")
    @ResponseBody
    @PermitAll
    public CommonResult<PageResult<AgencyPackageRespVO>> getAgencyPackages(
            @Parameter(description = "机构ID", required = true) @RequestParam("agencyId") @NotNull Long agencyId,
            @Parameter(description = "页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(value = "size", defaultValue = "10") Integer size) {
        PageResult<AgencyPackageRespVO> pageResult = agencyService.getAgencyPackages(agencyId, page, size);
        return success(pageResult);
    }

    @GetMapping("/review-stats")
    @Operation(summary = "获取机构评价统计")
    @ResponseBody
    @PermitAll
    public CommonResult<AgencyReviewStatsRespVO> getAgencyReviewStats(
            @Parameter(description = "机构ID", required = true) @RequestParam("agencyId") @NotNull Long agencyId) {
        AgencyReviewStatsRespVO reviewStats = agencyService.getAgencyReviewStats(agencyId);
        return success(reviewStats);
    }

    @GetMapping("/qualifications")
    @Operation(summary = "获取机构资质文件列表")
    @ResponseBody
    @PermitAll
    public CommonResult<AgencyQualificationRespVO> getAgencyQualifications(
            @Parameter(description = "机构ID", required = true) @RequestParam("agencyId") @NotNull Long agencyId) {
        AgencyQualificationRespVO qualifications = agencyQualificationService.getAgencyQualificationsForApp(agencyId);
        return success(qualifications);
    }
}
