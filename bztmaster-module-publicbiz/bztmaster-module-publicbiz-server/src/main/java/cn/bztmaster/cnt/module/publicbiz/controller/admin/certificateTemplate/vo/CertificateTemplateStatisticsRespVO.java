package cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 管理后台 - 证书模板统计 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 证书模板统计 Response VO")
@Data
public class CertificateTemplateStatisticsRespVO {

    @Schema(description = "证书模板总数", requiredMode = Schema.RequiredMode.REQUIRED, example = "15")
    private Integer total;

    @Schema(description = "启用中的模板数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "8")
    private Integer activeCount;

    @Schema(description = "已停用的模板数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "4")
    private Integer inactiveCount;

    @Schema(description = "草稿状态的模板数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer draftCount;

}