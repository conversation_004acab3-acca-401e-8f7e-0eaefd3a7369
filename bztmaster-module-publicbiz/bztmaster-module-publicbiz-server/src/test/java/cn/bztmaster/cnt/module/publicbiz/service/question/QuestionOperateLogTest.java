package cn.bztmaster.cnt.module.publicbiz.service.question;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.QuestionOptionVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 考题操作日志测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
public class QuestionOperateLogTest {

    @Resource
    private QuestionService questionService;

    @Test
    public void testCreateQuestionOperateLog() {
        // 创建测试数据
        QuestionSaveReqVO reqVO = new QuestionSaveReqVO();
        reqVO.setLevel1Name("职业技能等级认定");
        reqVO.setLevel1Code("ZY001");
        reqVO.setLevel2Name("家政服务类");
        reqVO.setLevel2Code("JZ001");
        reqVO.setLevel3Name("家政服务员");
        reqVO.setLevel3Code("JZFW001");
        reqVO.setCertName("职业道德基础");
        reqVO.setCertCode("KP001");
        reqVO.setTitle("家政服务员职业道德的核心要求是什么？");
        reqVO.setType("单选题");
        reqVO.setAnswer("A");
        reqVO.setBiz("家政业务");
        reqVO.setBizName("家政服务业务");
        reqVO.setDifficulty(1);
        reqVO.setScore(new BigDecimal("2.0"));
        reqVO.setTimeLimit(60);
        reqVO.setExplanation("家政服务员应当具备良好的职业道德...");
        reqVO.setKeywords("职业道德,家政服务");

        // 创建选项
        QuestionOptionVO optionA = new QuestionOptionVO();
        optionA.setOptionKey("A");
        optionA.setOptionContent("诚实守信、尊重客户");
        optionA.setIsCorrect(true);
        optionA.setOptionType("choice");
        optionA.setSortOrder(1);

        QuestionOptionVO optionB = new QuestionOptionVO();
        optionB.setOptionKey("B");
        optionB.setOptionContent("随意应付、敷衍了事");
        optionB.setIsCorrect(false);
        optionB.setOptionType("choice");
        optionB.setSortOrder(2);

        reqVO.setOptions(Arrays.asList(optionA, optionB));

        // 执行创建操作，这应该会触发操作日志记录
        Long questionId = questionService.createQuestion(reqVO);

        System.out.println("创建考题成功，ID: " + questionId);
        System.out.println("请检查操作日志表中是否有相应的日志记录");
    }

    @Test
    public void testUpdateQuestionOperateLog() {
        // 先创建一个考题
        Long questionId = createTestQuestion();

        // 创建更新数据，重点测试 answer 字段的变更记录
        QuestionSaveReqVO updateReqVO = new QuestionSaveReqVO();
        updateReqVO.setId(questionId);
        updateReqVO.setLevel1Name("职业技能等级认定");
        updateReqVO.setLevel1Code("ZY001");
        updateReqVO.setLevel2Name("家政服务类");
        updateReqVO.setLevel2Code("JZ001");
        updateReqVO.setLevel3Name("家政服务员");
        updateReqVO.setLevel3Code("JZFW001");
        updateReqVO.setCertName("职业道德基础");
        updateReqVO.setCertCode("KP001");
        updateReqVO.setTitle("被称为基建狂魔的是哪个国家？"); // 修改题干
        updateReqVO.setType("单选题"); // 保持题型不变
        updateReqVO.setAnswer("B"); // 修改答案：从 A 改为 B
        updateReqVO.setBiz("家政业务");
        updateReqVO.setBizName("家政服务业务");
        updateReqVO.setDifficulty(2); // 修改难度：从 1 改为 2
        updateReqVO.setScore(new BigDecimal("3.0")); // 修改分值：从 2.0 改为 3.0

        // 执行更新操作，这应该会触发操作日志记录
        questionService.updateQuestion(updateReqVO);

        System.out.println("更新考题成功，ID: " + questionId);
        System.out.println("应该记录以下字段变更：");
        System.out.println("- 题干内容：从【测试考题标题】修改为【被称为基建狂魔的是哪个国家？】");
        System.out.println("- 参考答案：从【A】修改为【B】");
        System.out.println("- 难度等级：从【1】修改为【2】");
        System.out.println("- 题目分值：从【2.0】修改为【3.0】");
        System.out.println("请检查操作日志表中是否有完整的字段变更记录");
    }

    @Test
    public void testDeleteQuestionOperateLog() {
        // 先创建一个考题
        Long questionId = createTestQuestion();

        // 执行删除操作，这应该会触发操作日志记录
        questionService.deleteQuestion(questionId);

        System.out.println("删除考题成功，ID: " + questionId);
        System.out.println("请检查操作日志表中是否有删除操作的日志记录");
    }

    /**
     * 创建测试考题的辅助方法
     */
    private Long createTestQuestion() {
        QuestionSaveReqVO reqVO = new QuestionSaveReqVO();
        reqVO.setLevel1Name("职业技能等级认定");
        reqVO.setLevel1Code("ZY001");
        reqVO.setLevel2Name("家政服务类");
        reqVO.setLevel2Code("JZ001");
        reqVO.setLevel3Name("家政服务员");
        reqVO.setLevel3Code("JZFW001");
        reqVO.setCertName("职业道德基础");
        reqVO.setCertCode("KP001");
        reqVO.setTitle("测试考题标题");
        reqVO.setType("单选题");
        reqVO.setAnswer("A");
        reqVO.setBiz("家政业务");
        reqVO.setBizName("家政服务业务");
        reqVO.setDifficulty(1);
        reqVO.setScore(new BigDecimal("2.0"));

        return questionService.createQuestion(reqVO);
    }
}
