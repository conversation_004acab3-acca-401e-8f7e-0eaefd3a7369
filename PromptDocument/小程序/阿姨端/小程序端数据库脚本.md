

### 系统用户表字段扩展脚本
-- 为 system_users 表添加账户类型和员工状态字段

-- 添加账户类型字段
ALTER TABLE `system_users` 
ADD COLUMN `account_type` TINYINT(1) NOT NULL DEFAULT '2' COMMENT '账户类型，1-内部员工，2-企业用户' AFTER `id`;

-- 添加员工状态字段
ALTER TABLE `system_users` 
ADD COLUMN `status` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '状态，1-在职，2-离职，3-试用期，4-停职' AFTER `account_type`;

-- 添加工作状态类型字段
ALTER TABLE `system_users` 
ADD COLUMN `work_status_type` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '工作状态类型，1-全职，2-兼职，3-其它' AFTER `status`;

-- 为现有数据设置默认值（可选）
-- 如果现有数据需要设置为特定类型，可以执行以下语句：
-- UPDATE `system_users` SET `account_type` = 2 WHERE `account_type` IS NULL;
-- UPDATE `system_users` SET `status` = 1 WHERE `status` IS NULL;
-- UPDATE `system_users` SET `work_status_type` = 1 WHERE `work_status_t


### 小程序和公众号粉丝表

```sql

-- 新增字段脚本
ALTER TABLE `mp_user` 
ADD COLUMN `platform_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'official_account' COMMENT '平台类型：miniprogram-小程序，official_account-公众号' AFTER `city`,

ADD COLUMN `mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号（仅小程序使用）' AFTER `subscribe_time`;

ADD COLUMN `oneid` char(36) NOT NULL DEFAULT '' COMMENT 'OneID GUID' AFTER `id`,
-- 更新表注释
ALTER TABLE `mp_user` COMMENT = '微信用户表（小程序+公众号）';


ALTER TABLE `system_notify_message` 
ADD COLUMN `one_id` varchar(50)  COMMENT '用户ID，oneId' AFTER `user_id`

```




# 雇主端

## 1. 阿姨评价表 (publicbiz_aunt_review)

```sql
CREATE TABLE `publicbiz_aunt_review` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `aunt_oneid` char(36) NOT NULL DEFAULT '' COMMENT '阿姨OneID GUID',
  `reviewer_id` bigint(20) NOT NULL COMMENT '评价人用户ID',
  `reviewer_name` varchar(50) NOT NULL COMMENT '评价人姓名',
  `reviewer_avatar` varchar(500) DEFAULT NULL COMMENT '评价人头像URL',
  `rating` decimal(2,1) NOT NULL COMMENT '评分：1.0-5.0',
  `review_tags` text COMMENT '评价标签（JSON格式，如：["专业","细心","守时"]）',
  `review_content` text COMMENT '评价内容',
  `review_images` text COMMENT '评价图片URL列表（JSON格式）',
  `review_type` varchar(20) DEFAULT 'service' COMMENT '评价类型：service-服务评价，attitude-态度评价，professional-专业评价',
  `is_anonymous` tinyint(1) DEFAULT '0' COMMENT '是否匿名评价：0-否，1-是',
  `is_recommend` tinyint(1) DEFAULT '0' COMMENT '是否推荐：0-否，1-是',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞数',
  `reply_content` text COMMENT '回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-隐藏，1-显示',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阿姨评价表';
```

# 阿姨排班系统数据库表结构

## 2. 服务打卡记录表 (service_punch_record)

```sql
CREATE TABLE `service_punch_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID',
  `schedule_id` bigint(20) NOT NULL COMMENT '排班ID',
  `aunt_oneid` char(36) NOT NULL DEFAULT '' COMMENT '阿姨的OneID GUID',
  `aunt_name` varchar(64) NOT NULL COMMENT '阿姨姓名',
  `punch_type` tinyint(1) NOT NULL COMMENT '打卡类型(1-开始打卡,2-完成打卡)',
  `punch_time` datetime NOT NULL COMMENT '打卡时间',
  `punch_location` varchar(255) NOT NULL COMMENT '打卡位置',
  `punch_latitude` decimal(10,7) NOT NULL COMMENT '打卡纬度',
  `punch_longitude` decimal(10,7) NOT NULL COMMENT '打卡经度',
  `photo_count` int(2) NOT NULL DEFAULT '0' COMMENT '照片数量',
  `photo_urls` text DEFAULT NULL COMMENT '照片URL列表，用逗号分隔',
  `remark` varchar(500) DEFAULT NULL COMMENT '打卡备注',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务打卡记录表';
```

# 机构财务中心数据库表结构

## 机构财务流水表 (agency_finance_transaction)

```sql
CREATE TABLE `agency_finance_transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `agency_id` bigint(20) NOT NULL COMMENT '机构ID',
    `agency_name` varchar(50) NOT NULL COMMENT '机构名称',
  `transaction_type` varchar(20) NOT NULL COMMENT '交易类型：income-收入，expense-支出',
  `transaction_category` varchar(50) DEFAULT NULL COMMENT '交易分类：订单收入、保险订单、佣金收入等',
  `description` varchar(255) NOT NULL COMMENT '交易描述',
  `amount` decimal(15,2) NOT NULL COMMENT '显示金额',
  `transaction_datetime` datetime NOT NULL COMMENT '交易时间',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式：在线支付、微信支付、支付宝等',
  `account_type` varchar(50) DEFAULT NULL COMMENT '账户类型：微信账户、在线支付账户等',
  `post_balance` decimal(15,2) DEFAULT NULL COMMENT '交易后账户余额',
  `balance_display` varchar(100) DEFAULT NULL COMMENT '余额显示文本（如：微信账户结余: ¥12360）',
  
  -- 关联字段
  `order_id` bigint(20) DEFAULT NULL COMMENT '关联订单ID',
  `order_type` varchar(20) DEFAULT NULL COMMENT '订单类型：insurance-保险订单、service-服务订单等',

  
  -- 统计字段
  `year` int(4) NOT NULL COMMENT '交易年份',
  `month` int(2) NOT NULL COMMENT '交易月份',
  `day` int(2) NOT NULL COMMENT '交易日期',
  
  -- 状态字段
  `status` varchar(20) DEFAULT 'success' COMMENT '交易状态：success-成功，failed-失败，pending-处理中',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  
  PRIMARY KEY (`id`),
  KEY `idx_agency_id` (`agency_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机构财务流水表';

```

