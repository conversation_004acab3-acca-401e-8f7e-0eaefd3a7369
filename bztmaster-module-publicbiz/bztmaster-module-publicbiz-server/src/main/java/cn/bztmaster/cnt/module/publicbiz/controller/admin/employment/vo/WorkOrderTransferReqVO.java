package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 工单转派请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单转派请求 VO")
@Data
public class WorkOrderTransferReqVO {

    @Schema(description = "工单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "WO202312010001")
    @NotBlank(message = "工单编号不能为空")
    private String workOrderNo;

    @Schema(description = "新负责人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "456")
    @NotNull(message = "新负责人ID不能为空")
    private Long newAssigneeId;

    @Schema(description = "新负责人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotBlank(message = "新负责人姓名不能为空")
    private String newAssigneeName;

    @Schema(description = "转派原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "工作量大，需要协助处理")
    @NotBlank(message = "转派原因不能为空")
    private String transferReason;

    @Schema(description = "转派备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "请优先处理此工单")
    private String transferRemark;

    @Schema(description = "调整后的优先级", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "HIGH")
    private String priority;
} 