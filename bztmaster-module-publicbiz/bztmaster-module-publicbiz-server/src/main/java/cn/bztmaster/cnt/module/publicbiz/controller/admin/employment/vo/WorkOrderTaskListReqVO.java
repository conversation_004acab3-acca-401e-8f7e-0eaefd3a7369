package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 工单任务列表查询请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单任务列表查询请求 VO")
@Data
public class WorkOrderTaskListReqVO {

    @Schema(description = "任务状态筛选：pending-待分配/assigned-已分配/in_progress-进行中/completed-已完成/cancelled-已取消")
    private String taskStatus;

    @Schema(description = "执行人员姓名，支持模糊查询")
    private String practitionerName;

    @Schema(description = "执行人员OneID")
    private String practitionerOneid;
} 