<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerServiceRecordMapper">

    <sql id="selectFields">
        id, practitioner_id, order_id, customer_id, customer_name, service_type, service_start_time, service_end_time,
        service_duration, service_address, service_amount, practitioner_income, platform_income, order_status,
        customer_rating, customer_comment, remark, create_time, update_time, creator, updater, deleted, tenant_id
    </sql>

</mapper> 