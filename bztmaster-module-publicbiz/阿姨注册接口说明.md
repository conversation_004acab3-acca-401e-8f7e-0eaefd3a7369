# 阿姨注册接口说明

## 概述

根据阿姨注册接口开发文档，已生成完整的后端接口代码，包含以下功能：

1. **提交阿姨注册申请** - 支持手机验证、信息录入、归属选择、证件上传
2. **获取注册申请状态** - 查询申请审核状态
3. **获取注册申请详情** - 查看完整的申请信息
4. **重新提交注册申请** - 支持被拒绝后的重新提交
5. **获取家政机构列表** - 支持机构搜索

## 生成的代码文件

### API层 (bztmaster-module-publicbiz-api)
- `AuntRegisterApi.java` - API接口定义
- `dto/` 目录下的所有DTO类：
  - `AuntRegisterSubmitReqDTO.java` - 提交请求DTO
  - `AuntRegisterSubmitRespDTO.java` - 提交响应DTO
  - `AuntRegisterStatusRespDTO.java` - 状态响应DTO
  - `AuntRegisterDetailRespDTO.java` - 详情响应DTO
  - `AuntRegisterResubmitReqDTO.java` - 重新提交请求DTO
  - `AgencyListRespDTO.java` - 机构列表响应DTO

### 服务层 (bztmaster-module-publicbiz-server)
- `AuntRegisterService.java` - 服务接口
- `impl/AuntRegisterServiceImpl.java` - 服务实现类
- `AuntRegisterApiImpl.java` - API实现类

### 控制器层
- `AuntRegisterController.java` - REST控制器
- `vo/` 目录下的所有VO类：
  - `AuntRegisterSubmitReqVO.java` - 提交请求VO
  - `AuntRegisterSubmitRespVO.java` - 提交响应VO
  - `AuntRegisterStatusRespVO.java` - 状态响应VO
  - `AuntRegisterDetailRespVO.java` - 详情响应VO
  - `AuntRegisterResubmitReqVO.java` - 重新提交请求VO
  - `AgencyListRespVO.java` - 机构列表响应VO

### 数据层
- `PublicbizPractitionerDO.java` - 阿姨基本信息数据对象
- `PublicbizPractitionerQualificationDO.java` - 阿姨资质文件数据对象
- `PublicbizPractitionerMapper.java` - 阿姨基本信息Mapper
- `PublicbizPractitionerQualificationMapper.java` - 阿姨资质文件Mapper

### 转换器
- `AuntRegisterConvert.java` - VO与DTO之间的转换器

## 接口地址

### 1. 提交阿姨注册申请
```
POST /publicbiz/aunt/register/submit
```

### 2. 获取阿姨注册申请状态
```
GET /publicbiz/aunt/register/status?mobile={mobile}&openId={openId}
```

### 3. 获取阿姨注册申请详情
```
GET /publicbiz/aunt/register/detail?applicationId={applicationId}
```

### 4. 重新提交阿姨注册申请
```
POST /publicbiz/aunt/register/resubmit
```

### 5. 获取家政机构列表
```
GET /publicbiz/aunt/register/agencies?keyword={keyword}
```

## 数据库表

代码中使用了两个主要的数据表：

1. **publicbiz_practitioner** - 阿姨基本信息表
2. **publicbiz_practitioner_qualification** - 阿姨资质文件表

## 测试

提供了 `AuntRegisterController.http` 文件，包含所有接口的测试用例，可以直接在IDE中运行测试。

## 注意事项

1. 所有接口都使用了 `@PermitAll` 注解，允许匿名访问
2. 实现了完整的参数验证，包括手机号格式、身份证号格式等
3. 支持微信授权获取的手机号直接通过验证
4. 实现了完整的业务逻辑，包括数据验证、状态管理等
5. 使用了事务管理确保数据一致性
6. 提供了详细的错误码和错误信息

## 错误码

- 1001: 手机号格式错误
- 1002: 手机号已注册
- 1003: 身份证号格式错误
- 1004: 身份证号已注册
- 1005: 必填信息不完整
- 1006: 证件照片上传失败
- 1007: 申请不存在
- 1008: 申请状态不允许重新提交
- 1009: 机构不存在
- 1010: 文件格式不支持
- 1011: 文件大小超限
