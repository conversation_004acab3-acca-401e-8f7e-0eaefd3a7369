package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.TalentUserTagDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.TalentUserTagDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户标签关联表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TalentUserTagMapper extends BaseMapperX<TalentUserTagDO> {

    /**
     * 根据用户ID查询标签详情列表
     *
     * @param userId 用户ID
     * @return 标签详情列表
     */
    List<TalentUserTagDetailDO> selectTagDetailsByUserId(@Param("userId") Long userId);

    /**
     * 根据OneID查询标签详情列表
     *
     * @param oneid OneID
     * @return 标签详情列表
     */
    List<TalentUserTagDetailDO> selectTagDetailsByOneid(@Param("oneid") String oneid);
}
