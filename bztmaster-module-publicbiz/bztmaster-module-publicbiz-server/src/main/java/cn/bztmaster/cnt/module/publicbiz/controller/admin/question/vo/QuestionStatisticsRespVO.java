package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 考题统计响应 VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "资源中心 - 考题统计响应 VO")
public class QuestionStatisticsRespVO {

    /**
     * 题目总数
     */
    @Schema(description = "题目总数", example = "15")
    private Integer total;

    /**
     * 选择题数（单选+多选）
     */
    @Schema(description = "选择题数", example = "6")
    private Integer single;

    /**
     * 判断题数
     */
    @Schema(description = "判断题数", example = "2")
    private Integer judge;

    /**
     * 简答题数
     */
    @Schema(description = "简答题数", example = "2")
    private Integer shortAnswer;

    /**
     * 填空题数
     */
    @Schema(description = "填空题数", example = "1")
    private Integer fill;

    /**
     * 材料题数
     */
    @Schema(description = "材料题数", example = "1")
    private Integer material;

    /**
     * 排序题数
     */
    @Schema(description = "排序题数", example = "1")
    private Integer sort;

    /**
     * 匹配题数
     */
    @Schema(description = "匹配题数", example = "1")
    private Integer match;

    /**
     * 文件上传题数
     */
    @Schema(description = "文件上传题数", example = "1")
    private Integer upload;

}
