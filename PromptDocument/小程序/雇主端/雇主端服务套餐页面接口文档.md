# 雇主端服务套餐页面接口文档

## 概述

本文档描述了雇主端服务套餐页面所需的后端接口，包括服务分类获取和套餐列表查询功能。页面展示内容参考截图，支持多种排序方式和筛选条件。

## 数据库表结构

### 1. 服务套餐主表 (publicbiz_service_package)

```sql
CREATE TABLE `publicbiz_service_package` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `name` varchar(200) NOT NULL COMMENT '套餐名称',
  `category` varchar(50) NOT NULL COMMENT '服务分类：日常保洁/深度保洁/家电清洗/专项服务/月嫂服务/收纳整理',
  `thumbnail` varchar(500) DEFAULT NULL COMMENT '套餐主图URL',
  `price` decimal(10,2) NOT NULL COMMENT '套餐价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `unit` varchar(20) NOT NULL COMMENT '价格单位：次/项/天/月',
  `service_duration` varchar(100) DEFAULT NULL COMMENT '服务时长，如：4小时、26天、90天',
  `package_type` varchar(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐',
  `task_split_rule` varchar(200) DEFAULT NULL COMMENT '任务拆分规则',
  `service_description` text COMMENT '服务描述，建议100-200字',
  `service_details` longtext COMMENT '详细服务内容，富文本格式',
  `service_process` longtext COMMENT '服务流程，富文本格式',
  `purchase_notice` text COMMENT '购买须知',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：active-已上架/pending-待上架/deleted-回收站',
  `advance_booking_days` int(11) NOT NULL DEFAULT '1' COMMENT '预约时间范围：1-提前1天/3-提前3天/7-提前7天',
  `time_selection_mode` varchar(20) NOT NULL DEFAULT 'fixed' COMMENT '时间选择模式：fixed-固定时间/flexible-灵活时间',
  `appointment_mode` varchar(20) NOT NULL DEFAULT 'start-date' COMMENT '预约模式：start-date-开始日期预约/all-at-once-一次性预约全部服务次数',
  `service_start_time` varchar(20) NOT NULL DEFAULT 'within-3-days' COMMENT '服务开始时间：within-3-days-下单后3天内开始/specified-date-指定日期开始',
  `address_setting` varchar(20) NOT NULL DEFAULT 'fixed' COMMENT '地址设置：fixed-固定地址/changeable-可变更地址',
  `max_booking_days` int(11) NOT NULL DEFAULT '30' COMMENT '最大预约天数',
  `cancellation_policy` varchar(500) DEFAULT NULL COMMENT '取消政策',
  `audit_status` varchar(20) NOT NULL DEFAULT 'auditing' COMMENT '审核状态：pending-待审核， auditing-审核中，approved-已通过，rejected-已拒绝',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
  `agency_name` varchar(100) DEFAULT NULL COMMENT '所属机构名称',
  `reject_reason` text COMMENT '拒绝原因，审核拒绝时填写',
  `category_id` bigint(20) DEFAULT NULL COMMENT '服务分类ID',
  -- 次数次卡套餐专用字段
  `service_times` int(11) DEFAULT NULL COMMENT '服务次数(次)或服务周期(天)，如：4、6、10',
  `validity_period` int(11) DEFAULT NULL COMMENT '有效期（天），如：90天、180天、365天',
  `validity_period_unit` varchar(20) DEFAULT 'day' COMMENT '有效期单位：day-天/week-周/month-月/year-年',
  `service_interval_type` varchar(20) DEFAULT NULL COMMENT '服务间隔类型/服务频次类型：weekly-每周/monthly-每月/flexible-灵活安排',
  `service_interval_value` int(11) DEFAULT NULL COMMENT '服务间隔数值/服务频次数值，如：1表示每周1次或每月1次',
  `single_duration_hours` int(11) DEFAULT NULL COMMENT '单次服务时长（小时），如：2、4、6',
  -- 长周期套餐专用字段
  `service_time` varchar(4000) DEFAULT NULL COMMENT '服务时间多个时间段逗号隔开，如：9:00-13:00,15:00-17:00',
  `service_time_start` time DEFAULT NULL COMMENT '服务开始时间，如：09:00:00',
  `service_time_end` time DEFAULT NULL COMMENT '服务结束时间，如：13:00:00',
  `rest_day_type` varchar(20) DEFAULT NULL COMMENT '休息日类型：none-无特殊设置/sunday-周日/statutory-法定节假日/both-周日及法定节假日/negotiable-客户协商',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_category` (`category`),
  KEY `idx_package_type` (`package_type`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_status` (`status`),
  KEY `idx_audit_status` (`audit_status`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='服务套餐主表';
```

### 2. 机构主表 (publicbiz_agency)

```sql
CREATE TABLE `publicbiz_agency` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `agency_no` varchar(50) NOT NULL COMMENT '机构编号',
  `agency_name` varchar(200) NOT NULL COMMENT '机构全称',
  `agency_short_name` varchar(100) DEFAULT NULL COMMENT '机构简称',
  `agency_type` varchar(30) NOT NULL COMMENT '机构类型：cooperation-合作/competitor-竞争对手/other-其他',
  `legal_representative` varchar(50) DEFAULT NULL COMMENT '法人代表',
  `unified_social_credit_code` varchar(50) DEFAULT NULL COMMENT '统一社会信用代码',
  `establishment_date` date DEFAULT NULL COMMENT '成立日期',
  `registered_address` varchar(500) DEFAULT NULL COMMENT '注册地址',
  `operating_address` varchar(500) DEFAULT NULL COMMENT '经营地址',
  `business_scope` text COMMENT '经营范围',
  `applicant_name` varchar(50) DEFAULT NULL COMMENT '申请人姓名',
  `applicant_phone` varchar(20) DEFAULT NULL COMMENT '申请人电话',
  `application_time` datetime DEFAULT NULL COMMENT '申请时间',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `agency_address` varchar(500) DEFAULT NULL COMMENT '机构地址',
  `province_code` varchar(50) DEFAULT NULL COMMENT '省份code',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city_code` varchar(50) DEFAULT NULL COMMENT '城市code',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `district_code` varchar(50) DEFAULT NULL COMMENT '区县code',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `street_code` varchar(100) DEFAULT NULL COMMENT '街道',
  `street` varchar(100) DEFAULT NULL COMMENT '街道',
  `detail_address` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `location_accuracy` varchar(20) DEFAULT NULL COMMENT '位置精度：high-高精度/medium-中等精度/low-低精度',
  `cooperation_status` varchar(20) NOT NULL DEFAULT 'cooperating' COMMENT '合作状态：cooperating-合作中/suspended-已暂停/terminated-已终止',
  `contract_no` varchar(50) DEFAULT NULL COMMENT '合同编号',
  `contract_start_date` date DEFAULT NULL COMMENT '合同开始日期',
  `contract_end_date` date DEFAULT NULL COMMENT '合同结束日期',
  `commission_rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例',
  `review_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-待审核/approved-已通过/rejected-已拒绝',
  `reviewer` varchar(64) DEFAULT NULL COMMENT '审核人',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_remark` text COMMENT '审核备注',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-正常/inactive-停用/pending-待审核',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agency_no` (`agency_no`),
  UNIQUE KEY `uk_unified_social_credit_code` (`unified_social_credit_code`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_agency_name` (`agency_name`),
  KEY `idx_agency_type` (`agency_type`),
  KEY `idx_longitude_latitude` (`longitude`,`latitude`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1006 DEFAULT CHARSET=utf8mb4 COMMENT='机构主表信息';
```

### 3. 套餐服务特色标签表 (publicbiz_package_feature)

```sql
CREATE TABLE `publicbiz_package_feature` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `package_id` bigint(20) NOT NULL COMMENT '套餐ID',
  `feature_name` varchar(100) NOT NULL COMMENT '特色标签名称',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序，数字越小越靠前',
  PRIMARY KEY (`id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_feature_name` (`feature_name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COMMENT='服务套餐特色标签表';
```

## 接口文档
## 接口信息

### 1. 根据分类获取套餐列表接口

**接口地址：** `GET /publicbiz/employer/service-category/packagelists`

**接口描述：** 根据分类ID获取该分类下的服务套餐列表，支持多种排序方式和套餐名称模糊搜索

**请求参数：**
```json
{
  "categoryId": 2,                    // 分类ID，可选，不传则搜索所有分类
  "keyword": "保洁",                   // 套餐名称关键词，可选，支持模糊搜索
  "sortType": "distance",             // 排序类型：distance-距离优先/price-价格优先/satisfaction-满意度优先/comprehensive-综合，默认distance
  "longitude": 116.397128,            // 用户当前经度，必填
  "latitude": 39.916527,              // 用户当前纬度，必填
  "page": 1,                          // 页码，默认1
  "pageSize": 20                      // 每页数量，默认20
}
```

**响应数据：**
```json
{
  "code": 1,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 4,
        "name": "月度保洁服务 | 30天",
        "category": "日常保洁",
        "categoryId": 2,
        "thumbnail": "/static/保洁服务图片.png",
        "price": 2800.00,
        "originalPrice": 3200.00,
        "unit": "天",
        "serviceDuration": "30天 | 每日2小时",
        "packageType": "long-term",
        "serviceDescription": "专业的日常保洁服务，每日2小时，持续30天",
        "status": "active",
        "auditStatus": "approved",
        "agencyId": 1001,
        "agencyName": "金牌家政",
        "agencyLongitude": 116.397128,
        "agencyLatitude": 39.916527,
        "features": [
          "日常保洁",
          "固定时间",
          "专业阿姨"
        ],
        "distance": 0.5,              // 距离用户位置的距离（公里）
        "satisfactionRate": 4.8,      // 满意度评分
        // 次数次卡套餐专用字段
        "serviceTimes": 2,            // 服务次数
        "validityPeriod": 90,         // 有效期（天）
        "validityPeriodUnit": "day",  // 有效期单位
        "serviceIntervalType": "weekly", // 服务间隔类型
        "serviceIntervalValue": 1,    // 服务间隔数值
        "singleDurationHours": 2,     // 单次服务时长（小时）
        // 长周期套餐专用字段
        "serviceTime": "9:00-13:00",  // 服务时间
        "serviceTimeStart": "09:00:00", // 服务开始时间
        "serviceTimeEnd": "13:00:00",   // 服务结束时间
        "restDayType": "sunday"       // 休息日类型
      },
      {
        "id": 5,
        "name": "2次日常保洁",
        "category": "日常保洁",
        "categoryId": 2,
        "thumbnail": "/static/保洁服务图片2.png",
        "price": 188.00,
        "originalPrice": 220.00,
        "unit": "次",
        "serviceDuration": "服务次数2次 | 有效期90天",
        "packageType": "count-card",
        "serviceDescription": "2次日常保洁服务，有效期90天",
        "status": "active",
        "auditStatus": "approved",
        "agencyId": 1001,
        "agencyName": "金牌家政",
        "agencyLongitude": 116.397128,
        "agencyLatitude": 39.916527,
        "features": [
          "日常保洁",
          "固定时间",
          "专业阿姨"
        ],
        "distance": 0.5,
        "satisfactionRate": 4.8,
        "serviceTimes": 2,
        "validityPeriod": 90,
        "validityPeriodUnit": "day",
        "serviceIntervalType": "flexible",
        "serviceIntervalValue": null,
        "singleDurationHours": 2
      }
    ],
    "total": 2,
    "page": 1,
    "pageSize": 20
  }
}
```

## 业务逻辑说明

### 搜索逻辑

1. **套餐名称模糊搜索**：
   - 支持对套餐名称（name字段）进行模糊匹配
   - 搜索不区分大小写
   - 支持中文、英文、数字的模糊匹配
   - 当keyword参数存在时，优先按关键词搜索，忽略categoryId参数
   - 搜索结果默认按距离优先排序

2. **分类筛选**：
   - 当只传递categoryId参数时，按分类ID筛选套餐
   - 当同时传递categoryId和keyword时，优先按keyword搜索
   - 当不传递categoryId时，搜索所有分类下的套餐

### 排序逻辑

1. **距离优先（distance）**：
   - 根据用户当前位置与服务套餐所属机构的经纬度计算距离
   - 按距离由近到远排序
   - 距离计算公式：使用Haversine公式计算两点间的球面距离
   - **搜索模式下的默认排序方式**

2. **价格优先（price）**：
   - 按套餐价格从低到高排序
   - 相同价格按创建时间倒序排列

3. **满意度优先（satisfaction）**：
   - 按同套餐的评分从高到低排序
   - 相同满意度按距离排序

4. **综合（comprehensive）**：
   - 综合考虑距离、价格、满意度等因素
         *   **综合评分计算公式:** `综合评分 = 机构评分权重×40% + 距离权重×25% + 满意度权重×25% + 价格权重×10% `
         *   **各维度权重分数计算:**
             *   `机构评分权重分数 = (机构所有套餐的综合评分 / 5.0) × 100`
             *   `距离权重分数 = (1 - 实际距离/最大距离) × 100`（距离越近分数越高）
             *   `满意度权重分数 = (该套餐满意度评分 / 5.0) × 100`
             *   `价格权重分数 = (1 - 套餐价格/同类套餐最高价格) × 100`（价格越低分数越高）

### 距离计算

- 使用用户当前位置的经纬度（longitude, latitude）
- 使用机构表中的经纬度字段（longitude, latitude）
- 计算两点间的球面距离，单位为公里
- 使用Haversine公式确保计算精度

### 特色标签说明

- features字段包含该套餐的特色标签列表
- 标签数据来源于publicbiz_package_feature表
- 按sort_order字段排序
- 最多返回5个标签

### 状态过滤

- status: active-已上架/pending-待上架/deleted-回收站
- auditStatus: pending-待审核/auditing-审核中/approved-已通过/rejected-已拒绝
- 接口只返回status为active且auditStatus为approved的套餐

### 套餐类型说明

1. **长周期套餐（long-term）**：
   - 适用于长期服务，如月嫂、住家保姆等
   - 显示服务时间、休息日设置等信息
   - 标签显示为"长周期套餐"

2. **次数次卡套餐（count-card）**：
   - 适用于按次数计费的服务，如保洁、护理等
   - 显示服务次数、有效期等信息
   - 标签显示为"次数次卡套餐"

### 数据展示规则

1. **套餐名称**：直接使用name字段
2. **服务描述**：组合serviceDuration和unit字段
3. **价格显示**：显示price字段，如果有originalPrice则显示原价对比
4. **机构名称**：显示agencyName字段
5. **特色标签**：显示features数组中的标签
6. **套餐标签**：根据packageType字段动态生成

### 搜索场景说明

1. **首页搜索**：
   - 用户在首页搜索框输入关键词后回车
   - 跳转到服务套餐页面，传递keyword参数
   - 页面默认选中搜索结果中第一个套餐对应的分类
   - 左侧分类导航根据搜索结果高亮对应分类

2. **分类浏览**：
   - 用户点击左侧分类导航
   - 传递categoryId参数，不传递keyword
   - 显示该分类下的所有套餐

### 错误处理

1. **位置获取失败**：使用默认位置（成都：104.0665°E, 30.5728°N）
2. **网络请求失败**：返回友好的错误提示
3. **数据为空**：显示空状态页面
4. **权限不足**：引导用户进行授权

## 注意事项

1. 所有接口需要支持跨域请求
2. 响应数据需要包含适当的缓存控制头
3. 分页参数需要验证合法性
4. 经纬度参数需要验证范围（经度：-180到180，纬度：-90到90）
5. 距离计算需要考虑地球曲率
6. 特色标签需要防止XSS攻击
7. 图片URL需要支持CDN加速
8. 搜索关键词需要进行安全过滤，防止SQL注入
9. 搜索结果的缓存策略需要考虑关键词和位置的变化
10. 模糊搜索建议使用全文索引优化查询性能
