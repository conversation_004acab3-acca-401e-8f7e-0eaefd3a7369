<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汇成人力资源服务平台 - 运营后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #3498db;
            --secondary: #2980b9;
            --success: #2ecc71;
            --warning: #f39c12;
            --danger: #e74c3c;
            --light: #f8f9fa;
            --dark: #343a40;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --border: #dee2e6;
            --sidebar-width: 240px;
            --header-height: 60px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: #333;
            display: flex;
            min-height: 100vh;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: var(--sidebar-width);
            background: linear-gradient(135deg, #1a2a6c, #2a5298);
            color: white;
            height: 100vh;
            position: fixed;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 1000;
        }
        
        .logo {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .logo img {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            background: white;
            border-radius: 8px;
            padding: 5px;
        }
        
        .logo h1 {
            font-size: 18px;
            font-weight: 600;
        }
        
        .nav-section {
            margin-bottom: 20px;
        }
        
        .nav-section h3 {
            font-size: 12px;
            text-transform: uppercase;
            padding: 10px 20px;
            color: rgba(255,255,255,0.6);
            letter-spacing: 1px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
            border-left: 3px solid transparent;
            font-size: 14px;
        }
        
        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .nav-link.active {
            background: rgba(255,255,255,0.15);
            color: white;
            border-left: 3px solid var(--success);
        }
        
        .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .badge {
            background: var(--danger);
            color: white;
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 11px;
            margin-left: auto;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
        }
        
        /* 顶部导航栏 */
        .topbar {
            height: var(--header-height);
            background: white;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--border);
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .search-box {
            background: var(--light);
            border-radius: 20px;
            padding: 8px 15px;
            width: 300px;
            display: flex;
            align-items: center;
        }
        
        .search-box input {
            border: none;
            background: transparent;
            padding: 0 10px;
            width: 100%;
            outline: none;
        }
        
        .user-actions {
            display: flex;
            align-items: center;
        }
        
        .notification {
            position: relative;
            margin-right: 20px;
            cursor: pointer;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        
        /* 工作台内容 */
        .dashboard {
            padding: 20px;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .dashboard-header h2 {
            font-size: 24px;
            color: var(--dark);
            display: flex;
            align-items: center;
        }
        
        .dashboard-header h2 i {
            margin-right: 10px;
            color: var(--primary);
        }
        
        .date-display {
            background: white;
            padding: 10px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 14px;
        }
        
        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header h3 {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark);
        }
        
        .card-actions {
            display: flex;
        }
        
        .card-actions button {
            background: none;
            border: none;
            color: var(--gray);
            cursor: pointer;
            margin-left: 10px;
            font-size: 14px;
        }
        
        .card-body {
            padding: 20px;
        }
        
        /* 网格布局 */
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        /* KPI卡片 */
        .kpi-card {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .kpi-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary);
            margin: 10px 0;
        }
        
        .kpi-label {
            font-size: 14px;
            color: var(--gray);
        }
        
        .kpi-trend {
            margin-top: 5px;
            font-size: 12px;
            color: var(--success);
        }
        
        .kpi-trend.down {
            color: var(--danger);
        }
        
        /* 快速入口 */
        .quick-access {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            text-align: center;
        }
        
        .quick-item {
            padding: 15px;
            background: var(--light);
            border-radius: 8px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .quick-item:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-3px);
        }
        
        .quick-item:hover i {
            color: white;
        }
        
        .quick-item i {
            font-size: 24px;
            color: var(--primary);
            margin-bottom: 10px;
        }
        
        .quick-item span {
            display: block;
            font-size: 13px;
        }
        
        /* 任务列表 */
        .task-list {
            list-style: none;
        }
        
        .task-item {
            padding: 15px;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .task-item:last-child {
            border-bottom: none;
        }
        
        .task-item:hover {
            background: var(--light);
        }
        
        .task-checkbox {
            margin-right: 15px;
        }
        
        .task-content {
            flex: 1;
        }
        
        .task-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .task-meta {
            font-size: 13px;
            color: var(--gray);
            display: flex;
        }
        
        .task-meta div {
            margin-right: 15px;
        }
        
        .task-priority {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .priority-high {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger);
        }
        
        .priority-medium {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning);
        }
        
        .priority-low {
            background: rgba(46, 204, 113, 0.1);
            color: var(--success);
        }
        
        /* 日历样式 */
        .calendar {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
        }
        
        .calendar-header {
            grid-column: 1 / -1;
            text-align: center;
            padding: 10px;
            font-weight: 500;
            color: var(--dark);
            border-bottom: 1px solid var(--border);
        }
        
        .calendar-day {
            text-align: center;
            padding: 10px 5px;
            font-size: 12px;
            color: var(--gray);
        }
        
        .calendar-date {
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
        }
        
        .calendar-date:hover {
            background: var(--light);
        }
        
        .calendar-date.today {
            background: var(--primary);
            color: white;
        }
        
        .calendar-date.event {
            position: relative;
        }
        
        .calendar-date.event::after {
            content: "";
            position: absolute;
            bottom: 3px;
            left: 50%;
            transform: translateX(-50%);
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background: var(--danger);
        }
        
        /* 公告样式 */
        .announcement {
            padding: 15px;
            border-bottom: 1px solid var(--border);
        }
        
        .announcement:last-child {
            border-bottom: none;
        }
        
        .announcement-title {
            font-weight: 500;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        
        .announcement-title i {
            color: var(--warning);
            margin-right: 8px;
        }
        
        .announcement-meta {
            font-size: 12px;
            color: var(--gray);
            margin-bottom: 10px;
        }
        
        .announcement-content {
            font-size: 14px;
        }
        
        /* 任务中心标签页 */
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border);
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 12px 20px;
            cursor: pointer;
            font-weight: 500;
            color: var(--gray);
            position: relative;
        }
        
        .tab.active {
            color: var(--primary);
        }
        
        .tab.active::after {
            content: "";
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--primary);
        }
        
        .tab-badge {
            margin-left: 8px;
            background: var(--danger);
            color: white;
            border-radius: 10px;
            padding: 1px 6px;
            font-size: 11px;
            font-weight: normal;
        }
        
        /* 筛选区域 */
        .filter-bar {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .filter-group {
            display: flex;
            align-items: center;
        }
        
        .filter-group label {
            margin-right: 8px;
            font-size: 14px;
            color: var(--gray);
        }
        
        select, input {
            padding: 8px 12px;
            border: 1px solid var(--border);
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: var(--primary);
            color: white;
        }
        
        .btn-outline {
            background: none;
            border: 1px solid var(--border);
            color: var(--gray);
        }
        
        /* 任务详情抽屉 */
        .drawer {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 500px;
            background: white;
            box-shadow: -3px 0 15px rgba(0,0,0,0.1);
            z-index: 2000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        
        .drawer.open {
            transform: translateX(0);
        }
        
        .drawer-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .drawer-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }
        
        .drawer-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 14px;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border);
            border-radius: 4px;
            font-size: 14px;
        }
        
        .timeline {
            list-style: none;
            position: relative;
            padding-left: 20px;
            margin-left: 10px;
        }
        
        .timeline::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--light-gray);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
            padding-left: 20px;
        }
        
        .timeline-item::before {
            content: "";
            position: absolute;
            left: -10px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--primary);
            border: 2px solid white;
        }
        
        .timeline-time {
            font-size: 12px;
            color: var(--gray);
            margin-bottom: 5px;
        }
        
        .timeline-content {
            background: var(--light);
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1500;
            display: none;
        }
        
        .overlay.active {
            display: block;
        }

        /* 表格样式 */
        .table-wrapper {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th, .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border);
            font-size: 14px;
        }

        .data-table th {
            background: var(--light);
            font-weight: 600;
            color: var(--dark);
        }

        .data-table tbody tr:hover {
            background: var(--light-gray);
        }

        .data-table .action-buttons .btn {
            margin-right: 5px;
            padding: 5px 10px;
            font-size: 13px;
        }
        .btn-danger-outline {
            background: transparent;
            border: 1px solid var(--danger);
            color: var(--danger);
        }
        .btn-danger-outline:hover {
            background: var(--danger);
            color: white;
        }

        .status-badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            display: inline-block;
        }

        .status-pending { background-color: rgba(243, 156, 18, 0.1); color: var(--warning); }
        .status-approved { background-color: rgba(46, 204, 113, 0.1); color: var(--success); }
        .status-rejected { background-color: rgba(231, 76, 60, 0.1); color: var(--danger); }

        /* 订单状态 */
        .status-paid { background-color: rgba(46, 204, 113, 0.1); color: var(--success); }
        .status-unpaid { background-color: rgba(243, 156, 18, 0.1); color: var(--warning); }
        .status-refunded { background-color: rgba(108, 117, 125, 0.1); color: var(--gray); }
        .status-fulfilling { background-color: rgba(52, 152, 219, 0.1); color: var(--primary); }
        .status-completed { background-color: rgba(46, 204, 113, 0.1); color: var(--success); }
        .status-closed { background-color: rgba(108, 117, 125, 0.1); color: var(--gray); }

        .btn-view {
            background-color: #17a2b8;
            color: white;
        }

        .btn-view:hover {
            background-color: #138496;
        }

        .status-pending {
            background-color: #ffc107;
            color: #212529;
        }
        
        #details-drawer .drawer-content {
            padding: 20px;
        }

        .talent-portrait-section {
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }

        .talent-portrait-section h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #333;
            font-size: 16px;
            border-left: 3px solid #007bff;
            padding-left: 10px;
        }

        .portrait-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .portrait-grid p {
            margin: 0;
            padding: 5px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        .certificates-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .cert-item {
            text-align: center;
        }

        .cert-item img {
            width: 120px;
            height: 85px;
            border-radius: 4px;
            object-fit: cover;
            border: 1px solid #ddd;
        }

        .cert-item p {
            margin-top: 5px;
            font-size: 14px;
        }

        .history-list .history-item,
        .reviews-list .review-item {
            background-color: #f9f9f9;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        }
        .history-list .history-item strong,
        .reviews-list .review-item strong {
            color: #007bff;
        }

        /* 标签样式 */
        .tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
        }

        .tag.tag-blue {
            background-color: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }

        .tag.tag-green {
            background-color: rgba(46, 204, 113, 0.1);
            color: #2ecc71;
        }

        .tag.tag-orange {
            background-color: rgba(243, 156, 18, 0.1);
            color: #f39c12;
        }

        .tag.tag-purple {
            background-color: rgba(155, 89, 182, 0.1);
            color: #9b59b6;
        }

        .tag.tag-gray {
            background-color: rgba(108, 117, 125, 0.1);
            color: #6c757d;
        }

        /* 备注信息样式 */
        .remark-content {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
        }

        .remark-content:empty::before {
            content: "暂无备注信息";
            color: #999;
            font-style: italic;
        }

    </style>
</head>
<body>
    <!-- 侧边导航 -->
    <div class="sidebar">
        <div class="logo">
            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48cmVjdCB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgZmlsbD0id2hpdGUiIHJ4PSIxMiIvPjxwYXRoIGQ9Ik0xMjggMzJMMTUyIDk2TDIyNCA5NkwxNjAgMTQ0TDE4NCAyMjRMMTI4IDE3Nkw3MiAyMjRMOTYgMTQ0TDMyIDk2TDEwNCA5NkwxMjggMzJaIiBmaWxsPSIjMzQ5OGRiIi8+PC9zdmc+" alt="汇成平台">
            <h1>汇成人力资源</h1>
        </div>
        
        <div class="nav-section">
            <h3>核心功能</h3>
            <a href="#" class="nav-link active">
                <i class="fas fa-home"></i> 工作台
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-tasks"></i> 任务中心
                <span class="badge">15</span>
            </a>
            <a href="#" class="nav-link" data-target="under-construction">
                <i class="fas fa-user-friends"></i> OneID人才库
            </a>
        </div>
        
        <div class="nav-section">
            <h3>公共能力</h3>
            <a href="#" class="nav-link" data-target="under-construction">
                <i class="fas fa-bullseye"></i> 商机中心
            </a>
            <a href="#" class="nav-link" data-target="lead-center">
                <i class="fas fa-filter"></i> 线索中心
                <span class="badge">8</span>
            </a>
            <a href="#" class="nav-link" data-target="under-construction">
                <i class="fas fa-file-invoice"></i> 订单中心
            </a>
            <a href="#" class="nav-link" data-target="under-construction">
                <i class="fas fa-box-open"></i> 资源中心
            </a>
        </div>
        
        <div class="nav-section">
            <h3>业务模块</h3>
            <a href="#" class="nav-link" data-target="under-construction">
                <i class="fas fa-graduation-cap"></i> 高校实践
            </a>
            <a href="#" class="nav-link" data-target="under-construction">
                <i class="fas fa-chalkboard-teacher"></i> 培训管理
            </a>
            <a href="#" class="nav-link" data-target="under-construction">
                <i class="fas fa-user-tie"></i> 就业服务
            </a>
            <a href="#" class="nav-link" data-target="under-construction">
                <i class="fas fa-clock"></i> 兼职零工
            </a>
        </div>
        
        <div class="nav-section">
            <h3>系统管理</h3>
            <a href="#" class="nav-link" data-target="under-construction">
                <i class="fas fa-users-cog"></i> 用户管理
            </a>
            <a href="#" class="nav-link" data-target="under-construction">
                <i class="fas fa-shield-alt"></i> 角色权限
            </a>
            <a href="#" class="nav-link" data-target="under-construction">
                <i class="fas fa-cog"></i> 系统设置
            </a>
        </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="topbar">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="搜索...">
            </div>
            
            <div class="user-actions">
                <div class="notification">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </div>
                <div class="notification">
                    <i class="fas fa-envelope"></i>
                    <span class="notification-badge">5</span>
                </div>
                <div class="user-profile">
                    <div class="user-avatar">张</div>
                    <div>
                        <div>张三</div>
                        <div style="font-size: 12px; color: var(--gray);">管理员</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 工作台内容 -->
        <div id="dashboard" class="dashboard">
            <div class="dashboard-header">
                <h2><i class="fas fa-home"></i> 个人工作台</h2>
                <div class="date-display">
                    <i class="fas fa-calendar-alt"></i> 2024年6月19日 星期三 下午15:00
                </div>
            </div>
            
            <!-- 第一行：KPI指标和快速入口 -->
            <div class="grid">
                <div class="card">
                    <div class="card-header">
                        <h3>核心指标</h3>
                    </div>
                    <div class="card-body">
                        <div class="grid" style="grid-template-columns: repeat(4, 1fr);">
                            <div class="kpi-card">
                                <div class="kpi-label">今日新增线索</div>
                                <div class="kpi-value">24</div>
                                <div class="kpi-trend">
                                    <i class="fas fa-arrow-up"></i> 12% 较昨日
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-label">待跟进商机</div>
                                <div class="kpi-value">8</div>
                                <div class="kpi-trend down">
                                    <i class="fas fa-arrow-down"></i> 3% 较昨日
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-label">待处理订单</div>
                                <div class="kpi-value">16</div>
                                <div class="kpi-trend">
                                    <i class="fas fa-arrow-up"></i> 8% 较昨日
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-label">人才库数量</div>
                                <div class="kpi-value">12,534</div>
                                <div class="kpi-trend">
                                    <i class="fas fa-arrow-up"></i> 1.5% 较上周
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3>快速入口</h3>
                    </div>
                    <div class="card-body">
                        <div class="quick-access">
                            <div class="quick-item">
                                <i class="fas fa-plus-circle"></i>
                                <span>新建订单</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-book"></i>
                                <span>发布课程</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-users"></i>
                                <span>客户管理</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-file-contract"></i>
                                <span>合同管理</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-chart-line"></i>
                                <span>数据报告</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-user-plus"></i>
                                <span>新建用户</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-tools"></i>
                                <span>系统设置</span>
                            </div>
                            <div class="quick-item">
                                <i class="fas fa-question-circle"></i>
                                <span>帮助中心</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 第二行：任务和日历 -->
            <div class="grid">
                <div class="card" style="grid-column: span 2;">
                    <div class="card-header">
                        <h3>我的待办</h3>
                        <div class="card-actions">
                            <button>查看全部 <i class="fas fa-arrow-right"></i></button>
                        </div>
                    </div>
                    <div class="card-body">
                        <ul class="task-list">
                            <li class="task-item">
                                <div class="task-checkbox">
                                    <input type="checkbox">
                                </div>
                                <div class="task-content">
                                    <div class="task-title">审批 XX大学实践项目合作协议</div>
                                    <div class="task-meta">
                                        <div><i class="far fa-user"></i> 李四</div>
                                        <div><i class="far fa-clock"></i> 今天 16:00前</div>
                                        <div><i class="fas fa-tag"></i> 高校实践</div>
                                    </div>
                                </div>
                                <span class="task-priority priority-high">高优先级</span>
                            </li>
                            <li class="task-item">
                                <div class="task-checkbox">
                                    <input type="checkbox">
                                </div>
                                <div class="task-content">
                                    <div class="task-title">处理李女士的月嫂服务投诉</div>
                                    <div class="task-meta">
                                        <div><i class="far fa-user"></i> 王五</div>
                                        <div><i class="far fa-clock"></i> 今天 17:30前</div>
                                        <div><i class="fas fa-tag"></i> 家政服务</div>
                                    </div>
                                </div>
                                <span class="task-priority priority-high">高优先级</span>
                            </li>
                            <li class="task-item">
                                <div class="task-checkbox">
                                    <input type="checkbox">
                                </div>
                                <div class="task-content">
                                    <div class="task-title">审核6月金牌月嫂认证考试结果</div>
                                    <div class="task-meta">
                                        <div><i class="far fa-user"></i> 系统自动</div>
                                        <div><i class="far fa-clock"></i> 明天 10:00前</div>
                                        <div><i class="fas fa-tag"></i> 培训认证</div>
                                    </div>
                                </div>
                                <span class="task-priority priority-medium">中优先级</span>
                            </li>
                            <li class="task-item">
                                <div class="task-checkbox">
                                    <input type="checkbox">
                                </div>
                                <div class="task-content">
                                    <div class="task-title">确认YY企业培训订单收款</div>
                                    <div class="task-meta">
                                        <div><i class="far fa-user"></i> 赵六</div>
                                        <div><i class="far fa-clock"></i> 明天 12:00前</div>
                                        <div><i class="fas fa-tag"></i> 企业培训</div>
                                    </div>
                                </div>
                                <span class="task-priority priority-medium">中优先级</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3>日程安排</h3>
                    </div>
                    <div class="card-body">
                        <div class="calendar">
                            <div class="calendar-header">2024年6月</div>
                            <div class="calendar-day">日</div>
                            <div class="calendar-day">一</div>
                            <div class="calendar-day">二</div>
                            <div class="calendar-day">三</div>
                            <div class="calendar-day">四</div>
                            <div class="calendar-day">五</div>
                            <div class="calendar-day">六</div>
                            
                            <!-- 日历日期 -->
                            <div class="calendar-date"></div>
                            <div class="calendar-date"></div>
                            <div class="calendar-date"></div>
                            <div class="calendar-date"></div>
                            <div class="calendar-date">1</div>
                            <div class="calendar-date">2</div>
                            <div class="calendar-date">3</div>
                            
                            <div class="calendar-date">4</div>
                            <div class="calendar-date">5</div>
                            <div class="calendar-date">6</div>
                            <div class="calendar-date">7</div>
                            <div class="calendar-date">8</div>
                            <div class="calendar-date">9</div>
                            <div class="calendar-date">10</div>
                            
                            <div class="calendar-date">11</div>
                            <div class="calendar-date">12</div>
                            <div class="calendar-date event">13</div>
                            <div class="calendar-date">14</div>
                            <div class="calendar-date">15</div>
                            <div class="calendar-date">16</div>
                            <div class="calendar-date">17</div>
                            
                            <div class="calendar-date">18</div>
                            <div class="calendar-date today">19</div>
                            <div class="calendar-date">20</div>
                            <div class="calendar-date event">21</div>
                            <div class="calendar-date event">22</div>
                            <div class="calendar-date">23</div>
                            <div class="calendar-date">24</div>
                            
                            <div class="calendar-date">25</div>
                            <div class="calendar-date">26</div>
                            <div class="calendar-date">27</div>
                            <div class="calendar-date">28</div>
                            <div class="calendar-date">29</div>
                            <div class="calendar-date">30</div>
                            <div class="calendar-date"></div>
                        </div>
                        
                        <div style="margin-top: 20px;">
                            <div style="font-weight: 500; margin-bottom: 10px;">今日安排</div>
                            <ul style="list-style: none; font-size: 14px;">
                                <li style="padding: 5px 0; display: flex; align-items: center;">
                                    <i class="fas fa-circle" style="color: #3498db; font-size: 8px; margin-right: 10px;"></i>
                                    <div>
                                        <div>15:30 高校实践项目评审会</div>
                                        <div style="font-size: 12px; color: var(--gray);">3号楼201会议室</div>
                                    </div>
                                </li>
                                <li style="padding: 5px 0; display: flex; align-items: center;">
                                    <i class="fas fa-circle" style="color: #e74c3c; font-size: 8px; margin-right: 10px;"></i>
                                    <div>
                                        <div>16:40 月度运营数据分析报告</div>
                                        <div style="font-size: 12px; color: var(--gray);">线上会议</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 第三行：公告 -->
            <div class="card">
                <div class="card-header">
                    <h3>系统公告</h3>
                </div>
                <div class="card-body">
                    <div class="announcement">
                        <div class="announcement-title">
                            <i class="fas fa-bullhorn"></i> 系统维护通知
                        </div>
                        <div class="announcement-meta">
                            2024年6月18日 发布人：系统管理员
                        </div>
                        <div class="announcement-content">
                            平台将于2024年6月22日(周六)凌晨2:00-4:00进行系统维护，届时所有服务将暂停使用。请提前做好工作安排，给您带来不便敬请谅解。
                        </div>
                    </div>
                    
                    <div class="announcement">
                        <div class="announcement-title">
                            <i class="fas fa-gift"></i> 端午节放假通知
                        </div>
                        <div class="announcement-meta">
                            2024年6月17日 发布人：行政部
                        </div>
                        <div class="announcement-content">
                            根据国家法定节假日安排，公司端午节放假时间为2024年6月22日(周六)至6月24日(周一)，共3天。6月25日(周二)正常上班。节假日期间，请各部门安排好值班人员。
                        </div>
                    </div>
                    
                    <div class="announcement">
                        <div class="announcement-title">
                            <i class="fas fa-star"></i> OneID人才库功能升级
                        </div>
                        <div class="announcement-meta">
                            2024年6月15日 发布人：产品部
                        </div>
                        <div class="announcement-content">
                            人才库中枢(OneID)已完成重大升级，新增跨场景用户识别功能，支持自动合并同一用户在不同业务场景中的数据。详细更新内容请查看产品更新日志。
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 商机中心内容 -->
        <div id="opportunity-center" class="dashboard" style="display: none;">
            <div class="dashboard-header">
                <h2><i class="fas fa-bullseye"></i> 商机管理</h2>
                <button class="btn btn-primary" id="add-opportunity-btn"><i class="fas fa-plus"></i> 新建商机</button>
            </div>

            <div class="filter-bar">
                <div class="filter-group">
                    <label for="filter-stage">销售阶段:</label>
                    <select id="filter-stage">
                        <option value="">全部</option>
                        <option>初步接洽</option>
                        <option>需求分析</option>
                        <option>方案报价</option>
                        <option>商务谈判</option>
                        <option>赢单</option>
                        <option>输单</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="filter-owner">销售负责人:</label>
                    <select id="filter-owner">
                        <option value="">全部</option>
                        <option>张三</option>
                        <option>李四</option>
                        <option>王五</option>
                    </select>
                </div>
                <div class="filter-group">
                    <input type="text" placeholder="搜索商机名称..." id="filter-name">
                </div>
                <button class="btn btn-primary">筛选</button>
                <button class="btn btn-outline">重置</button>
            </div>

            <div class="card">
                <div class="card-body" style="padding: 0;">
                    <div class="table-wrapper">
                        <table class="data-table" id="opportunity-table">
                            <thead>
                                <tr>
                                    <th>商机编号</th>
                                    <th>商机名称</th>
                                    <th>关联客户</th>
                                    <th>商机金额(元)</th>
                                    <th>销售阶段</th>
                                    <th>审批状态</th>
                                    <th>销售负责人</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 线索中心内容 -->
        <div id="lead-center" class="dashboard" style="display: none;">
            <div class="dashboard-header">
                <h2><i class="fas fa-filter"></i> 线索管理</h2>
                <button class="btn btn-primary" id="add-lead-btn"><i class="fas fa-plus"></i> 新建线索</button>
            </div>

            <div class="filter-bar">
                <div class="filter-group">
                    <label for="lead-filter-source">线索来源:</label>
                    <select id="lead-filter-source">
                        <option value="">全部</option>
                        <option>官网注册</option>
                        <option>市场活动</option>
                        <option>公众号文章</option>
                        <option>视频号</option>
                        <option>抖音</option>
                        <option>入驻商家</option>
                        <option>小红书</option>
                        <option>微博</option>
                        <option>知乎</option>
                        <option>百度推广</option>
                        <option>微信朋友圈</option>
                        <option>QQ群/微信群</option>
                        <option>线下展会</option>
                        <option>合作伙伴推荐</option>
                        <option>老客户推荐</option>
                        <option>电话营销</option>
                        <option>短信营销</option>
                        <option>邮件营销</option>
                        <option>其他</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="lead-filter-status">线索状态:</label>
                    <select id="lead-filter-status">
                        <option value="">全部</option>
                        <option>未处理</option>
                        <option>跟进中</option>
                        <option>已转化</option>
                        <option>无意向</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="lead-filter-business-module">业务模块:</label>
                    <select id="lead-filter-business-module">
                        <option value="">全部</option>
                        <option>高校业务</option>
                        <option>家政业务</option>
                        <option>培训业务</option>
                        <option>认证业务</option>
                    </select>
                </div>
                <div class="filter-group">
                    <input type="text" placeholder="搜索客户姓名/电话..." id="lead-filter-name">
                </div>
                <button class="btn btn-primary">筛选</button>
                <button class="btn btn-outline">重置</button>
            </div>

            <div class="card">
                <div class="card-body" style="padding: 0;">
                    <div class="table-wrapper">
                        <table class="data-table" id="lead-table">
                            <thead>
                                <tr>
                                    <th>线索ID</th>
                                    <th>客户姓名</th>
                                    <th>联系电话</th>
                                    <th>线索来源</th>
                                    <th>业务模块</th>
                                    <th>线索状态</th>
                                    <th>创建方式</th>
                                    <th>创建人</th>
                                    <th>当前跟进人</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单中心内容 -->
        <div id="order-center" class="dashboard" style="display: none;">
            <div class="dashboard-header">
                <h2><i class="fas fa-file-invoice"></i> 订单管理</h2>
                <button class="btn btn-primary" id="add-order-btn"><i class="fas fa-plus"></i> 新建订单</button>
            </div>

            <div class="filter-bar">
                <div class="filter-group">
                    <label for="order-filter-type">订单类型:</label>
                    <select id="order-filter-type">
                        <option value="">全部</option>
                        <option>高校实践</option>
                        <option>企业培训</option>
                        <option>个人报名</option>
                        <option>家政服务</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="order-filter-status">订单状态:</label>
                    <select id="order-filter-status">
                        <option value="">全部</option>
                        <option>待支付</option>
                        <option>待履约</option>
                        <option>已完成</option>
                        <option>已关闭</option>
                    </select>
                </div>
                <div class="filter-group">
                    <input type="text" placeholder="搜索订单号/客户..." id="order-filter-keyword">
                </div>
                <button class="btn btn-primary">筛选</button>
                <button class="btn btn-outline">重置</button>
            </div>

            <div class="card">
                <div class="card-body" style="padding: 0;">
                    <div class="table-wrapper">
                        <table class="data-table" id="order-table">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>订单类型</th>
                                    <th>关联客户/学员</th>
                                    <th>服务名称</th>
                                    <th>订单金额(元)</th>
                                    <th>订单状态</th>
                                    <th>支付状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 资源中心内容 -->
        <div id="resource-center" class="dashboard" style="display: none;">
            <div class="dashboard-header">
                <h2><i class="fas fa-box-open"></i> 资源中心</h2>
                 <div class="card-actions">
                    <button id="add-resource-btn" class="btn btn-primary"><i class="fas fa-plus"></i> 新增资源</button>
                </div>
            </div>
            <div class="card">
                <div class="card-body" style="padding: 0;">
                    <div class="tabs" id="resource-tabs">
                        <div class="tab active" data-tab="partners">合作伙伴</div>
                        <div class="tab" data-tab="teachers">统一师资库</div>
                        <div class="tab" data-tab="aunties">统一阿姨库</div>
                        <div class="tab" data-tab="courses">数字资产 (课程)</div>
                    </div>

                    <!-- 合作伙伴 Tab -->
                    <div id="tab-partners" class="tab-content" style="padding: 20px;">
                        <div class="filter-bar" style="box-shadow: none; padding: 0 0 20px 0;">
                            <div class="filter-group">
                                <label for="partner-filter-type">机构类型:</label>
                                <select id="partner-filter-type">
                                    <option value="">全部</option>
                                    <option>高校</option>
                                    <option>企业</option>
                                    <option>家政机构</option>
                                    <option>供应商</option>
                                    <option>渠道合作方</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="partner-filter-status">合作状态:</label>
                                <select id="partner-filter-status">
                                    <option value="">全部</option>
                                    <option>合作中</option>
                                    <option>待审核</option>
                                    <option>已终止</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <input type="text" placeholder="搜索机构名称..." id="partner-filter-name">
                            </div>
                            <button class="btn btn-primary">筛选</button>
                        </div>
                        <div class="table-wrapper">
                            <table class="data-table" id="partners-table">
                                <thead>
                                    <tr>
                                        <th>合作伙伴ID</th>
                                        <th>机构名称</th>
                                        <th>机构类型</th>
                                        <th>合作状态</th>
                                        <th>风险等级</th>
                                        <th>我方负责人</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 师资库 Tab -->
                    <div id="tab-teachers" class="tab-content" style="display:none; padding: 20px;">
                        <div class="filter-bar" style="box-shadow: none; padding: 0 0 20px 0;">
                            <div class="filter-group">
                                <label for="teacher-filter-type">讲师类型:</label>
                                <select id="teacher-filter-type">
                                    <option value="">全部</option>
                                    <option>内部讲师</option>
                                    <option>外部讲师</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="teacher-filter-status">合作状态:</label>
                                <select id="teacher-filter-status">
                                    <option value="">全部</option>
                                    <option>合作中</option>
                                    <option>待签约</option>
                                    <option>已解约</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <input type="text" placeholder="搜索姓名/擅长领域..." id="teacher-filter-keyword">
                            </div>
                            <button class="btn btn-primary">筛选</button>
                        </div>
                         <div class="table-wrapper">
                            <table class="data-table" id="teachers-table">
                                <thead>
                                    <tr>
                                        <th>讲师ID</th>
                                        <th>讲师姓名</th>
                                        <th>讲师类型</th>
                                        <th>擅长领域</th>
                                        <th>联系电话</th>
                                        <th>合作状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 阿姨库 Tab -->
                    <div id="tab-aunties" class="tab-content" style="display:none; padding: 20px;">
                        <div class="filter-bar" style="box-shadow: none; padding: 0 0 20px 0;">
                            <div class="filter-group">
                                <label for="auntie-filter-level">阿姨等级:</label>
                                <select id="auntie-filter-level">
                                    <option value="">全部</option>
                                    <option>普通</option>
                                    <option>中级</option>
                                    <option>高级</option>
                                    <option>金牌</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="auntie-filter-servicetype">服务类型:</label>
                                <select id="auntie-filter-servicetype">
                                    <option value="">全部</option>
                                    <option>月嫂</option>
                                    <option>育儿嫂</option>
                                    <option>保洁师</option>
                                    <option>综合家务</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="auntie-filter-status">服务状态:</label>
                                <select id="auntie-filter-status">
                                    <option value="">全部</option>
                                    <option>服务中</option>
                                    <option>待岗</option>
                                    <option>培训中</option>
                                    <option>休假中</option>
                                    <option>已解约</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <input type="text" placeholder="搜索阿姨姓名..." id="auntie-filter-keyword">
                            </div>
                            <button class="btn btn-primary">筛选</button>
                        </div>
                         <div class="table-wrapper">
                            <table class="data-table" id="aunties-table">
                                <thead>
                                    <tr>
                                        <th>阿姨ID</th>
                                        <th>姓名</th>
                                        <th>等级</th>
                                        <th>工作年限</th>
                                        <th>主要服务</th>
                                        <th>审核状态</th>
                                        <th>服务状态</th>
                                        <th>证书状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 数字资产 Tab -->
                    <div id="tab-courses" class="tab-content" style="display:none; padding: 20px;">
                        <div class="filter-bar" style="box-shadow: none; padding: 0 0 20px 0;">
                            <div class="filter-group">
                                <label for="course-filter-category">课程分类:</label>
                                <select id="course-filter-category">
                                    <option value="">全部</option>
                                    <option>家政技能</option>
                                    <option>职业素养</option>
                                    <option>高校实践</option>
                                    <option>企业管理</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="course-filter-delivery">授课形式:</label>
                                <select id="course-filter-delivery">
                                    <option value="">全部</option>
                                    <option>线上录播</option>
                                    <option>线上直播</option>
                                    <option>线下授课</option>
                                    <option>混合模式</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="course-filter-status">课程状态:</label>
                                <select id="course-filter-status">
                                    <option value="">全部</option>
                                    <option>已上架</option>
                                    <option>待上架</option>
                                    <option>已下架</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <input type="text" placeholder="搜索课程名称..." id="course-filter-keyword">
                            </div>
                            <button class="btn btn-primary">筛选</button>
                        </div>
                         <div class="table-wrapper">
                            <table class="data-table" id="courses-table">
                                <thead>
                                    <tr>
                                        <th>课程ID</th>
                                        <th>课程名称</th>
                                        <th>课程分类</th>
                                        <th>授课形式</th>
                                        <th>价格(元)</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 建设中页面 -->
        <div id="under-construction" class="dashboard" style="display: none;">
            <div class="dashboard-header">
                <h2><i class="fas fa-tools"></i> 功能建设中</h2>
            </div>
            <div class="card" style="text-align: center; padding: 60px 20px;">
                <div style="font-size: 80px; color: var(--gray); margin-bottom: 20px;">
                    <i class="fas fa-hammer"></i>
                </div>
                <h3 style="color: var(--dark); margin-bottom: 15px;">功能正在加紧建设中</h3>
                <p style="color: var(--gray); font-size: 16px; line-height: 1.6; max-width: 500px; margin: 0 auto;">
                    该功能模块正在开发中，我们将尽快为您提供完整的服务体验。<br>
                    感谢您的耐心等待！
                </p>
                <div style="margin-top: 30px;">
                    <button class="btn btn-primary" onclick="switchView('lead-center')">
                        <i class="fas fa-arrow-left"></i> 返回线索管理
                    </button>
                </div>
            </div>
        </div>

        <!-- 高校实践模块内容 -->
        <div id="practice-center" class="dashboard" style="display: none;">
            <!-- 视图容器 -->
        </div>

    </div>

    <!-- 新增/编辑商机 抽屉 -->
    <div id="opportunity-drawer" class="drawer">
        <div class="drawer-header">
            <h3 id="drawer-title">新建商机</h3>
            <button style="background:none; border:none; font-size: 20px; cursor: pointer;" id="close-drawer-btn">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="opportunity-form">
                <input type="hidden" id="opportunity-id">
                <div class="form-group">
                    <label for="opportunity-name">商机名称</label>
                    <input type="text" id="opportunity-name" class="form-control" placeholder="例如: XX大学2024年秋季实践项目" required>
                </div>
                <div class="form-group">
                    <label for="opportunity-client">关联客户</label>
                    <input type="text" id="opportunity-client" class="form-control" placeholder="必填, 请从资源库选择" required>
                </div>
                <div class="form-group">
                    <label for="opportunity-amount">预计成交金额(元)</label>
                    <input type="number" id="opportunity-amount" class="form-control" placeholder="必填, 必须为正数" required>
                </div>
                <div class="form-group">
                    <label for="opportunity-date">预计成交日期</label>
                    <input type="date" id="opportunity-date" class="form-control">
                </div>
                <div class="form-group">
                    <label for="opportunity-stage">销售阶段</label>
                    <select id="opportunity-stage" class="form-control">
                        <option>初步接洽</option>
                        <option>需求分析</option>
                        <option>方案报价</option>
                        <option>商务谈判</option>
                        <option>赢单</option>
                        <option>输单</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="opportunity-owner">销售负责人</label>
                     <select id="opportunity-owner" class="form-control">
                        <option>张三</option>
                        <option>李四</option>
                        <option>王五</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="opportunity-description">商机描述</label>
                    <textarea id="opportunity-description" rows="4" class="form-control"></textarea>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" id="cancel-btn">取消</button>
            <button class="btn btn-primary" id="save-btn">保存</button>
        </div>
    </div>

    <!-- 新增/编辑线索 抽屉 -->
    <div id="lead-drawer" class="drawer">
        <div class="drawer-header">
            <h3 id="lead-drawer-title">新建线索</h3>
            <button style="background:none; border:none; font-size: 20px; cursor: pointer;" id="close-lead-drawer-btn">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="lead-form">
                <input type="hidden" id="lead-id">
                <div class="form-group">
                    <label for="lead-name">客户姓名</label>
                    <input type="text" id="lead-name" class="form-control" placeholder="客户的姓名" required>
                </div>
                <div class="form-group">
                    <label for="lead-phone">联系电话</label>
                    <input type="tel" id="lead-phone" class="form-control" placeholder="必填, 11位手机号" required>
                </div>
                <div class="form-group">
                    <label for="lead-source">线索来源</label>
                    <select id="lead-source" class="form-control">
                        <option>官网注册</option>
                        <option>市场活动</option>
                        <option>公众号文章</option>
                        <option>视频号</option>
                        <option>抖音</option>
                        <option>入驻商家</option>
                        <option>小红书</option>
                        <option>微博</option>
                        <option>知乎</option>
                        <option>百度推广</option>
                        <option>微信朋友圈</option>
                        <option>QQ群/微信群</option>
                        <option>线下展会</option>
                        <option>合作伙伴推荐</option>
                        <option>老客户推荐</option>
                        <option>电话营销</option>
                        <option>短信营销</option>
                        <option>邮件营销</option>
                        <option>其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="lead-business-module">业务模块</label>
                    <select id="lead-business-module" class="form-control" required>
                        <option value="">请选择业务模块</option>
                        <option value="高校业务">高校业务</option>
                        <option value="家政业务">家政业务</option>
                        <option value="培训业务">培训业务</option>
                        <option value="认证业务">认证业务</option>
                    </select>
                </div>
                 <div class="form-group">
                    <label for="lead-status">线索状态</label>
                    <select id="lead-status" class="form-control">
                        <option>未处理</option>
                        <option>跟进中</option>
                        <option>已转化</option>
                        <option>无意向</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="lead-owner">当前跟进人</label>
                     <select id="lead-owner" class="form-control">
                        <option>未分配</option>
                        <option>张三</option>
                        <option>李四</option>
                        <option>王五</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="lead-remark">备注信息</label>
                    <textarea id="lead-remark" class="form-control" rows="4" maxlength="500" placeholder="请输入对此条线索的详细说明，如客户需求、沟通记录、特殊要求等"></textarea>
                    <div style="text-align: right; margin-top: 5px; font-size: 12px; color: #666;">
                        <span id="remark-char-count">0</span>/500
                    </div>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" id="cancel-lead-btn">取消</button>
            <button class="btn btn-primary" id="save-lead-btn">保存</button>
        </div>
    </div>
    
    <!-- 新增/编辑订单 抽屉 -->
    <div id="order-drawer" class="drawer">
        <div class="drawer-header">
            <h3 id="order-drawer-title">新建订单</h3>
            <button style="background:none; border:none; font-size: 20px; cursor: pointer;" id="close-order-drawer-btn">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="order-form">
                <input type="hidden" id="order-id">
                <div class="form-group">
                    <label for="order-type">订单类型</label>
                    <select id="order-type" class="form-control">
                        <option>高校实践</option>
                        <option>企业培训</option>
                        <option>个人报名</option>
                        <option>家政服务</option>
                        <option>考试认证</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="order-client">关联客户/学员</label>
                    <input type="text" id="order-client" class="form-control" placeholder="必填, 请输入客户或学员名称" required>
                </div>
                <div class="form-group">
                    <label for="order-service">商品/服务名称</label>
                    <input type="text" id="order-service" class="form-control" placeholder="必填, 订单核心内容摘要" required>
                </div>
                <div class="form-group">
                    <label for="order-amount">订单金额(元)</label>
                    <input type="number" id="order-amount" class="form-control" placeholder="必填, 必须为正数" required>
                </div>
                <div class="form-group">
                    <label for="order-status">订单状态</label>
                    <select id="order-status" class="form-control">
                        <option>待支付</option>
                        <option>待履约</option>
                        <option>已完成</option>
                        <option>已关闭</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="order-payment-status">支付状态</label>
                    <select id="order-payment-status" class="form-control">
                        <option>未支付</option>
                        <option>已支付</option>
                        <option>部分支付</option>
                        <option>已退款</option>
                    </select>
                </div>

                <!-- Type-specific fields container -->
                <div id="order-type-specific-fields">
                    <!-- 家政服务字段 -->
                    <div class="specific-fields" id="fields-家政服务" style="display: none;">
                         <div class="section-title" style="font-size: 14px; margin-top:20px; padding-bottom: 5px;">家政服务信息</div>
                         <div class="form-group">
                            <label for="order-service-address">服务地址</label>
                            <input type="text" id="order-service-address" class="form-control" placeholder="请输入详细服务地址">
                        </div>
                        <div class="form-group">
                            <label for="order-auntie-info">指派阿姨</label>
                            <input type="text" id="order-auntie-info" class="form-control" placeholder="请输入阿姨姓名/编号">
                        </div>
                        <div class="form-group">
                            <label for="order-service-date">服务开始日期</label>
                            <input type="date" id="order-service-date" class="form-control">
                        </div>
                    </div>

                    <!-- 高校实践字段 -->
                    <div class="specific-fields" id="fields-高校实践" style="display: none;">
                        <div class="section-title" style="font-size: 14px; margin-top:20px; padding-bottom: 5px;">高校实践信息</div>
                        <div class="form-group">
                            <label for="order-school-name">学校名称</label>
                            <input type="text" id="order-school-name" class="form-control" placeholder="请输入学校全称">
                        </div>
                         <div class="form-group">
                            <label for="order-student-count">参与人数</label>
                            <input type="number" id="order-student-count" class="form-control" placeholder="请输入参与学生总人数">
                        </div>
                        <div class="form-group">
                            <label for="order-practice-date">实践开始日期</label>
                            <input type="date" id="order-practice-date" class="form-control">
                        </div>
                    </div>
                    
                    <!-- 企业培训字段 -->
                    <div class="specific-fields" id="fields-企业培训" style="display: none;">
                        <div class="section-title" style="font-size: 14px; margin-top:20px; padding-bottom: 5px;">企业培训信息</div>
                        <div class="form-group">
                            <label for="order-trainer-name">指派讲师</label>
                            <input type="text" id="order-trainer-name" class="form-control" placeholder="请输入培训讲师姓名">
                        </div>
                         <div class="form-group">
                            <label for="order-trainee-count">参与人数</label>
                            <input type="number" id="order-trainee-count" class="form-control" placeholder="请输入参与培训的总人数">
                        </div>
                        <div class="form-group">
                            <label for="order-training-date">培训日期</label>
                            <input type="date" id="order-training-date" class="form-control">
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" id="cancel-order-btn">取消</button>
            <button class="btn btn-primary" id="save-order-btn">保存</button>
        </div>
    </div>

    <!-- 新增/编辑资源 抽屉 (通用) -->
    <div id="resource-drawer" class="drawer">
        <div class="drawer-header">
            <h3 id="resource-drawer-title">新增合作伙伴</h3>
            <button style="background:none; border:none; font-size: 20px; cursor: pointer;" id="close-resource-drawer-btn">&times;</button>
        </div>
        <div class="drawer-body">
            <!-- 合作伙伴表单 -->
            <form id="partner-form">
                <input type="hidden" id="partner-id">
                <div class="form-group">
                    <label for="partner-name">机构名称</label>
                    <input type="text" id="partner-name" class="form-control" placeholder="必填, 机构全称" required>
                </div>
                <div class="form-group">
                    <label for="partner-type">机构类型</label>
                    <select id="partner-type" class="form-control">
                        <option>高校</option>
                        <option>企业</option>
                        <option>家政机构</option>
                        <option>供应商</option>
                        <option>渠道合作方</option>
                    </select>
                </div>
                 <div class="form-group">
                    <label for="partner-status">合作状态</label>
                    <select id="partner-status" class="form-control">
                        <option>待审核</option>
                        <option>合作中</option>
                        <option>已终止</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="partner-risk">风险等级</label>
                    <select id="partner-risk" class="form-control">
                        <option>正常</option>
                        <option>经营异常</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="partner-owner">我方负责人</label>
                     <select id="partner-owner" class="form-control">
                        <option>张三</option>
                        <option>李四</option>
                        <option>王五</option>
                    </select>
                </div>
            </form>

            <!-- 师资库表单 -->
            <form id="teacher-form" style="display: none;">
                <input type="hidden" id="teacher-id">
                <div class="form-group">
                    <label for="teacher-name">讲师姓名</label>
                    <input type="text" id="teacher-name" class="form-control" placeholder="必填" required>
                </div>
                <div class="form-group">
                    <label for="teacher-type">讲师类型</label>
                    <select id="teacher-type" class="form-control">
                        <option>内部讲师</option>
                        <option>外部讲师</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="teacher-field">擅长领域</label>
                    <input type="text" id="teacher-field" class="form-control" placeholder="如：职业规划、计算机科学">
                </div>
                 <div class="form-group">
                    <label for="teacher-phone">联系电话</label>
                    <input type="tel" id="teacher-phone" class="form-control" placeholder="必填">
                </div>
                <div class="form-group">
                    <label for="teacher-status">合作状态</label>
                    <select id="teacher-status" class="form-control">
                        <option>合作中</option>
                        <option>待签约</option>
                        <option>已解约</option>
                    </select>
                </div>
            </form>

            <!-- 阿姨库表单 -->
            <form id="auntie-form" style="display: none;">
                <input type="hidden" id="auntie-id">
                <div class="form-group">
                    <label for="auntie-name">阿姨姓名</label>
                    <input type="text" id="auntie-name" class="form-control" placeholder="必填" required>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="form-group">
                        <label for="auntie-age">年龄</label>
                        <input type="number" id="auntie-age" class="form-control" placeholder="必填">
                    </div>
                    <div class="form-group">
                        <label for="auntie-workYears">工作年限</label>
                        <input type="number" id="auntie-workYears" class="form-control" placeholder="必填">
                    </div>
                    <div class="form-group">
                        <label for="auntie-level">阿姨等级</label>
                        <select id="auntie-level" class="form-control">
                            <option>普通</option>
                            <option>中级</option>
                            <option>高级</option>
                            <option>金牌</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="auntie-service-type">服务类型</label>
                        <select id="auntie-service-type" class="form-control">
                            <option>月嫂</option>
                            <option>育儿嫂</option>
                            <option>保洁师</option>
                            <option>综合家务</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="auntie-status">服务状态</label>
                        <select id="auntie-status" class="form-control">
                            <option>服务中</option>
                            <option>待岗</option>
                            <option>培训中</option>
                            <option>休假中</option>
                            <option>已解约</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="auntie-verificationStatus">审核状态</label>
                        <select id="auntie-verificationStatus" class="form-control">
                            <option>待审核</option>
                            <option>已审核</option>
                        </select>
                    </div>
                     <div class="form-group">
                        <label for="auntie-nativePlace">籍贯</label>
                        <input type="text" id="auntie-nativePlace" class="form-control">
                    </div>
                     <div class="form-group">
                        <label for="auntie-education">学历</label>
                        <select id="auntie-education" class="form-control">
                            <option>小学</option>
                            <option>初中</option>
                            <option>高中</option>
                            <option>大专</option>
                            <option>本科及以上</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="auntie-avatar-upload">头像</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                       <img id="auntie-avatar-preview" src="https://placehold.co/60x60/cccccc/ffffff/png?text=头像" style="width: 60px; height: 60px; border-radius: 4px; object-fit: cover;">
                       <input type="file" id="auntie-avatar-upload" class="form-control" accept="image/*">
                       <input type="hidden" id="auntie-avatar">
                    </div>
                </div>
                <div class="form-group">
                    <label for="auntie-talentPortrait">介绍</label>
                    <textarea id="auntie-talentPortrait" rows="4" class="form-control" placeholder="请输入对阿姨的综合性评价和介绍"></textarea>
                </div>
                 <!-- 证书管理模块 -->
                <div class="section" style="margin-top: 20px;">
                    <h4 class="section-title" style="font-size: 14px; border: none; padding-bottom: 5px;">资质证书管理</h4>
                    <div id="auntie-certificates-editor">
                        <!-- 动态证书编辑器将在这里生成 -->
                    </div>
                    <button type="button" class="btn btn-outline" id="add-new-cert-btn" style="margin-top: 10px; font-size: 12px;"><i class="fas fa-plus"></i> 添加新证书</button>
                </div>
            </form>

            <!-- 数字资产(课程)表单 -->
            <form id="course-form" style="display: none;">
                <input type="hidden" id="course-id">
                <div class="form-group">
                    <label for="course-name">课程名称</label>
                    <input type="text" id="course-name" class="form-control" placeholder="必填" required>
                </div>
                <div class="form-group">
                    <label for="course-category">课程分类</label>
                    <select id="course-category" class="form-control">
                        <option>家政技能</option>
                        <option>职业素养</option>
                        <option>高校实践</option>
                        <option>企业管理</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="course-delivery-method">授课形式</label>
                    <select id="course-delivery-method" class="form-control">
                        <option>线上录播</option>
                        <option>线上直播</option>
                        <option>线下授课</option>
                        <option>混合模式</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="course-teacher">关联讲师</label>
                    <input type="text" id="course-teacher" class="form-control" placeholder="输入讲师姓名">
                </div>
                <div class="form-group">
                    <label for="course-duration">课程时长(小时)</label>
                    <input type="number" id="course-duration" class="form-control" placeholder="请输入数字">
                </div>
                 <div class="form-group">
                    <label for="course-price">课程价格(元)</label>
                    <input type="number" id="course-price" class="form-control" placeholder="0 表示免费">
                </div>
                <div class="form-group">
                    <label for="course-status">课程状态</label>
                    <select id="course-status" class="form-control">
                        <option>已上架</option>
                        <option>待上架</option>
                        <option>已下架</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="course-target-audience">适宜人群</label>
                    <textarea id="course-target-audience" rows="2" class="form-control" placeholder="例如：零基础学员、在职项目经理"></textarea>
                </div>
                 <div class="form-group">
                    <label for="course-syllabus">课程大纲</label>
                    <textarea id="course-syllabus" rows="5" class="form-control" placeholder="请输入课程章节、课时安排等"></textarea>
                </div>
                 <div class="form-group">
                    <label for="course-description">课程详情介绍</label>
                    <textarea id="course-description" rows="5" class="form-control" placeholder="请输入详细的课程介绍信息"></textarea>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" id="cancel-resource-btn">取消</button>
            <button class="btn btn-primary" id="save-resource-btn">保存</button>
        </div>
    </div>

    <!-- 课程详情 抽屉 -->
    <div id="course-details-drawer" class="drawer" style="width: 600px;">
        <div class="drawer-header">
            <h3 id="course-details-drawer-title">课程详情</h3>
            <button style="background:none; border:none; font-size: 20px; cursor: pointer;" id="close-course-details-drawer-btn">&times;</button>
        </div>
        <div class="drawer-body" id="course-details-content">
            <!-- 课程详情动态生成 -->
        </div>
         <div class="drawer-footer">
            <button class="btn btn-outline" id="close-course-details-drawer-btn-2">关闭</button>
        </div>
    </div>

    <!-- 阿姨人才画像 抽屉 -->
    <div id="auntie-details-drawer" class="drawer" style="width: 600px;">
        <div class="drawer-header">
            <h3 id="auntie-details-drawer-title">阿姨介绍</h3>
            <button style="background:none; border:none; font-size: 20px; cursor: pointer;" id="close-auntie-details-drawer-btn">&times;</button>
        </div>
        <div class="drawer-body" id="auntie-details-content">
            <!-- 阿姨详情动态生成 -->
        </div>
         <div class="drawer-footer">
            <button class="btn btn-outline" id="close-auntie-details-drawer-btn-2">关闭</button>
        </div>
    </div>

    <!-- 订单详情 抽屉 -->
    <div id="order-details-drawer" class="drawer" style="width: 600px;">
        <div class="drawer-header">
            <h3 id="order-details-drawer-title">订单详情</h3>
            <button style="background:none; border:none; font-size: 20px; cursor: pointer;" id="close-order-details-drawer-btn">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="section" id="order-details-content">
                <!-- 订单详情动态生成 -->
            </div>
            <div class="section">
                <h4 class="section-title">合同管理</h4>
                <div id="contract-info" style="padding: 10px; background: #f8f9fa; border-radius: 6px;">
                     <!-- 合同信息动态生成 -->
                </div>
            </div>
            <div class="section">
                <h4 class="section-title">跟进记录</h4>
                <ul class="timeline" id="order-log-timeline">
                    <!-- 日志条目动态生成 -->
                </ul>
            </div>
        </div>
    </div>

    <!-- 操作日志 抽屉 -->
    <div id="log-drawer" class="drawer" style="width: 400px;">
        <div class="drawer-header">
            <h3 id="log-drawer-title">跟进记录</h3>
            <button style="background:none; border:none; font-size: 20px; cursor: pointer;" id="close-log-drawer-btn">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="section" id="lead-remark-section" style="display: none;">
                <h4 class="section-title" style="font-size: 14px; margin-bottom: 15px; border: none;">线索备注</h4>
                <div class="remark-content" id="lead-remark-content" style="background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid #3498db; margin-bottom: 20px;">
                    <!-- 备注内容动态显示 -->
                </div>
            </div>
            <div class="section">
                <h4 class="section-title" style="font-size: 14px; margin-bottom: 15px; border: none;">添加跟进记录</h4>
                <div class="form-group">
                    <textarea id="new-log-content" rows="3" class="form-control" placeholder="在此输入跟进内容，例如：已电话沟通，客户意向良好。"></textarea>
                </div>
                <div style="text-align: right;">
                    <button class="btn btn-primary" id="add-log-btn">添加跟进</button>
                </div>
            </div>
            <ul class="timeline" id="log-timeline">
                <!-- 日志条目动态生成 -->
            </ul>
        </div>
    </div>

    <!-- 分配线索 弹窗 -->
    <div id="assign-modal" class="modal" style="display: none; position: fixed; z-index: 2500; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); align-items: center; justify-content: center;">
        <div class="card" style="width: 500px; margin-bottom: 0;">
            <div class="card-header">
                <h3>分配线索</h3>
                 <button style="background:none; border:none; font-size: 20px; cursor: pointer;" id="close-assign-modal-btn">&times;</button>
            </div>
            <div class="card-body">
                <p>将线索 <strong id="assign-lead-name"></strong> 分配给：</p>
                
                <!-- 组织架构选择 -->
                <div class="form-group">
                    <label for="assign-department-level1">选择一级部门</label>
                    <select id="assign-department-level1" class="form-control" onchange="updateDepartmentLevel2()">
                        <option value="">请选择一级部门</option>
                        <option value="business">业务部门</option>
                        <option value="support">支持部门</option>
                        <option value="management">管理部门</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="assign-department-level2">选择二级部门</label>
                    <select id="assign-department-level2" class="form-control" onchange="updateDepartmentLevel3()" disabled>
                        <option value="">请先选择一级部门</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="assign-department-level3">选择三级部门</label>
                    <select id="assign-department-level3" class="form-control" onchange="updateAssignMembers()" disabled>
                        <option value="">请先选择二级部门</option>
                    </select>
                </div>
                
                <!-- 人员选择 -->
                <div class="form-group">
                    <label for="assign-owner">选择跟进人</label>
                    <select id="assign-owner" class="form-control" disabled>
                        <option value="">请先选择部门</option>
                    </select>
                </div>
                
                <!-- 当前选择显示 -->
                <div id="assign-selection-display" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; display: none;">
                    <div style="font-size: 12px; color: #666;">当前选择：</div>
                    <div id="assign-selection-text" style="font-weight: 500; margin-top: 5px;"></div>
                </div>
                
                 <div style="text-align: right; margin-top: 20px;">
                    <button class="btn btn-outline" id="cancel-assign-btn">取消</button>
                    <button class="btn btn-primary" id="confirm-assign-btn" disabled>确认分配</button>
                </div>
            </div>
        </div>
    </div>

    <div class="overlay" id="overlay"></div>

    <!-- 新增/编辑实践项目 抽屉 -->
    <div id="practice-project-drawer" class="drawer" style="width: 600px;">
        <div class="drawer-header">
            <h3 id="practice-project-drawer-title">新建实践项目</h3>
            <button style="background:none; border:none; font-size: 20px; cursor: pointer;" class="close-drawer-btn">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="practice-project-form">
                <input type="hidden" id="practice-project-id">
                <div class="section">
                    <h4 class="section-title">项目主体信息</h4>
                    <div class="form-group">
                        <label for="practice-project-name">项目名称</label>
                        <input type="text" id="practice-project-name" class="form-control" placeholder="例如: XX大学2024年暑期企业认知实践" required>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div class="form-group">
                            <label for="practice-project-school">合作高校</label>
                            <input type="text" id="practice-project-school" class="form-control" placeholder="从资源中心选择" required>
                        </div>
                        <div class="form-group">
                            <label for="practice-project-company">实践企业</label>
                            <input type="text" id="practice-project-company" class="form-control" placeholder="从资源中心选择" required>
                        </div>
                        <div class="form-group">
                            <label for="practice-project-start-date">项目总体开始日期</label>
                            <input type="date" id="practice-project-start-date" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="practice-project-end-date">项目总体结束日期</label>
                            <input type="date" id="practice-project-end-date" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="practice-project-owner">项目负责人</label>
                            <select id="practice-project-owner" class="form-control">
                                <option>张三</option>
                                <option>李四</option>
                                <option>王五</option>
                            </select>
                        </div>
                         <div class="form-group">
                            <label for="practice-project-status">项目状态</label>
                            <select id="practice-project-status" class="form-control">
                                <option>筹备中</option>
                                <option>报名中</option>
                                <option>进行中</option>
                                <option>已结束</option>
                                <option>已归档</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="section">
                    <h4 class="section-title">项目介绍</h4>
                     <div class="form-group">
                        <label for="practice-project-description">实践内容与目标</label>
                        <textarea id="practice-project-description" rows="4" class="form-control" placeholder="详细描述本次实践的主要内容、流程和预期达成的目标。"></textarea>
                    </div>
                     <div class="form-group">
                        <label for="practice-project-requirements">面向对象与要求</label>
                        <textarea id="practice-project-requirements" rows="3" class="form-control" placeholder="例如: 大二、大三学生，不限专业，要求有良好的沟通能力。"></textarea>
                    </div>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline close-drawer-btn">取消</button>
            <button class="btn btn-primary" id="save-practice-project-btn">保存项目</button>
        </div>
    </div>

     <!-- 新增/编辑班次 抽屉 -->
    <div id="practice-class-drawer" class="drawer">
        <div class="drawer-header">
            <h3 id="practice-class-drawer-title">新增班次</h3>
            <button style="background:none; border:none; font-size: 20px; cursor: pointer;" class="close-drawer-btn">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="practice-class-form">
                <input type="hidden" id="practice-class-id">
                <div class="form-group">
                    <label for="practice-class-name">班次名称</label>
                    <input type="text" id="practice-class-name" class="form-control" placeholder="例如: 上午班 / A组" required>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="form-group">
                        <label for="practice-class-start-date">实践开始日期</label>
                        <input type="date" id="practice-class-start-date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="practice-class-end-date">实践结束日期</label>
                        <input type="date" id="practice-class-end-date" class="form-control">
                    </div>
                </div>
                <div class="form-group">
                    <label for="practice-class-capacity">计划人数</label>
                    <input type="number" id="practice-class-capacity" class="form-control" placeholder="请输入该班次计划招生人数">
                </div>
                <div class="section" style="margin-top: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                         <h4 class="section-title" style="font-size: 14px; border: none; padding-bottom: 5px;">日程安排</h4>
                         <button type="button" class="btn btn-primary" style="font-size: 12px; padding: 5px 10px;" id="add-schedule-item-btn"><i class="fas fa-plus"></i> 添加日程</button>
                    </div>
                    <div class="table-wrapper" style="margin-top: 10px; max-height: 250px; overflow-y: auto;">
                        <table class="data-table" id="schedule-table">
                             <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>时间</th>
                                    <th>主题/内容</th>
                                    <th>负责人</th>
                                    <th>地点</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态日程条目 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline close-drawer-btn">取消</button>
            <button class="btn btn-primary" id="save-practice-class-btn">保存班次</button>
        </div>
    </div>
    
     <!-- 新增/编辑日程 抽屉 -->
    <div id="schedule-item-drawer" class="drawer" style="width: 450px; z-index: 2100;">
        <div class="drawer-header">
            <h3 id="schedule-item-drawer-title">新增日程</h3>
            <button style="background:none; border:none; font-size: 20px; cursor: pointer;" class="close-drawer-btn">&times;</button>
        </div>
        <div class="drawer-body">
            <form id="schedule-item-form">
                <input type="hidden" id="schedule-item-id">
                 <div class="form-group">
                    <label for="schedule-item-date">日期</label>
                    <input type="date" id="schedule-item-date" class="form-control">
                </div>
                 <div class="form-group">
                    <label for="schedule-item-time">时间</label>
                    <input type="text" id="schedule-item-time" class="form-control" placeholder="例如: 09:00-12:00">
                </div>
                <div class="form-group">
                    <label for="schedule-item-topic">主题/内容</label>
                    <textarea id="schedule-item-topic" rows="3" class="form-control" placeholder="例如: 公司文化与制度培训"></textarea>
                </div>
                <div class="form-group">
                    <label for="schedule-item-lecturer">讲师/负责人</label>
                    <input type="text" id="schedule-item-lecturer" class="form-control" placeholder="例如: 张三 人力资源部经理">
                </div>
                <div class="form-group">
                    <label for="schedule-item-location">地点</label>
                    <input type="text" id="schedule-item-location" class="form-control" placeholder="例如: 总部大楼301会议室">
                </div>
            </form>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline close-drawer-btn">取消</button>
            <button class="btn btn-primary" id="save-schedule-item-btn">保存日程</button>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // --- Global State & Variables ---
        const mainContentDivs = document.querySelectorAll('.main-content > .dashboard');
        const navLinks = document.querySelectorAll('.nav-link');
        const overlay = document.getElementById('overlay');
        let openOrderDetailsDrawerFunc;

        let opportunities = [
            { id: 'SJ2024001', name: 'XX大学2024年秋季实践项目', client: 'XX大学', amount: 500000, stage: '方案报价', approvalStatus: '已批准', owner: '张三', createdDate: '2024-05-10' },
            { id: 'SJ2024002', name: 'YY企业员工能力提升培训', client: 'YY企业', amount: 120000, stage: '商务谈判', approvalStatus: '已批准', owner: '李四', createdDate: '2024-05-15' },
            { id: 'SJ2024003', name: 'ZZ中学社会实践基地建设', client: 'ZZ中学', amount: 800000, stage: '需求分析', approvalStatus: '待审批', owner: '张三', createdDate: '2024-06-01' },
            { id: 'SJ2024004', name: 'AA公司年度人力资源咨询', client: 'AA公司', amount: 250000, stage: '初步接洽', approvalStatus: '已批准', owner: '王五', createdDate: '2024-06-05' },
            { id: 'SJ2024005', name: 'BB集团校园招聘外包服务', client: 'BB集团', amount: 300000, stage: '赢单', approvalStatus: '已批准', owner: '李四', createdDate: '2024-04-20' },
            { id: 'SJ2024006', name: 'CC科技公司技术培训', client: 'CC科技', amount: 180000, stage: '输单', approvalStatus: '已批准', owner: '张三', createdDate: '2024-05-22' },
            { id: 'SJ2024007', name: 'DD教育集团教师认证项目', client: 'DD教育集团', amount: 750000, stage: '需求分析', approvalStatus: '已批准', owner: '李四', createdDate: '2024-06-10' },
            { id: 'SJ2024008', name: 'EE医疗器械销售培训', client: 'EE医疗器械', amount: 220000, stage: '方案报价', approvalStatus: '待审批', owner: '王五', createdDate: '2024-06-12' },
            { id: 'SJ2024009', name: 'FF互联网公司新媒体运营', client: 'FF互联网', amount: 95000, stage: '商务谈判', approvalStatus: '已批准', owner: '张三', createdDate: '2024-06-15' },
            { id: 'SJ2024010', name: 'GG金融控股投资分析师培训', client: 'GG金融控股', amount: 480000, stage: '初步接洽', approvalStatus: '已退回', owner: '李四', createdDate: '2024-06-18' },
            { id: 'SJ2024011', name: 'HH物流公司仓储管理优化', client: 'HH物流', amount: 150000, stage: '赢单', approvalStatus: '已批准', owner: '王五', createdDate: '2024-03-25' },
            { id: 'SJ2024012', name: 'II地产公司金牌销售培训', client: 'II地产', amount: 320000, stage: '输单', approvalStatus: '已批准', owner: '张三', createdDate: '2024-06-02' },
        ];

        let leads = [
            { id: 'XS2024001', name: '李女士', phone: '***********', source: '视频号', businessModule: '家政业务', status: '未处理', owner: '未分配', creator: '系统自动', creationMethod: '系统生成', createdDate: '2024-06-18', remark: '客户需要金牌月嫂服务，预算2-3万，希望有经验的阿姨，最好能提供催乳服务。', logs: [] },
            { id: 'XS2024002', name: '王先生', phone: '***********', source: '官网注册', businessModule: '高校业务', status: '跟进中', owner: '李四', creator: '系统自动', creationMethod: '系统生成', createdDate: '2024-06-17', remark: 'XX大学计算机学院希望开展暑期企业实践项目，预计参与学生50人，时间7-8月。', logs: [] },
            { id: 'XS2024003', name: '赵总', phone: '***********', source: '抖音', businessModule: '培训业务', status: '已转化', owner: '张三', creator: '张三', creationMethod: '手动录入', createdDate: '2024-06-15', remark: '企业需要新员工入职培训，包括企业文化、岗位技能等，预计培训人数30人。', logs: [] },
            { id: 'XS2024004', name: '刘工', phone: '***********', source: '入驻商家', businessModule: '认证业务', status: '跟进中', owner: '王五', creator: '王五', creationMethod: '手动录入', createdDate: '2024-06-14', remark: '家政机构需要为员工申请职业技能等级认定，包括母婴护理、养老护理等证书。', logs: [] },
            { id: 'XS2024005', name: '陈女士', phone: '***********', source: '小红书', businessModule: '家政业务', status: '无意向', owner: '李四', creator: '系统自动', creationMethod: '系统生成', createdDate: '2024-06-12', remark: '客户咨询育儿嫂服务，但预算较低，无法满足我们的服务标准。', logs: [] },
            { id: 'XS2024006', name: '周老板', phone: '***********', source: '百度推广', businessModule: '培训业务', status: '未处理', owner: '未分配', creator: '张三', creationMethod: '手动录入', createdDate: '2024-06-19', remark: '物流公司需要仓储管理培训，提升员工工作效率和安全管理意识。', logs: [] },
            { id: 'XS2024007', name: '吴小姐', phone: '***********', source: '微信朋友圈', businessModule: '高校业务', status: '跟进中', owner: '李四', creator: '系统自动', creationMethod: '系统生成', createdDate: '2024-06-19', remark: 'YY师范大学希望开展教育实习项目，需要与优质教育机构合作。', logs: [] },
            { id: 'XS2024008', name: '郑先生', phone: '***********', source: 'QQ群/微信群', businessModule: '认证业务', status: '未处理', owner: '未分配', creator: '系统自动', creationMethod: '系统生成', createdDate: '2024-06-18', remark: '个人希望考取心理咨询师证书，需要了解报名条件和培训安排。', logs: [] },
            { id: 'XS2024009', name: '冯总', phone: '***********', source: '线下展会', businessModule: '培训业务', status: '已转化', owner: '王五', creator: '王五', creationMethod: '手动录入', createdDate: '2024-06-17', remark: '房地产公司需要销售技能培训，提升团队业绩，预计培训人数80人。', logs: [] },
            { id: 'XS2024010', name: '孙女士', phone: '***********', source: '老客户推荐', businessModule: '家政业务', status: '跟进中', owner: '张三', creator: '张三', creationMethod: '手动录入', createdDate: '2024-06-16', remark: '老客户推荐的朋友，需要日常保洁服务，每周2次，长期合作。', logs: [] },
            { id: 'XS2024011', name: '杨小姐', phone: '***********', source: '电话营销', businessModule: '高校业务', status: '无意向', owner: '李四', creator: '系统自动', creationMethod: '系统生成', createdDate: '2024-06-15', remark: '学校目前没有实践项目需求，建议后续跟进。', logs: [] },
            { id: 'XS2024012', name: '朱先生', phone: '***********', source: '短信营销', businessModule: '认证业务', status: '未处理', owner: '未分配', creator: '系统自动', creationMethod: '系统生成', createdDate: '2024-06-19', remark: '企业需要为员工申请多项职业技能证书，包括电工、焊工等。', logs: [] },
            { id: 'XS2024013', name: '何总', phone: '***********', source: '邮件营销', businessModule: '培训业务', status: '跟进中', owner: '王五', creator: '张三', creationMethod: '手动录入', createdDate: '2024-06-19', remark: '制造业公司需要安全生产培训，包括安全操作规程、应急处理等。', logs: [] },
            { id: 'XS2024014', name: '林先生', phone: '***********', source: '合作伙伴推荐', businessModule: '家政业务', status: '已转化', owner: '张三', creator: '张三', creationMethod: '手动录入', createdDate: '2024-06-18', remark: '合作伙伴推荐的高端客户，需要24小时住家保姆，要求英语流利。', logs: [] },
            { id: 'XS2024015', name: '潘女士', phone: '***********', source: '其他', businessModule: '高校业务', status: '未处理', owner: '未分配', creator: '系统自动', creationMethod: '系统生成', createdDate: '2024-06-19', remark: '职业技术学院希望开展校企合作项目，为学生提供实习机会。', logs: [] },
        ];

        let orders = [
            { id: 'DD20240510001', type: '家政服务', client: '李女士', service: '金牌月嫂服务（1个月）', amount: 25000, status: '已完成', paymentStatus: '已支付', createdDate: '2024-05-10', details: { address: '上海市黄浦区XX路XX号', auntie: '刘阿姨', startDate: '2024-05-11' } },
            { id: 'DD20240620001', type: '高校实践', client: 'XX大学', service: '2024秋季学期社会实践项目', amount: 500000, status: '待履约', paymentStatus: '已支付', createdDate: '2024-06-15', details: { school: 'XX大学', studentCount: 300, startDate: '2024-09-01' } },
            { id: 'DD20240620002', type: '企业培训', client: 'YY企业', service: '新员工入职培训', amount: 120000, status: '已完成', paymentStatus: '已支付', createdDate: '2024-05-10', details: { trainer: '李教授', traineeCount: 50, trainingDate: '2024-05-20' } },
            { id: 'DD20240620003', type: '个人报名', client: '王女士', service: '金牌月嫂职业技能培训', amount: 4999, status: '已完成', paymentStatus: '已支付', createdDate: '2024-06-01', details: {} },
            { id: 'DD20240620004', type: '家政服务', client: '赵先生', service: '高级育儿嫂服务（3个月）', amount: 45000, status: '待履约', paymentStatus: '已支付', createdDate: '2024-06-18', details: { address: '上海市浦东新区XX路XX号', auntie: '王阿姨', startDate: '2024-07-01' } },
            { id: 'DD20240620005', type: '个人报名', client: '孙先生', service: 'Python入门与数据分析', amount: 99, status: '待支付', paymentStatus: '未支付', createdDate: '2024-06-19', details: {} },
            { id: 'DD20240620006', type: '家政服务', client: '周女士', service: '日常保洁（4小时）', amount: 300, status: '已关闭', paymentStatus: '未支付', createdDate: '2024-06-12', details: {} },
        ];

        let practiceProjects = [
            {
                id: 'GX2024001',
                name: 'A大学2024暑期企业认知实践',
                school: 'A大学',
                company: 'XX科技有限公司',
                startDate: '2024-07-15',
                endDate: '2024-08-15',
                owner: '张三',
                status: '进行中',
                totalCapacity: 90,
                confirmedStudents: 85,
                description: '旨在通过为期一个月的企业实践，帮助学生了解行业动态，掌握基本职业技能，提升就业竞争力。',
                requirements: 'A大学大二、大三学生，不限专业，要求有良好的沟通能力和团队协作精神。',
                classes: [
                    { 
                        id: 'C01', 
                        name: '上午班', 
                        startDate: '2024-07-15',
                        endDate: '2024-07-30',
                        capacity: 30, 
                        confirmed: 30,
                        status: '已满额',
                        schedule: [
                            { id: 'S01', date: '2024-07-15', time: '09:00-11:00', topic: '开营仪式与企业文化介绍', lecturer: 'HR-王经理', location: '总部报告厅' },
                            { id: 'S02', date: '2024-07-16', time: '14:00-16:00', topic: '产品部门工作流程与分享', lecturer: '产品总监-李明', location: '301会议室' },
                        ]
                    },
                    { 
                        id: 'C02', 
                        name: '下午班', 
                        startDate: '2024-07-15',
                        endDate: '2024-07-30',
                        capacity: 30, 
                        confirmed: 28,
                        status: '报名中',
                        schedule: []
                    },
                    { 
                        id: 'C03', 
                        name: 'B组', 
                        startDate: '2024-08-01',
                        endDate: '2024-08-15',
                        capacity: 30, 
                        confirmed: 27,
                        status: '报名中',
                        schedule: []
                    },
                ],
                enrolledStudents: [
                    { name: '张三', studentId: '2021001', className: '上午班', status: '已确认', applyDate: '2024-06-20'},
                    { name: '李四', studentId: '2021002', className: '下午班', status: '已确认', applyDate: '2024-06-21'},
                    { name: '王五', studentId: '2021003', className: 'B组', status: '待确认', applyDate: '2024-06-22'},
                ]
            },
            {
                id: 'GX2024002',
                name: 'B大学2024秋季金融科技实践',
                school: 'B大学',
                company: 'YY金融科技有限公司',
                startDate: '2024-09-01',
                endDate: '2024-09-30',
                owner: '李四',
                status: '报名中',
                totalCapacity: 50,
                confirmedStudents: 5,
                description: '深入了解金融科技行业的前沿动态，参与真实项目。',
                requirements: '金融、计算机相关专业优先。',
                classes: [
                     { 
                        id: 'C04', 
                        name: '唯一班次', 
                        startDate: '2024-09-01',
                        endDate: '2024-09-30',
                        capacity: 50, 
                        confirmed: 5,
                        status: '报名中',
                        schedule: []
                    }
                ],
                enrolledStudents: []
            }
        ];

        let partners = [
            { id: 'P001', name: '北京大学', type: '高校', status: '合作中', risk: '正常', owner: '张三', createdDate: '2023-01-15' },
            { id: 'P002', name: '阿里巴巴集团', type: '企业', status: '合作中', risk: '正常', owner: '李四', createdDate: '2022-11-20' },
            { id: 'P003', 'name': '某某家政服务有限公司', type: '家政机构', status: '待审核', risk: '正常', owner: '王五', createdDate: '2024-06-10' },
            { id: 'P004', 'name': '华育国际', type: '供应商', status: '已终止', risk: '经营异常', owner: '张三', createdDate: '2021-05-30' },
            { id: 'P005', 'name': '前程无忧', type: '渠道合作方', status: '合作中', risk: '正常', owner: '李四', createdDate: '2023-08-01' }
        ];

        let teachers = [
            { id: 'T001', name: '王老师', type: '内部讲师', field: '职业规划、面试技巧', phone: '***********', status: '合作中' },
            { id: 'T002', name: '李教授', type: '外部讲师', field: '计算机科学、人工智能', phone: '***********', status: '合作中' },
            { id: 'T003', name: '张讲师', type: '外部讲师', field: '市场营销、新媒体运营', phone: '***********', status: '待签约' }
        ];

        let aunties = [
            {
                id: 'A001',
                name: '刘阿姨',
                avatar: 'https://placehold.co/100x100/3498db/ffffff/png?text=刘',
                age: 48,
                level: '金牌',
                serviceType: '月嫂',
                status: '服务中',
                workYears: 10,
                nativePlace: '四川 成都',
                education: '大专',
                rating: '99%',
                orders: 152,
                verificationStatus: '已审核',
                certificates: [
                    { name: '身份证', img: 'https://placehold.co/120x85/95a5a6/ffffff/png?text=身份证', verified: true },
                    { name: '健康证', img: 'https://placehold.co/120x85/2ecc71/ffffff/png?text=健康证', verified: true },
                    { name: '金牌月嫂证', img: 'https://placehold.co/120x85/f1c40f/ffffff/png?text=月嫂证', verified: true }
                ],
                serviceHistory: [ { orderId: 'DD20240510001' } ],
                customerReviews: [
                    { customer: '李女士', rating: 5, comment: '刘阿姨非常专业，有耐心，把我和宝宝都照顾得很好。' }
                ],
                talentPortrait: '10年母婴护理经验，持有金牌月嫂证。擅长新生儿常见问题处理与产妇心理疏导。性格沉稳，做事有条不紊，深受高端客户信赖。'
            },
            {
                id: 'A002',
                name: '王阿姨',
                avatar: 'https://placehold.co/100x100/2ecc71/ffffff/png?text=王',
                age: 45,
                level: '高级',
                serviceType: '育儿嫂',
                status: '待岗',
                workYears: 6,
                nativePlace: '湖南 长沙',
                education: '高中',
                rating: '95%',
                orders: 89,
                verificationStatus: '已审核',
                certificates: [
                    { name: '身份证', img: 'https://placehold.co/120x85/95a5a6/ffffff/png?text=身份证', verified: true },
                    { name: '健康证', img: 'https://placehold.co/120x85/2ecc71/ffffff/png?text=健康证', verified: true },
                    { name: '高级育儿嫂证', img: 'https://placehold.co/120x85/f39c12/ffffff/png?text=育儿嫂', verified: false }
                ],
                serviceHistory: [ { orderId: 'DD20240620004' } ],
                customerReviews: [
                    { customer: '赵先生', rating: 5, comment: '王阿姨很有亲和力，孩子很喜欢她，带教也很有方法。' }
                ],
                talentPortrait: '6年育儿嫂经验，擅长幼儿早期教育与良好习惯培养。性格活泼开朗，有爱心。'
            },
            {
                id: 'A003',
                name: '李阿姨',
                avatar: 'https://placehold.co/100x100/e74c3c/ffffff/png?text=李',
                age: 52,
                level: '中级',
                serviceType: '综合家务',
                status: '培训中',
                workYears: 8,
                nativePlace: '河南 郑州',
                education: '初中',
                rating: '92%',
                orders: 34,
                verificationStatus: '待审核',
                certificates: [
                    { name: '身份证', img: 'https://placehold.co/120x85/95a5a6/ffffff/png?text=身份证', verified: false },
                ],
                serviceHistory: [],
                customerReviews: [],
                talentPortrait: '8年家政服务经验，擅长中式烹饪与收纳整理。目前正在平台参与高级家政师培训，待完成审核。'
            }
        ];

        let courses = [
            { 
                id: 'C001', 
                name: '金牌月嫂职业技能培训', 
                category: '家政技能', 
                deliveryMethod: '线下授课', 
                price: 4999, 
                status: '已上架',
                teacher: '王老师',
                duration: 80,
                targetAudience: '希望从事母婴护理行业的女性，零基础学员',
                syllabus: '第一章：新生儿护理\n第二章：产妇护理\n第三章：月子餐制作',
                description: '系统化培训，理论与实践相结合，打造专业的金牌月嫂。'
            },
            { 
                id: 'C002', 
                name: '大学生职业生涯规划', 
                category: '职业素养', 
                deliveryMethod: '线上直播', 
                price: 299, 
                status: '已上架',
                teacher: '李教授',
                duration: 16,
                targetAudience: '在校大学生、应届毕业生',
                syllabus: '第一节：自我认知与定位\n第二节：行业与职业分析\n第三节：求职技巧与简历制作',
                description: '帮助大学生明确发展方向，提升求职竞争力。'
            },
            { 
                id: 'C003', 
                name: 'Python入门与数据分析', 
                category: '高校实践', 
                deliveryMethod: '线上录播', 
                price: 99, 
                status: '待上架',
                teacher: '张讲师',
                duration: 24,
                targetAudience: '对编程及数据分析感兴趣的零基础学员',
                syllabus: '基础语法、Pandas库、Matplotlib可视化',
                description: '快速上手Python，掌握主流数据分析工具。'
            }
        ];

        // --- Global Functions ---
        function getCurrentDateTime() {
            return new Date().toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' }).replace(/\//g, '-');
        }

        function addLog(leadId, content, author = '张三') {
            const lead = leads.find(l => l.id === leadId);
            if (lead) {
                if(!lead.logs) lead.logs = [];
                lead.logs.unshift({
                    timestamp: getCurrentDateTime(),
                    author: author,
                    content: content
                });
            }
        }
        
        function initializeLogs() {
            leads.forEach(lead => {
                if (!lead.logs || lead.logs.length === 0) { 
                    addLog(lead.id, `由 ${lead.creator} 创建了该线索。`, lead.creator);
                    if (lead.owner !== '未分配') {
                        addLog(lead.id, `系统自动分配给 ${lead.owner}。`, '系统');
                    }
                    if(lead.status === '跟进中') {
                        addLog(lead.id, `电话沟通，客户表示需要考虑一下。`, lead.owner);
                    }
                    if(lead.status === '已转化') {
                        addLog(lead.id, `客户意向明确，成功转化为商机。`, lead.owner);
                    }
                }
            });
        }

        // --- Page Navigation ---
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.dataset.target || (this.innerText.includes('工作台') ? 'dashboard' : null);

                if (targetId) {
                    mainContentDivs.forEach(div => {
                        div.style.display = (div.id === targetId) ? 'block' : 'none';
                    });
                    navLinks.forEach(nav => nav.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });


        // --- Opportunity Center Logic ---
        (function() {
            const tableBody = document.querySelector('#opportunity-table tbody');
            const addBtn = document.getElementById('add-opportunity-btn');
            const drawer = document.getElementById('opportunity-drawer');
            const closeBtn = document.getElementById('close-drawer-btn');
            const cancelBtn = document.getElementById('cancel-btn');
            const saveBtn = document.getElementById('save-btn');
            const form = document.getElementById('opportunity-form');
            const drawerTitle = document.getElementById('drawer-title');

            function renderTable() {
                tableBody.innerHTML = '';
                opportunities.forEach(op => {
                    const row = document.createElement('tr');
                    row.dataset.id = op.id;
                    row.innerHTML = `
                        <td>${op.id}</td>
                        <td>${op.name}</td>
                        <td>${op.client}</td>
                        <td>${op.amount.toLocaleString('zh-CN')}</td>
                        <td>${op.stage}</td>
                        <td><span class="status-badge ${op.approvalStatus === '已批准' ? 'status-approved' : (op.approvalStatus === '待审批' ? 'status-pending' : 'status-rejected')}">${op.approvalStatus}</span></td>
                        <td>${op.owner}</td>
                        <td>${op.createdDate}</td>
                        <td class="action-buttons">
                            <button class="btn btn-primary edit-btn">编辑</button>
                            <button class="btn btn-danger-outline delete-btn">删除</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            }

            function openDrawer(title = '新建商机') {
                drawerTitle.innerText = title;
                drawer.classList.add('open');
                overlay.classList.add('active');
            }

            function closeDrawer() {
                drawer.classList.remove('open');
                overlay.classList.remove('active');
                form.reset();
                document.getElementById('opportunity-id').value = '';
            }

            addBtn.addEventListener('click', () => openDrawer('新建商机'));
            closeBtn.addEventListener('click', closeDrawer);
            cancelBtn.addEventListener('click', closeDrawer);

            saveBtn.addEventListener('click', () => {
                const id = document.getElementById('opportunity-id').value;
                const newOpportunity = {
                    id: id || `SJ${new Date().getFullYear()}${(Math.random() * 1000).toFixed(0).padStart(3, '0')}`,
                    name: document.getElementById('opportunity-name').value,
                    client: document.getElementById('opportunity-client').value,
                    amount: parseInt(document.getElementById('opportunity-amount').value),
                    stage: document.getElementById('opportunity-stage').value,
                    owner: document.getElementById('opportunity-owner').value,
                };

                if (!newOpportunity.name || !newOpportunity.client || isNaN(newOpportunity.amount) || newOpportunity.amount <= 0) {
                    alert('请填写所有必填项，并确保金额为正数！');
                    return;
                }

                if (id) {
                    const index = opportunities.findIndex(op => op.id === id);
                    if (index > -1) {
                        opportunities[index] = { ...opportunities[index], ...newOpportunity, approvalStatus: '待审批' };
                    }
                } else {
                    opportunities.unshift({ ...newOpportunity, approvalStatus: '待审批', createdDate: new Date().toISOString().split('T')[0] });
                }
                
                renderTable();
                closeDrawer();
            });

            tableBody.addEventListener('click', function(e) {
                const target = e.target;
                const row = target.closest('tr');
                if (!row) return;

                const id = row.dataset.id;
                const opportunity = opportunities.find(op => op.id === id);

                if (target.classList.contains('edit-btn')) {
                    document.getElementById('opportunity-id').value = opportunity.id;
                    document.getElementById('opportunity-name').value = opportunity.name;
                    document.getElementById('opportunity-client').value = opportunity.client;
                    document.getElementById('opportunity-amount').value = opportunity.amount;
                    document.getElementById('opportunity-date').value = opportunity.createdDate;
                    document.getElementById('opportunity-stage').value = opportunity.stage;
                    document.getElementById('opportunity-owner').value = opportunity.owner;
                    document.getElementById('opportunity-description').value = `${opportunity.name} 的详细描述信息。`;
                    openDrawer('编辑商机');
                }

                if (target.classList.contains('delete-btn')) {
                    if (confirm(`确定要删除商机 "${opportunity.name}" 吗？`)) {
                        opportunities = opportunities.filter(op => op.id !== id);
                        renderTable();
                    }
                }
            });
            
            renderTable(); // Initial render
        })();


        // --- Lead Center Logic ---
        (function() {
            const tableBody = document.querySelector('#lead-table tbody');
            const addBtn = document.getElementById('add-lead-btn');
            const drawer = document.getElementById('lead-drawer');
            const closeBtn = document.getElementById('close-lead-drawer-btn');
            const cancelBtn = document.getElementById('cancel-lead-btn');
            const saveBtn = document.getElementById('save-lead-btn');
            const form = document.getElementById('lead-form');
            const drawerTitle = document.getElementById('lead-drawer-title');

            const logDrawer = document.getElementById('log-drawer');
            const closeLogDrawerBtn = document.getElementById('close-log-drawer-btn');
            const logTimeline = document.getElementById('log-timeline');
            const logDrawerTitle = document.getElementById('log-drawer-title');
            const addLogBtn = document.getElementById('add-log-btn');

            const assignModal = document.getElementById('assign-modal');
            const closeAssignModalBtn = document.getElementById('close-assign-modal-btn');
            const cancelAssignBtn = document.getElementById('cancel-assign-btn');
            const confirmAssignBtn = document.getElementById('confirm-assign-btn');
            
            let currentAssigningLeadId = null;
            let currentViewingLeadId = null;

            // 多级组织架构数据
            const organizationStructure = {
                business: {
                    name: '业务部门',
                    children: {
                        sales: {
                            name: '销售部',
                            children: {
                                sales_north: {
                                    name: '华北销售部',
                                    members: [
                                        { id: 'sales_north_001', name: '张三', title: '华北销售经理', phone: '***********' },
                                        { id: 'sales_north_002', name: '李四', title: '华北高级销售', phone: '***********' }
                                    ]
                                },
                                sales_south: {
                                    name: '华南销售部',
                                    members: [
                                        { id: 'sales_south_001', name: '王五', title: '华南销售经理', phone: '***********' },
                                        { id: 'sales_south_002', name: '赵六', title: '华南销售专员', phone: '13800138004' }
                                    ]
                                }
                            }
                        },
                        marketing: {
                            name: '市场部',
                            children: {
                                marketing_digital: {
                                    name: '数字营销部',
                                    members: [
                                        { id: 'marketing_digital_001', name: '钱七', title: '数字营销总监', phone: '13800138005' },
                                        { id: 'marketing_digital_002', name: '孙八', title: 'SEO专员', phone: '13800138006' }
                                    ]
                                },
                                marketing_brand: {
                                    name: '品牌推广部',
                                    members: [
                                        { id: 'marketing_brand_001', name: '周九', title: '品牌经理', phone: '13800138007' },
                                        { id: 'marketing_brand_002', name: '吴十', title: '设计专员', phone: '13800138008' }
                                    ]
                                }
                            }
                        },
                        training: {
                            name: '培训部',
                            children: {
                                training_online: {
                                    name: '在线培训部',
                                    members: [
                                        { id: 'training_online_001', name: '郑十一', title: '在线培训经理', phone: '13800138009' },
                                        { id: 'training_online_002', name: '王十二', title: '课程开发专员', phone: '13800138010' }
                                    ]
                                },
                                training_offline: {
                                    name: '线下培训部',
                                    members: [
                                        { id: 'training_offline_001', name: '刘十三', title: '线下培训经理', phone: '13800138011' },
                                        { id: 'training_offline_002', name: '陈十四', title: '培训讲师', phone: '13800138012' }
                                    ]
                                }
                            }
                        },
                        certification: {
                            name: '认证部',
                            children: {
                                cert_management: {
                                    name: '认证管理部',
                                    members: [
                                        { id: 'cert_management_001', name: '杨十五', title: '认证管理经理', phone: '13800138013' },
                                        { id: 'cert_management_002', name: '黄十六', title: '认证专员', phone: '13800138014' }
                                    ]
                                },
                                cert_quality: {
                                    name: '质量监督部',
                                    members: [
                                        { id: 'cert_quality_001', name: '林十七', title: '质量监督经理', phone: '13800138015' },
                                        { id: 'cert_quality_002', name: '何十八', title: '质量专员', phone: '13800138016' }
                                    ]
                                }
                            }
                        }
                    }
                },
                support: {
                    name: '支持部门',
                    children: {
                        university: {
                            name: '高校业务部',
                            children: {
                                uni_cooperation: {
                                    name: '校企合作部',
                                    members: [
                                        { id: 'uni_cooperation_001', name: '罗十九', title: '校企合作经理', phone: '13800138017' },
                                        { id: 'uni_cooperation_002', name: '梁二十', title: '合作专员', phone: '13800138018' }
                                    ]
                                },
                                uni_practice: {
                                    name: '实践项目部',
                                    members: [
                                        { id: 'uni_practice_001', name: '谢二一', title: '实践项目经理', phone: '13800138019' },
                                        { id: 'uni_practice_002', name: '韩二二', title: '项目专员', phone: '13800138020' }
                                    ]
                                }
                            }
                        },
                        domestic: {
                            name: '家政服务部',
                            children: {
                                domestic_service: {
                                    name: '服务管理部',
                                    members: [
                                        { id: 'domestic_service_001', name: '董二三', title: '服务管理经理', phone: '13800138021' },
                                        { id: 'domestic_service_002', name: '马二四', title: '服务专员', phone: '13800138022' }
                                    ]
                                },
                                domestic_quality: {
                                    name: '服务质量部',
                                    members: [
                                        { id: 'domestic_quality_001', name: '朱二五', title: '服务质量经理', phone: '13800138023' },
                                        { id: 'domestic_quality_002', name: '胡二六', title: '质量专员', phone: '13800138024' }
                                    ]
                                }
                            }
                        }
                    }
                },
                management: {
                    name: '管理部门',
                    children: {
                        hr: {
                            name: '人力资源部',
                            children: {
                                hr_recruitment: {
                                    name: '招聘部',
                                    members: [
                                        { id: 'hr_recruitment_001', name: '郭二七', title: '招聘经理', phone: '13800138025' },
                                        { id: 'hr_recruitment_002', name: '刘二八', title: '招聘专员', phone: '13800138026' }
                                    ]
                                },
                                hr_development: {
                                    name: '培训发展部',
                                    members: [
                                        { id: 'hr_development_001', name: '张二九', title: '培训发展经理', phone: '***********' },
                                        { id: 'hr_development_002', name: '李三十', title: '发展专员', phone: '***********' }
                                    ]
                                }
                            }
                        },
                        finance: {
                            name: '财务部',
                            children: {
                                finance_accounting: {
                                    name: '会计核算部',
                                    members: [
                                        { id: 'finance_accounting_001', name: '王三一', title: '会计经理', phone: '***********' },
                                        { id: 'finance_accounting_002', name: '赵三二', title: '会计', phone: '***********' }
                                    ]
                                },
                                finance_audit: {
                                    name: '审计部',
                                    members: [
                                        { id: 'finance_audit_001', name: '钱三三', title: '审计经理', phone: '***********' },
                                        { id: 'finance_audit_002', name: '孙三四', title: '审计专员', phone: '***********' }
                                    ]
                                }
                            }
                        },
                        tech: {
                            name: '技术部',
                            children: {
                                tech_frontend: {
                                    name: '前端开发部',
                                    members: [
                                        { id: 'tech_frontend_001', name: '周三五', title: '前端技术总监', phone: '13800138033' },
                                        { id: 'tech_frontend_002', name: '吴三六', title: '前端工程师', phone: '13800138034' }
                                    ]
                                },
                                tech_backend: {
                                    name: '后端开发部',
                                    members: [
                                        { id: 'tech_backend_001', name: '郑三七', title: '后端技术总监', phone: '***********' },
                                        { id: 'tech_backend_002', name: '王三八', title: '后端工程师', phone: '***********' }
                                    ]
                                }
                            }
                        }
                    }
                }
            };

            /**
             * 根据业务模块返回对应的标签样式类名
             * @param {string} businessModule 业务模块
             * @returns {string} 标签样式类名
             */
            function getBusinessModuleTagClass(businessModule) {
                const moduleMap = {
                    '高校业务': 'blue',
                    '家政业务': 'green',
                    '培训业务': 'orange',
                    '认证业务': 'purple'
                };
                return moduleMap[businessModule] || 'gray';
            }

            function renderLeadsTable() {
                tableBody.innerHTML = '';
                leads.forEach(lead => {
                    const maskedPhone = lead.phone.substring(0, 3) + '****' + lead.phone.substring(7);
                    const row = document.createElement('tr');
                    row.dataset.id = lead.id;
                    row.innerHTML = `
                        <td>${lead.id}</td>
                        <td>${lead.name}</td>
                        <td>${maskedPhone}</td>
                        <td>${lead.source}</td>
                        <td><span class="tag tag-${getBusinessModuleTagClass(lead.businessModule)}">${lead.businessModule || '-'}</span></td>
                        <td>${lead.status}</td>
                        <td>${lead.creationMethod}</td>
                        <td>${lead.creator}</td>
                        <td>${lead.owner}</td>
                        <td>${lead.createdDate}</td>
                        <td class="action-buttons">
                            <button class="btn btn-primary edit-lead-btn" style="padding: 5px 8px; font-size: 12px;">编辑</button>
                            <button class="btn btn-outline assign-lead-btn" style="padding: 5px 8px; font-size: 12px;">分配</button>
                            <button class="btn btn-outline log-lead-btn" style="padding: 5px 8px; font-size: 12px;">跟进</button>
                            <button class="btn btn-danger-outline delete-lead-btn" style="padding: 5px 8px; font-size: 12px;">删除</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            }

            function openLeadDrawer(title = '新建线索') {
                drawerTitle.innerText = title;
                drawer.classList.add('open');
                overlay.classList.add('active');
            }

            function closeLeadDrawer() {
                drawer.classList.remove('open');
                if (!logDrawer.classList.contains('open') && !assignModal.style.display.includes('flex')) {
                   overlay.classList.remove('active');
                }
                form.reset();
                document.getElementById('lead-id').value = '';
            }
            
            function openLogDrawer(lead) {
                currentViewingLeadId = lead.id;
                logDrawer.classList.add('open');
                overlay.classList.add('active');
                logDrawerTitle.innerText = `"${lead.name}"的跟进记录`;
                
                // 显示备注信息
                const remarkSection = document.getElementById('lead-remark-section');
                const remarkContent = document.getElementById('lead-remark-content');
                if (lead.remark && lead.remark.trim()) {
                    remarkSection.style.display = 'block';
                    remarkContent.innerHTML = lead.remark.replace(/\n/g, '<br>');
                } else {
                    remarkSection.style.display = 'none';
                }
                
                renderLogs(lead.id);
            }

            function renderLogs(leadId) {
                const lead = leads.find(l => l.id === leadId);
                logTimeline.innerHTML = '';
                if (lead && lead.logs) {
                    lead.logs.forEach(log => {
                         const logItem = document.createElement('li');
                         logItem.className = 'timeline-item';
                         logItem.innerHTML = `
                            <div class="timeline-time">${log.timestamp}</div>
                            <div class="timeline-content"><strong>${log.author}:</strong> ${log.content}</div>
                        `;
                        logTimeline.appendChild(logItem);
                    });
                }
            }

            function closeLogDrawer() {
                logDrawer.classList.remove('open');
                 if (!drawer.classList.contains('open') && !assignModal.style.display.includes('flex')) {
                   overlay.classList.remove('active');
                }
                currentViewingLeadId = null;
            }
            
            function openAssignModal(lead) {
                currentAssigningLeadId = lead.id;
                document.getElementById('assign-lead-name').innerText = `"${lead.name}" (ID: ${lead.id})`;
                
                // 重置分配弹窗状态
                resetAssignModal();
                
                // 如果线索已有分配，尝试预选部门和人员
                if (lead.owner && lead.owner !== '未分配') {
                    // 查找该人员所在的多级部门
                    for (const [level1Key, level1Dept] of Object.entries(organizationStructure)) {
                        for (const [level2Key, level2Dept] of Object.entries(level1Dept.children)) {
                            for (const [level3Key, level3Dept] of Object.entries(level2Dept.children)) {
                                const member = level3Dept.members.find(m => m.name === lead.owner);
                                if (member) {
                                    const level1Select = document.getElementById('assign-department-level1');
                                    const level2Select = document.getElementById('assign-department-level2');
                                    const level3Select = document.getElementById('assign-department-level3');
                                    const ownerSelect = document.getElementById('assign-owner');
                                    
                                    // 设置一级部门
                                    level1Select.value = level1Key;
                                    updateDepartmentLevel2();
                                    
                                    // 等待DOM更新后设置二级部门
                                    setTimeout(() => {
                                        level2Select.value = level2Key;
                                        updateDepartmentLevel3();
                                        
                                        // 等待DOM更新后设置三级部门
                                        setTimeout(() => {
                                            level3Select.value = level3Key;
                                            updateAssignMembers();
                                            
                                            // 等待DOM更新后设置人员选择
                                            setTimeout(() => {
                                                ownerSelect.value = lead.owner;
                                                updateAssignSelection();
                                            }, 10);
                                        }, 10);
                                    }, 10);
                                    return; // 找到后退出
                                }
                            }
                        }
                    }
                }
                
                assignModal.style.display = 'flex';
                overlay.classList.add('active');
            }

            function closeAssignModal() {
                 assignModal.style.display = 'none';
                 if (!drawer.classList.contains('open') && !logDrawer.classList.contains('open')) {
                   overlay.classList.remove('active');
                }
                currentAssigningLeadId = null;
                
                // 重置分配弹窗状态
                resetAssignModal();
            }

            // 更新二级部门 - 设置为全局函数
            window.updateDepartmentLevel2 = function() {
                const level1Select = document.getElementById('assign-department-level1');
                const level2Select = document.getElementById('assign-department-level2');
                const level3Select = document.getElementById('assign-department-level3');
                const ownerSelect = document.getElementById('assign-owner');
                const confirmBtn = document.getElementById('confirm-assign-btn');
                const selectionDisplay = document.getElementById('assign-selection-display');
                
                const selectedLevel1 = level1Select.value;
                
                // 重置下级选择
                level2Select.innerHTML = '<option value="">请先选择一级部门</option>';
                level2Select.disabled = true;
                level3Select.innerHTML = '<option value="">请先选择二级部门</option>';
                level3Select.disabled = true;
                ownerSelect.innerHTML = '<option value="">请先选择三级部门</option>';
                ownerSelect.disabled = true;
                confirmBtn.disabled = true;
                selectionDisplay.style.display = 'none';
                
                if (selectedLevel1 && organizationStructure[selectedLevel1]) {
                    const level1Dept = organizationStructure[selectedLevel1];
                    level2Select.disabled = false;
                    
                    // 添加二级部门选项
                    Object.entries(level1Dept.children).forEach(([key, dept]) => {
                        const option = document.createElement('option');
                        option.value = key;
                        option.textContent = dept.name;
                        level2Select.appendChild(option);
                    });
                }
            }

            // 更新三级部门 - 设置为全局函数
            window.updateDepartmentLevel3 = function() {
                const level1Select = document.getElementById('assign-department-level1');
                const level2Select = document.getElementById('assign-department-level2');
                const level3Select = document.getElementById('assign-department-level3');
                const ownerSelect = document.getElementById('assign-owner');
                const confirmBtn = document.getElementById('confirm-assign-btn');
                const selectionDisplay = document.getElementById('assign-selection-display');
                
                const selectedLevel1 = level1Select.value;
                const selectedLevel2 = level2Select.value;
                
                // 重置下级选择
                level3Select.innerHTML = '<option value="">请先选择二级部门</option>';
                level3Select.disabled = true;
                ownerSelect.innerHTML = '<option value="">请先选择三级部门</option>';
                ownerSelect.disabled = true;
                confirmBtn.disabled = true;
                selectionDisplay.style.display = 'none';
                
                if (selectedLevel1 && selectedLevel2 && 
                    organizationStructure[selectedLevel1] && 
                    organizationStructure[selectedLevel1].children[selectedLevel2]) {
                    
                    const level2Dept = organizationStructure[selectedLevel1].children[selectedLevel2];
                    level3Select.disabled = false;
                    
                    // 添加三级部门选项
                    Object.entries(level2Dept.children).forEach(([key, dept]) => {
                        const option = document.createElement('option');
                        option.value = key;
                        option.textContent = dept.name;
                        level3Select.appendChild(option);
                    });
                }
            }

            // 更新人员列表 - 设置为全局函数
            window.updateAssignMembers = function() {
                const level1Select = document.getElementById('assign-department-level1');
                const level2Select = document.getElementById('assign-department-level2');
                const level3Select = document.getElementById('assign-department-level3');
                const ownerSelect = document.getElementById('assign-owner');
                const confirmBtn = document.getElementById('confirm-assign-btn');
                const selectionDisplay = document.getElementById('assign-selection-display');
                const selectionText = document.getElementById('assign-selection-text');
                
                const selectedLevel1 = level1Select.value;
                const selectedLevel2 = level2Select.value;
                const selectedLevel3 = level3Select.value;
                
                // 重置人员选择
                ownerSelect.innerHTML = '<option value="">请先选择三级部门</option>';
                ownerSelect.disabled = true;
                confirmBtn.disabled = true;
                selectionDisplay.style.display = 'none';
                
                if (selectedLevel1 && selectedLevel2 && selectedLevel3 && 
                    organizationStructure[selectedLevel1] && 
                    organizationStructure[selectedLevel1].children[selectedLevel2] &&
                    organizationStructure[selectedLevel1].children[selectedLevel2].children[selectedLevel3]) {
                    
                    const level3Dept = organizationStructure[selectedLevel1].children[selectedLevel2].children[selectedLevel3];
                    ownerSelect.disabled = false;
                    
                    // 添加人员选项
                    level3Dept.members.forEach(member => {
                        const option = document.createElement('option');
                        option.value = member.name;
                        option.textContent = `${member.name} (${member.title})`;
                        ownerSelect.appendChild(option);
                    });
                }
            }

            // 更新选择显示
            function updateAssignSelection() {
                const level1Select = document.getElementById('assign-department-level1');
                const level2Select = document.getElementById('assign-department-level2');
                const level3Select = document.getElementById('assign-department-level3');
                const ownerSelect = document.getElementById('assign-owner');
                const confirmBtn = document.getElementById('confirm-assign-btn');
                const selectionDisplay = document.getElementById('assign-selection-display');
                const selectionText = document.getElementById('assign-selection-text');
                
                const selectedLevel1 = level1Select.value;
                const selectedLevel2 = level2Select.value;
                const selectedLevel3 = level3Select.value;
                const selectedOwner = ownerSelect.value;
                
                if (selectedLevel1 && selectedLevel2 && selectedLevel3 && selectedOwner && 
                    organizationStructure[selectedLevel1] && 
                    organizationStructure[selectedLevel1].children[selectedLevel2] &&
                    organizationStructure[selectedLevel1].children[selectedLevel2].children[selectedLevel3]) {
                    
                    const level1Dept = organizationStructure[selectedLevel1];
                    const level2Dept = level1Dept.children[selectedLevel2];
                    const level3Dept = level2Dept.children[selectedLevel3];
                    const member = level3Dept.members.find(m => m.name === selectedOwner);
                    
                    if (member) {
                        selectionText.textContent = `${level1Dept.name} > ${level2Dept.name} > ${level3Dept.name} - ${member.name} (${member.title})`;
                        selectionDisplay.style.display = 'block';
                        confirmBtn.disabled = false;
                    }
                } else {
                    selectionDisplay.style.display = 'none';
                    confirmBtn.disabled = true;
                }
            }

            // 重置分配弹窗状态
            function resetAssignModal() {
                const level1Select = document.getElementById('assign-department-level1');
                const level2Select = document.getElementById('assign-department-level2');
                const level3Select = document.getElementById('assign-department-level3');
                const ownerSelect = document.getElementById('assign-owner');
                const confirmBtn = document.getElementById('confirm-assign-btn');
                const selectionDisplay = document.getElementById('assign-selection-display');
                
                level1Select.value = '';
                level2Select.innerHTML = '<option value="">请先选择一级部门</option>';
                level2Select.disabled = true;
                level3Select.innerHTML = '<option value="">请先选择二级部门</option>';
                level3Select.disabled = true;
                ownerSelect.innerHTML = '<option value="">请先选择三级部门</option>';
                ownerSelect.disabled = true;
                confirmBtn.disabled = true;
                selectionDisplay.style.display = 'none';
            }


            addBtn.addEventListener('click', () => openLeadDrawer('新建线索'));
            closeBtn.addEventListener('click', closeLeadDrawer);
            cancelBtn.addEventListener('click', closeLeadDrawer);
            closeLogDrawerBtn.addEventListener('click', closeLogDrawer);
            closeAssignModalBtn.addEventListener('click', closeAssignModal);
            cancelAssignBtn.addEventListener('click', closeAssignModal);

            // 添加多级部门选择事件监听器
            document.getElementById('assign-department-level1').addEventListener('change', updateAssignSelection);
            document.getElementById('assign-department-level2').addEventListener('change', updateAssignSelection);
            document.getElementById('assign-department-level3').addEventListener('change', updateAssignSelection);
            document.getElementById('assign-owner').addEventListener('change', updateAssignSelection);

            addLogBtn.addEventListener('click', () => {
                const content = document.getElementById('new-log-content').value;
                if (content.trim() && currentViewingLeadId) {
                    addLog(currentViewingLeadId, content);
                    renderLogs(currentViewingLeadId);
                    document.getElementById('new-log-content').value = '';
                }
            });

            confirmAssignBtn.addEventListener('click', () => {
                const selectedLevel1 = document.getElementById('assign-department-level1').value;
                const selectedLevel2 = document.getElementById('assign-department-level2').value;
                const selectedLevel3 = document.getElementById('assign-department-level3').value;
                const newOwner = document.getElementById('assign-owner').value;
                
                if(currentAssigningLeadId && selectedLevel1 && selectedLevel2 && selectedLevel3 && newOwner) {
                    const lead = leads.find(l => l.id === currentAssigningLeadId);
                    if(lead.owner !== newOwner) {
                        const level1Dept = organizationStructure[selectedLevel1];
                        const level2Dept = level1Dept.children[selectedLevel2];
                        const level3Dept = level2Dept.children[selectedLevel3];
                        const member = level3Dept.members.find(m => m.name === newOwner);
                        const assignmentText = member ? `${level1Dept.name} > ${level2Dept.name} > ${level3Dept.name} - ${member.name} (${member.title})` : newOwner;
                        
                        lead.owner = newOwner;
                        addLog(lead.id, `将线索分配给 ${assignmentText}`);
                        renderLeadsTable();
                    }
                    closeAssignModal();
                }
            });

            saveBtn.addEventListener('click', () => {
                const id = document.getElementById('lead-id').value;
                const newLeadData = {
                    name: document.getElementById('lead-name').value,
                    phone: document.getElementById('lead-phone').value,
                    source: document.getElementById('lead-source').value,
                    businessModule: document.getElementById('lead-business-module').value,
                    status: document.getElementById('lead-status').value,
                    owner: document.getElementById('lead-owner').value,
                    remark: document.getElementById('lead-remark').value
                };

                if (!newLeadData.name || !newLeadData.phone) {
                    alert('请填写姓名和联系电话！');
                    return;
                }
                
                if (!newLeadData.businessModule) {
                    alert('请选择业务模块！');
                    document.getElementById('lead-business-module').focus();
                    return;
                }

                if (id) {
                    const index = leads.findIndex(l => l.id === id);
                    if (index > -1) {
                        leads[index] = { ...leads[index], ...newLeadData };
                    }
                } else {
                    const newLead = {
                        ...newLeadData,
                        id: `XS${new Date().getFullYear()}${(Math.random() * 1000).toFixed(0).padStart(3, '0')}`,
                        creator: '张三', 
                        creationMethod: '手动录入',
                        createdDate: new Date().toISOString().split('T')[0],
                        logs: []
                    };
                    leads.unshift(newLead);
                    addLog(newLead.id, `由 ${newLead.creator} 创建了该线索。`, newLead.creator);
                }
                
                renderLeadsTable();
                closeLeadDrawer();
            });

            tableBody.addEventListener('click', function(e) {
                const target = e.target;
                const row = target.closest('tr');
                if (!row) return;

                const id = row.dataset.id;
                const lead = leads.find(l => l.id === id);
                if (!lead) return;

                if (target.classList.contains('edit-lead-btn')) {
                    document.getElementById('lead-id').value = lead.id;
                    document.getElementById('lead-name').value = lead.name;
                    document.getElementById('lead-phone').value = lead.phone;
                    document.getElementById('lead-source').value = lead.source;
                    document.getElementById('lead-business-module').value = lead.businessModule || '';
                    document.getElementById('lead-status').value = lead.status;
                    document.getElementById('lead-owner').value = lead.owner;
                    document.getElementById('lead-remark').value = lead.remark || '';
                    openLeadDrawer('编辑线索');
                } else if (target.classList.contains('delete-lead-btn')) {
                    if (confirm(`确定要删除线索 "${lead.name}" 吗？`)) {
                        leads = leads.filter(l => l.id !== id);
                        renderLeadsTable();
                    }
                } else if (target.classList.contains('log-lead-btn')) {
                    openLogDrawer(lead);
                } else if (target.classList.contains('assign-lead-btn')) {
                    openAssignModal(lead);
                }
            });
            
            // 备注信息字符计数功能
            const leadRemarkTextarea = document.getElementById('lead-remark');
            const remarkCharCount = document.getElementById('remark-char-count');
            
            if (leadRemarkTextarea && remarkCharCount) {
                leadRemarkTextarea.addEventListener('input', function() {
                    const currentLength = this.value.length;
                    remarkCharCount.textContent = currentLength;
                    
                    // 当接近字符限制时改变颜色
                    if (currentLength >= 450) {
                        remarkCharCount.style.color = '#e74c3c';
                    } else if (currentLength >= 400) {
                        remarkCharCount.style.color = '#f39c12';
                    } else {
                        remarkCharCount.style.color = '#666';
                    }
                });
            }

            initializeLogs();
            renderLeadsTable();
        })();


        // --- Order Center Logic ---
        (function() {
            const tableBody = document.querySelector('#order-table tbody');
            const addBtn = document.getElementById('add-order-btn');
            const drawer = document.getElementById('order-drawer');
            const closeBtn = document.getElementById('close-order-drawer-btn');
            const cancelBtn = document.getElementById('cancel-order-btn');
            const saveBtn = document.getElementById('save-order-btn');
            const form = document.getElementById('order-form');
            const drawerTitle = document.getElementById('order-drawer-title');

            const detailsDrawer = document.getElementById('order-details-drawer');
            const closeDetailsDrawerBtn = document.getElementById('close-order-details-drawer-btn');
            const detailsContent = document.getElementById('order-details-content');
            const contractInfo = document.getElementById('contract-info');
            const orderLogTimeline = document.getElementById('order-log-timeline');

            function addOrderLog(orderId, content, author = '张三') {
                const order = orders.find(o => o.id === orderId);
                if (order) {
                    if(!order.logs) order.logs = [];
                     order.logs.unshift({
                        timestamp: getCurrentDateTime(),
                        author: author,
                        content: content
                    });
                }
            }

            function renderOrdersTable() {
                tableBody.innerHTML = '';
                orders.forEach(order => {
                    const row = document.createElement('tr');
                    row.dataset.id = order.id;
                    row.innerHTML = `
                        <td>${order.id}</td>
                        <td>${order.type}</td>
                        <td>${order.client}</td>
                        <td>${order.service}</td>
                        <td>${order.amount.toLocaleString('zh-CN')}</td>
                        <td><span class="status-badge ${ order.status === '待履约' ? 'status-fulfilling' : (order.status === '已完成' ? 'status-completed' : (order.status === '待支付' ? 'status-unpaid' : 'status-closed')) }">${order.status}</span></td>
                        <td><span class="status-badge ${ order.paymentStatus === '已支付' ? 'status-paid' : 'status-unpaid' }">${order.paymentStatus}</span></td>
                        <td>${order.createdDate}</td>
                        <td class="action-buttons">
                            <button class="btn btn-primary view-details-btn" style="padding: 5px 8px; font-size: 12px;">详情</button>
                            <button class="btn btn-outline edit-order-btn" style="padding: 5px 8px; font-size: 12px;">编辑</button>
                            <button class="btn btn-danger-outline delete-order-btn" style="padding: 5px 8px; font-size: 12px;">删除</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            }

            function openDrawer(title = '新建订单') {
                drawerTitle.innerText = title;
                drawer.classList.add('open');
                overlay.classList.add('active');
            }

            function closeDrawer() {
                drawer.classList.remove('open');
                if(!detailsDrawer.classList.contains('open')) {
                    overlay.classList.remove('active');
                }
                form.reset();
                document.getElementById('order-id').value = '';
                toggleTypeSpecificFields(); // Hide all specific fields on close
            }

            openOrderDetailsDrawerFunc = function(order) {
                document.getElementById('order-details-drawer-title').innerText = `订单详情 - ${order.id}`;
                
                let detailsHtml = `
                    <p><strong>客户/学员:</strong> ${order.client}</p>
                    <p><strong>服务名称:</strong> ${order.service}</p>
                    <p><strong>订单金额:</strong> ${order.amount.toLocaleString('zh-CN')} 元</p>
                    <p><strong>创建时间:</strong> ${order.createdDate}</p>
                `;
                
                if (order.type === '高校实践' && order.details) {
                    detailsHtml += `
                        <p><strong>学校:</strong> ${order.details.school || 'N/A'}</p>
                        <p><strong>参与人数:</strong> ${order.details.studentCount || 'N/A'}</p>
                        <p><strong>开始日期:</strong> ${order.details.startDate || 'N/A'}</p>
                    `;
                } else if (order.type === '家政服务' && order.details) {
                     detailsHtml += `
                        <p><strong>服务地址:</strong> ${order.details.address || 'N/A'}</p>
                        <p><strong>指派阿姨:</strong> ${order.details.auntie || 'N/A'}</p>
                        <p><strong>开始日期:</strong> ${order.details.startDate || 'N/A'}</p>
                    `;
                } else if (order.type === '企业培训' && order.details) {
                     detailsHtml += `
                        <p><strong>指派讲师:</strong> ${order.details.trainer || 'N/A'}</p>
                        <p><strong>参与人数:</strong> ${order.details.traineeCount || 'N/A'}</p>
                        <p><strong>培训日期:</strong> ${order.details.trainingDate || 'N/A'}</p>
                    `;
                }

                detailsContent.innerHTML = detailsHtml;

                contractInfo.innerHTML = `
                    <p>合同状态: <span class="status-badge status-approved">已签署</span></p>
                    <p>合同编号: <a href="#">HC-HT-20240620-001</a></p>
                `;

                if(!order.logs || order.logs.length === 0) {
                    addOrderLog(order.id, '订单已创建', '系统');
                }

                orderLogTimeline.innerHTML = (order.logs || []).map(log => `
                    <li class="timeline-item">
                        <div class="timeline-time">${log.timestamp}</div>
                        <div class="timeline-content"><strong>${log.author}:</strong> ${log.content}</div>
                    </li>
                `).join('');


                detailsDrawer.classList.add('open');
                overlay.classList.add('active');
            }

            function closeDetailsDrawer() {
                detailsDrawer.classList.remove('open');
                if(!drawer.classList.contains('open')) {
                   overlay.classList.remove('active');
                }
            }
            
            function toggleTypeSpecificFields(orderType) {
                document.querySelectorAll('#order-type-specific-fields .specific-fields').forEach(fieldSet => {
                    fieldSet.style.display = (fieldSet.id === `fields-${orderType}`) ? 'block' : 'none';
                });
            }

            document.getElementById('order-type').addEventListener('change', (e) => toggleTypeSpecificFields(e.target.value));

            addBtn.addEventListener('click', () => openDrawer('新建订单'));
            closeBtn.addEventListener('click', closeDrawer);
            cancelBtn.addEventListener('click', closeDrawer);
            closeDetailsDrawerBtn.addEventListener('click', closeDetailsDrawer);

            saveBtn.addEventListener('click', () => {
                const id = document.getElementById('order-id').value;
                const orderData = {
                    type: document.getElementById('order-type').value,
                    client: document.getElementById('order-client').value,
                    service: document.getElementById('order-service').value,
                    amount: parseInt(document.getElementById('order-amount').value),
                    status: document.getElementById('order-status').value,
                    paymentStatus: document.getElementById('order-payment-status').value,
                };
                
                if (!orderData.client || !orderData.service || isNaN(orderData.amount)) {
                    alert('请填写必填项！');
                    return;
                }

                if (id) {
                    const index = orders.findIndex(o => o.id === id);
                    if (index > -1) {
                        orders[index] = { ...orders[index], ...orderData };
                        addOrderLog(id, "订单信息已更新");
                    }
                } else {
                    const newOrder = { 
                        ...orderData,
                        id: `DD${new Date().getFullYear()}${(Math.random() * 100000).toFixed(0).padStart(5, '0')}`,
                        createdDate: new Date().toISOString().split('T')[0],
                        details: {},
                        logs: []
                    };
                    orders.unshift(newOrder);
                    addOrderLog(newOrder.id, "创建了新订单", '张三');
                }
                
                renderOrdersTable();
                closeDrawer();
            });

            tableBody.addEventListener('click', function(e) {
                const target = e.target;
                const row = target.closest('tr');
                if (!row) return;

                const id = row.dataset.id;
                const order = orders.find(o => o.id === id);
                if (!order) return;

                if (target.classList.contains('edit-order-btn')) {
                    document.getElementById('order-id').value = order.id;
                    document.getElementById('order-type').value = order.type;
                    document.getElementById('order-client').value = order.client;
                    document.getElementById('order-service').value = order.service;
                    document.getElementById('order-amount').value = order.amount;
                    document.getElementById('order-status').value = order.status;
                    document.getElementById('order-payment-status').value = order.paymentStatus;
                    toggleTypeSpecificFields(order.type);
                    openDrawer('编辑订单');
                } else if (target.classList.contains('delete-order-btn')) {
                    if (confirm(`确定要删除订单 "${order.id}" 吗？`)) {
                        orders = orders.filter(o => o.id !== id);
                        renderOrdersTable();
                    }
                } else if (target.classList.contains('view-details-btn')) {
                    openOrderDetailsDrawerFunc(order);
                }
            });

            renderOrdersTable();
        })();

        // --- Resource Center Logic ---
        (function() {
            const resourceTabs = document.getElementById('resource-tabs');
            const resourceTabLinks = resourceTabs.querySelectorAll('.tab');
            const resourceTabContents = document.querySelectorAll('#resource-center .tab-content');
            
            const partnersTableBody = document.querySelector('#partners-table tbody');
            const teachersTableBody = document.querySelector('#teachers-table tbody');
            const auntiesTableBody = document.querySelector('#aunties-table tbody');
            const coursesTableBody = document.querySelector('#courses-table tbody');

            const addBtn = document.getElementById('add-resource-btn');
            const drawer = document.getElementById('resource-drawer');
            const closeBtn = document.getElementById('close-resource-drawer-btn');
            const cancelBtn = document.getElementById('cancel-resource-btn');
            const saveBtn = document.getElementById('save-resource-btn');
            const drawerTitle = document.getElementById('resource-drawer-title');
            const courseDetailsDrawer = document.getElementById('course-details-drawer');
            const closeCourseDetailsBtn1 = document.getElementById('close-course-details-drawer-btn');
            const closeCourseDetailsBtn2 = document.getElementById('close-course-details-drawer-btn-2');

            const auntieDetailsDrawer = document.getElementById('auntie-details-drawer');
            const closeAuntieDetailsBtn1 = document.getElementById('close-auntie-details-drawer-btn');
            const closeAuntieDetailsBtn2 = document.getElementById('close-auntie-details-drawer-btn-2');


            let currentResource = 'partners'; // default
            let editingResourceId = null;
            let currentViewingAuntieId = null;

            const forms = {
                partners: document.getElementById('partner-form'),
                teachers: document.getElementById('teacher-form'),
                aunties: document.getElementById('auntie-form'),
                courses: document.getElementById('course-form'),
            };

            function renderPartnersTable() {
                const typeFilter = document.getElementById('partner-filter-type').value;
                const statusFilter = document.getElementById('partner-filter-status').value;
                const nameFilter = document.getElementById('partner-filter-name').value.toLowerCase();

                const filtered = partners.filter(p => {
                    return (!typeFilter || p.type === typeFilter) &&
                           (!statusFilter || p.status === statusFilter) &&
                           (!nameFilter || p.name.toLowerCase().includes(nameFilter));
                });

                partnersTableBody.innerHTML = '';
                filtered.forEach(p => {
                    const row = document.createElement('tr');
                    row.dataset.id = p.id;
                    row.innerHTML = `
                        <td>${p.id}</td>
                        <td>${p.name}</td>
                        <td>${p.type}</td>
                        <td><span class="status-badge ${p.status === '合作中' ? 'status-approved' : (p.status === '待审核' ? 'status-pending' : 'status-rejected')}">${p.status}</span></td>
                        <td>${p.risk}</td>
                        <td>${p.owner}</td>
                        <td>${p.createdDate}</td>
                        <td class="action-buttons">
                            <button class="btn btn-primary edit-btn" style="padding: 5px 8px; font-size: 12px;">编辑</button>
                            <button class="btn btn-danger-outline delete-btn" style="padding: 5px 8px; font-size: 12px;">删除</button>
                        </td>`;
                    partnersTableBody.appendChild(row);
                });
            }
            
            function renderTeachersTable() {
                const typeFilter = document.getElementById('teacher-filter-type').value;
                const statusFilter = document.getElementById('teacher-filter-status').value;
                const keywordFilter = document.getElementById('teacher-filter-keyword').value.toLowerCase();
                
                const filtered = teachers.filter(t => {
                    return (!typeFilter || t.type === typeFilter) &&
                           (!statusFilter || t.status === statusFilter) &&
                           (!keywordFilter || t.name.toLowerCase().includes(keywordFilter) || t.field.toLowerCase().includes(keywordFilter));
                });

                teachersTableBody.innerHTML = '';
                filtered.forEach(t => {
                    const row = document.createElement('tr');
                    row.dataset.id = t.id;
                    row.innerHTML = `
                        <td>${t.id}</td>
                        <td>${t.name}</td>
                        <td>${t.type}</td>
                        <td>${t.field}</td>
                        <td>${t.phone}</td>
                        <td><span class="status-badge ${t.status === '合作中' ? 'status-approved' : 'status-pending'}">${t.status}</span></td>
                        <td class="action-buttons">
                            <button class="btn btn-primary edit-btn" style="padding: 5px 8px; font-size: 12px;">编辑</button>
                            <button class="btn btn-danger-outline delete-btn" style="padding: 5px 8px; font-size: 12px;">删除</button>
                        </td>`;
                    teachersTableBody.appendChild(row);
                });
            }

            function renderAuntiesTable() {
                const levelFilter = document.getElementById('auntie-filter-level').value;
                const serviceTypeFilter = document.getElementById('auntie-filter-servicetype').value;
                const statusFilter = document.getElementById('auntie-filter-status').value;
                const keywordFilter = document.getElementById('auntie-filter-keyword').value.toLowerCase();

                const filtered = aunties.filter(a => {
                    return (!levelFilter || a.level === levelFilter) &&
                           (!serviceTypeFilter || a.serviceType === serviceTypeFilter) &&
                           (!statusFilter || a.status === statusFilter) &&
                           (!keywordFilter || a.name.toLowerCase().includes(keywordFilter));
                });

                auntiesTableBody.innerHTML = '';
                filtered.forEach(a => {
                    const certStatus = a.certificates && a.certificates.length > 0
                        ? (a.certificates.every(c => c.verified) 
                            ? `<span style="color:var(--success); font-weight: 500;">全部已认证</span>` 
                            : `<span style="color:var(--warning); font-weight: 500;">待认证 (${a.certificates.filter(c=>!c.verified).length})</span>`)
                        : '未上传';

                    const row = document.createElement('tr');
                    row.dataset.id = a.id;
                    row.innerHTML = `
                        <td>${a.id}</td>
                        <td>
                            <div style="display: flex; align-items: center;">
                                <img src="${a.avatar || 'https://placehold.co/40x40/cccccc/ffffff/png?text=无'}" alt="${a.name}" style="width: 32px; height: 32px; border-radius: 50%; margin-right: 10px;">
                                ${a.name}
                            </div>
                        </td>
                        <td>${a.level}</td>
                        <td>${a.workYears || 'N/A'} 年</td>
                        <td>${a.serviceType}</td>
                        <td><span class="status-badge ${a.verificationStatus === '已审核' ? 'status-approved' : 'status-pending'}">${a.verificationStatus}</span></td>
                        <td><span class="status-badge ${a.status === '服务中' ? 'status-approved' : 'status-pending'}">${a.status}</span></td>
                        <td>${certStatus}</td>
                        <td class="action-buttons">
                            <button class="btn btn-primary auntie-view-details-btn" style="padding: 5px 8px; font-size: 12px;">详情</button>
                            <button class="btn btn-outline edit-btn" style="padding: 5px 8px; font-size: 12px;">编辑</button>
                            <button class="btn btn-danger-outline delete-btn" style="padding: 5px 8px; font-size: 12px;">删除</button>
                        </td>`;
                    auntiesTableBody.appendChild(row);
                });
            }

            function renderCoursesTable() {
                const categoryFilter = document.getElementById('course-filter-category').value;
                const deliveryFilter = document.getElementById('course-filter-delivery').value;
                const statusFilter = document.getElementById('course-filter-status').value;
                const keywordFilter = document.getElementById('course-filter-keyword').value.toLowerCase();

                const filtered = courses.filter(c => {
                    return (!categoryFilter || c.category === categoryFilter) &&
                           (!deliveryFilter || c.deliveryMethod === deliveryFilter) &&
                           (!statusFilter || c.status === statusFilter) &&
                           (!keywordFilter || c.name.toLowerCase().includes(keywordFilter));
                });
                
                coursesTableBody.innerHTML = '';
                filtered.forEach(c => {
                    const row = document.createElement('tr');
                    row.dataset.id = c.id;
                    row.innerHTML = `
                        <td>${c.id}</td>
                        <td>${c.name}</td>
                        <td>${c.category}</td>
                        <td>${c.deliveryMethod}</td>
                        <td>${c.price.toLocaleString('zh-CN')}</td>
                        <td><span class="status-badge ${c.status === '已上架' ? 'status-approved' : 'status-pending'}">${c.status}</span></td>
                        <td class="action-buttons">
                            <button class="btn btn-primary view-details-btn" style="padding: 5px 8px; font-size: 12px;">详情</button>
                            <button class="btn btn-outline edit-btn" style="padding: 5px 8px; font-size: 12px;">编辑</button>
                            <button class="btn btn-danger-outline delete-btn" style="padding: 5px 8px; font-size: 12px;">删除</button>
                        </td>`;
                    coursesTableBody.appendChild(row);
                });
            }

            function switchTab(targetTab) {
                currentResource = targetTab;
                resourceTabLinks.forEach(tab => tab.classList.toggle('active', tab.dataset.tab === targetTab));
                resourceTabContents.forEach(content => content.style.display = content.id === `tab-${targetTab}` ? 'block' : 'none');
                
                const addBtnTextMap = { partners: '新增合作伙伴', teachers: '新增师资', aunties: '新增阿姨', courses: '新增课程' };
                addBtn.innerHTML = `<i class="fas fa-plus"></i> ${addBtnTextMap[targetTab] || '新增资源'}`;
                
                 switch (targetTab) {
                    case 'partners': renderPartnersTable(); break;
                    case 'teachers': renderTeachersTable(); break;
                    case 'aunties': renderAuntiesTable(); break;
                    case 'courses': renderCoursesTable(); break;
                }
            }

            function openDrawer(data = null) {
                const titleMap = {
                    partners: { add: '新增合作伙伴', edit: '编辑合作伙伴' },
                    teachers: { add: '新增师资', edit: '编辑师资' },
                    aunties: { add: '新增阿姨', edit: '编辑阿姨' },
                    courses: { add: '新增课程', edit: '编辑课程' },
                };
                drawerTitle.innerText = titleMap[currentResource][data ? 'edit' : 'add'];

                Object.values(forms).forEach(form => form.style.display = 'none');
                forms[currentResource].style.display = 'block';
                forms[currentResource].reset();
                editingResourceId = null;

                if (data) {
                    editingResourceId = data.id;
                    // Populate form based on currentResource
                    switch(currentResource) {
                        case 'partners':
                            document.getElementById('partner-id').value = data.id;
                            document.getElementById('partner-name').value = data.name;
                            document.getElementById('partner-type').value = data.type;
                            document.getElementById('partner-status').value = data.status;
                            document.getElementById('partner-risk').value = data.risk;
                            document.getElementById('partner-owner').value = data.owner;
                            break;
                        case 'teachers':
                            document.getElementById('teacher-id').value = data.id;
                            document.getElementById('teacher-name').value = data.name;
                            document.getElementById('teacher-type').value = data.type;
                            document.getElementById('teacher-field').value = data.field;
                            document.getElementById('teacher-phone').value = data.phone;
                            document.getElementById('teacher-status').value = data.status;
                            break;
                        case 'aunties':
                            document.getElementById('auntie-id').value = data.id;
                            document.getElementById('auntie-name').value = data.name;
                            document.getElementById('auntie-age').value = data.age;
                            document.getElementById('auntie-workYears').value = data.workYears;
                            document.getElementById('auntie-level').value = data.level;
                            document.getElementById('auntie-service-type').value = data.serviceType;
                            document.getElementById('auntie-status').value = data.status;
                            document.getElementById('auntie-verificationStatus').value = data.verificationStatus;
                            document.getElementById('auntie-nativePlace').value = data.nativePlace;
                            document.getElementById('auntie-education').value = data.education;
                            
                            const avatarUrl = data.avatar || 'https://placehold.co/60x60/cccccc/ffffff/png?text=头像';
                            document.getElementById('auntie-avatar-preview').src = avatarUrl;
                            document.getElementById('auntie-avatar').value = avatarUrl;
                            document.getElementById('auntie-avatar-upload').value = null;

                            document.getElementById('auntie-talentPortrait').value = data.talentPortrait;
                            renderCertificateEditor(data.certificates || []);
                            break;
                        case 'courses':
                             document.getElementById('course-id').value = data.id;
                             document.getElementById('course-name').value = data.name;
                             document.getElementById('course-category').value = data.category;
                             document.getElementById('course-delivery-method').value = data.deliveryMethod;
                             document.getElementById('course-teacher').value = data.teacher;
                             document.getElementById('course-duration').value = data.duration;
                             document.getElementById('course-price').value = data.price;
                             document.getElementById('course-status').value = data.status;
                             document.getElementById('course-target-audience').value = data.targetAudience;
                             document.getElementById('course-syllabus').value = data.syllabus;
                             document.getElementById('course-description').value = data.description;
                            break;
                    }
                }
                drawer.classList.add('open');
                overlay.classList.add('active');
            }

            function closeDrawer() {
                drawer.classList.remove('open');
                if(!courseDetailsDrawer.classList.contains('open') && !auntieDetailsDrawer.classList.contains('open')) {
                    overlay.classList.remove('active');
                }
            }

            function openCourseDetailsDrawer(course) {
                document.getElementById('course-details-drawer-title').innerText = `课程详情 - ${course.name}`;
                const content = `
                    <div class="section">
                        <h4 class="section-title">基本信息</h4>
                        <p><strong>课程名称:</strong> ${course.name}</p>
                        <p><strong>课程分类:</strong> ${course.category}</p>
                        <p><strong>授课形式:</strong> ${course.deliveryMethod}</p>
                        <p><strong>关联讲师:</strong> ${course.teacher}</p>
                        <p><strong>课程时长:</strong> ${course.duration} 小时</p>
                        <p><strong>课程价格:</strong> ${course.price > 0 ? course.price.toLocaleString('zh-CN') + ' 元' : '免费'}</p>
                        <p><strong>课程状态:</strong> ${course.status}</p>
                    </div>
                     <div class="section">
                        <h4 class="section-title">详细介绍</h4>
                        <p><strong>适宜人群:</strong><br>${course.targetAudience.replace(/\n/g, '<br>')}</p>
                        <hr style="border: none; border-top: 1px solid #eee; margin: 15px 0;">
                        <p><strong>课程大纲:</strong><br>${course.syllabus.replace(/\n/g, '<br>')}</p>
                        <hr style="border: none; border-top: 1px solid #eee; margin: 15px 0;">
                        <p><strong>详情介绍:</strong><br>${course.description.replace(/\n/g, '<br>')}</p>
                    </div>
                `;
                document.getElementById('course-details-content').innerHTML = content;
                courseDetailsDrawer.classList.add('open');
                overlay.classList.add('active');
            }
            function closeCourseDetailsDrawer() {
                courseDetailsDrawer.classList.remove('open');
                if(!drawer.classList.contains('open') && !auntieDetailsDrawer.classList.contains('open')) {
                    overlay.classList.remove('active');
                }
            }

            function openAuntieDetailsDrawer(auntie) {
                currentViewingAuntieId = auntie.id;
                document.getElementById('auntie-details-drawer-title').innerText = `阿姨介绍 - ${auntie.name}`;
                
                const certificatesHtml = (auntie.certificates && auntie.certificates.length > 0)
                    ? auntie.certificates.map(cert => `
                        <div class="cert-item" style="text-align: center; background-color: #f9f9f9; padding: 10px; border-radius: 6px;">
                            <img src="${cert.img}" alt="${cert.name}" onerror="this.src='https://placehold.co/120x85/cccccc/ffffff/png?text=错误';" style="width: 120px; height: 85px; border-radius: 4px; object-fit: cover; border: 1px solid #ddd; margin-bottom: 5px;">
                            <p style="font-size: 13px; margin: 5px 0 0 0;">
                                ${cert.name}<br>
                                <span class="status-badge ${cert.verified ? 'status-approved' : 'status-pending'}" style="margin-top: 5px;">${cert.verified ? '已认证' : '待认证'}</span>
                            </p>
                        </div>`).join('')
                    : '<p>暂无证书信息</p>';
                
                const historyHtml = (auntie.serviceHistory && auntie.serviceHistory.length > 0)
                    ? auntie.serviceHistory.map(h => {
                        const order = orders.find(o => o.id === h.orderId);
                        if (!order) return `<div class="history-item" style="background-color: #fff; border-radius: 4px; padding: 10px; margin-bottom: 10px; border: 1px solid #eee;"><p>关联订单信息丢失 (ID: ${h.orderId})</p></div>`;
                        
                        return `
                        <div class="history-item" style="background-color: #fff; border-radius: 4px; padding: 15px; margin-bottom: 10px; border: 1px solid #eee;">
                            <p style="margin:0 0 5px 0;"><strong>订单号:</strong> ${order.id}</p>
                            <p style="margin:0 0 5px 0;"><strong>服务内容:</strong> ${order.service}</p>
                            <p style="margin:0 0 10px 0;"><strong>完成日期:</strong> ${order.createdDate}</p>
                            <button class="btn btn-outline view-order-details-from-auntie-btn" data-order-id="${order.id}" style="padding: 5px 10px; font-size: 12px;">查看订单详情</button>
                        </div>
                        `;
                    }).join('')
                    : '<p>暂无服务历史</p>';

                const reviewsHtml = (auntie.customerReviews && auntie.customerReviews.length > 0)
                    ? auntie.customerReviews.map(r => `
                        <div class="review-item" style="background-color: #f9f9f9; border-radius: 4px; padding: 10px; margin-bottom: 10px;">
                            <p style="margin: 0;"><strong>${r.customer} (评分: ${'★'.repeat(r.rating || 5)}${'☆'.repeat(5 - (r.rating || 5))})</strong></p>
                            <p style="margin: 5px 0 0; color: #555;">${r.comment}</p>
                        </div>`).join('')
                    : '<p>暂无客户评价</p>';


                const content = `
                    <div class="section">
                        <div style="display: flex; align-items: flex-start; gap: 20px;">
                            <img src="${auntie.avatar || 'https://placehold.co/100x100/cccccc/ffffff/png?text=无'}" alt="${auntie.name}" style="width: 100px; height: 100px; border-radius: 8px;">
                            <div style="flex: 1;">
                                <h4 class="section-title" style="margin-top: 0; border: none; padding-bottom: 5px;">${auntie.name} <span style="font-size: 14px; color: #555; font-weight: normal;">(${auntie.age}岁)</span></h4>
                                <p style="margin: 0; color: #666;">${auntie.nativePlace} | ${auntie.education} | ${auntie.workYears}年经验</p>
                                <div style="margin-top: 10px;">
                                    <span class="status-badge status-approved">${auntie.level}</span>
                                    <span class="status-badge status-approved">${auntie.serviceType}</span>
                                    <span class="status-badge ${auntie.verificationStatus === '已审核' ? 'status-approved' : 'status-pending'}">${auntie.verificationStatus}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="section">
                        <h4 class="section-title">介绍</h4>
                        <p>${auntie.talentPortrait || '暂无简介'}</p>
                    </div>
                    <div class="section">
                         <h4 class="section-title" style="margin-bottom: 10px; border: none;">资质证书</h4>
                         <div class="certificates-list" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)); gap: 15px;">${certificatesHtml}</div>
                    </div>
                    <div class="section">
                        <h4 class="section-title">服务历史</h4>
                        <div class="history-list">${historyHtml}</div>
                    </div>
                    <div class="section">
                        <h4 class="section-title">客户评价</h4>
                        <div class="reviews-list">${reviewsHtml}</div>
                    </div>
                `;
                document.getElementById('auntie-details-content').innerHTML = content;
                auntieDetailsDrawer.classList.add('open');
                overlay.classList.add('active');
            }

            function closeAuntieDetailsDrawer() {
                auntieDetailsDrawer.classList.remove('open');
                if(!drawer.classList.contains('open') && !courseDetailsDrawer.classList.contains('open')) {
                   overlay.classList.remove('active');
                }
                currentViewingAuntieId = null;
                renderAuntiesTable();
            }

            function renderCertificateEditor(certificates = []) {
                const editorContainer = document.getElementById('auntie-certificates-editor');
                editorContainer.innerHTML = '';
                certificates.forEach((cert, index) => {
                    const certRow = document.createElement('div');
                    certRow.className = 'cert-editor-row';
                    certRow.style.display = 'flex';
                    certRow.style.alignItems = 'center';
                    certRow.style.gap = '10px';
                    certRow.style.marginBottom = '10px';
                    certRow.innerHTML = `
                        <input type="text" value="${cert.name}" class="form-control cert-name-input" placeholder="证书名称">
                        <div style="display: flex; align-items: center; gap: 5px;">
                           <input type="checkbox" id="cert-verified-${index}" class="cert-verified-checkbox" ${cert.verified ? 'checked' : ''}>
                           <label for="cert-verified-${index}">已认证</label>
                        </div>
                        <button type="button" class="btn btn-danger-outline remove-cert-btn" style="padding: 5px 10px; font-size: 12px;">移除</button>
                    `;
                    editorContainer.appendChild(certRow);
                });
            }
            
            // Event Listeners
            resourceTabLinks.forEach(tab => tab.addEventListener('click', () => switchTab(tab.dataset.tab)));
            addBtn.addEventListener('click', () => openDrawer());
            closeBtn.addEventListener('click', closeDrawer);
            cancelBtn.addEventListener('click', closeDrawer);
            closeCourseDetailsBtn1.addEventListener('click', closeCourseDetailsDrawer);
            closeCourseDetailsBtn2.addEventListener('click', closeCourseDetailsDrawer);
            closeAuntieDetailsBtn1.addEventListener('click', closeAuntieDetailsDrawer);
            closeAuntieDetailsBtn2.addEventListener('click', closeAuntieDetailsDrawer);

            document.getElementById('auntie-avatar-upload').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(event) {
                        document.getElementById('auntie-avatar-preview').src = event.target.result;
                        document.getElementById('auntie-avatar').value = event.target.result;
                    }
                    reader.readAsDataURL(file);
                }
            });

            document.getElementById('auntie-details-content').addEventListener('click', function(e) {
                const target = e.target.closest('button');
                if (!target) return;
                
                if (target.classList.contains('view-order-details-from-auntie-btn')) {
                    const orderId = target.dataset.orderId;
                    const order = orders.find(o => o.id === orderId);
                    if(order && typeof openOrderDetailsDrawerFunc === 'function') {
                        openOrderDetailsDrawerFunc(order);
                    } else {
                        alert('无法打开订单详情。');
                    }
                }
            });

            document.getElementById('resource-drawer').addEventListener('click', function(e){
                if(e.target.id === 'add-new-cert-btn') {
                    const editorContainer = document.getElementById('auntie-certificates-editor');
                    const newIndex = editorContainer.children.length;
                    const certRow = document.createElement('div');
                    certRow.className = 'cert-editor-row';
                    certRow.style.display = 'flex';
                    certRow.style.alignItems = 'center';
                    certRow.style.gap = '10px';
                    certRow.style.marginBottom = '10px';
                    certRow.innerHTML = `
                        <input type="text" value="" class="form-control cert-name-input" placeholder="证书名称">
                        <div style="display: flex; align-items: center; gap: 5px;">
                           <input type="checkbox" id="cert-verified-${newIndex}" class="cert-verified-checkbox">
                           <label for="cert-verified-${newIndex}">已认证</label>
                        </div>
                        <button type="button" class="btn btn-danger-outline remove-cert-btn" style="padding: 5px 10px; font-size: 12px;">移除</button>
                    `;
                    editorContainer.appendChild(certRow);
                }
                if(e.target.classList.contains('remove-cert-btn')) {
                    e.target.closest('.cert-editor-row').remove();
                }
            });


            saveBtn.addEventListener('click', () => {
                let data, index, collection;
                switch (currentResource) {
                    case 'partners':
                        data = {
                            id: editingResourceId || `P${(Math.random() * 1000).toFixed(0).padStart(3,'0')}`,
                            name: document.getElementById('partner-name').value,
                            type: document.getElementById('partner-type').value,
                            status: document.getElementById('partner-status').value,
                            risk: document.getElementById('partner-risk').value,
                            owner: document.getElementById('partner-owner').value,
                        };
                        collection = partners;
                        break;
                    case 'teachers':
                        data = {
                            id: editingResourceId || `T${(Math.random() * 1000).toFixed(0).padStart(3,'0')}`,
                            name: document.getElementById('teacher-name').value,
                            type: document.getElementById('teacher-type').value,
                            field: document.getElementById('teacher-field').value,
                            phone: document.getElementById('teacher-phone').value,
                            status: document.getElementById('teacher-status').value,
                        };
                         collection = teachers;
                        break;
                    case 'aunties':
                        const updatedCertificates = [];
                        document.querySelectorAll('#auntie-certificates-editor .cert-editor-row').forEach(row => {
                            const nameInput = row.querySelector('.cert-name-input');
                            const verifiedCheckbox = row.querySelector('.cert-verified-checkbox');
                            if (nameInput && nameInput.value.trim()) {
                                updatedCertificates.push({
                                    name: nameInput.value.trim(),
                                    verified: verifiedCheckbox.checked,
                                    img: `https://placehold.co/120x85/7f8c8d/ffffff/png?text=${encodeURIComponent(nameInput.value.trim())}`
                                });
                            }
                        });
                        data = {
                            id: editingResourceId || `A${(Math.random() * 1000).toFixed(0).padStart(3,'0')}`,
                            name: document.getElementById('auntie-name').value,
                            age: parseInt(document.getElementById('auntie-age').value),
                            workYears: parseInt(document.getElementById('auntie-workYears').value),
                            level: document.getElementById('auntie-level').value,
                            serviceType: document.getElementById('auntie-service-type').value,
                            status: document.getElementById('auntie-status').value,
                            verificationStatus: document.getElementById('auntie-verificationStatus').value,
                            nativePlace: document.getElementById('auntie-nativePlace').value,
                            education: document.getElementById('auntie-education').value,
                            avatar: document.getElementById('auntie-avatar').value,
                            talentPortrait: document.getElementById('auntie-talentPortrait').value,
                            certificates: updatedCertificates,
                        };
                        collection = aunties;
                        break;
                    case 'courses':
                         data = {
                            id: editingResourceId || `C${(Math.random() * 1000).toFixed(0).padStart(3,'0')}`,
                            name: document.getElementById('course-name').value,
                            category: document.getElementById('course-category').value,
                            deliveryMethod: document.getElementById('course-delivery-method').value,
                            teacher: document.getElementById('course-teacher').value,
                            duration: parseInt(document.getElementById('course-duration').value),
                            price: parseFloat(document.getElementById('course-price').value),
                            status: document.getElementById('course-status').value,
                            targetAudience: document.getElementById('course-target-audience').value,
                            syllabus: document.getElementById('course-syllabus').value,
                            description: document.getElementById('course-description').value,
                        };
                        collection = courses;
                        break;
                }
                
                if (editingResourceId) {
                    index = collection.findIndex(item => item.id === editingResourceId);
                    collection[index] = { ...collection[index], ...data };
                } else {
                    collection.unshift({ ...data, createdDate: new Date().toISOString().split('T')[0]});
                }
                
                switchTab(currentResource);
                closeDrawer();
            });

            document.getElementById('resource-center').addEventListener('click', (e) => {
                const target = e.target;
                const row = target.closest('tr');
                if (!row) return;
                const id = row.dataset.id;
                
                let collection, data;
                switch(currentResource) {
                    case 'partners': collection = partners; break;
                    case 'teachers': collection = teachers; break;
                    case 'aunties': collection = aunties; break;
                    case 'courses': collection = courses; break;
                }
                data = collection.find(item => item.id === id);

                if (target.classList.contains('edit-btn')) {
                    openDrawer(data);
                } else if (target.classList.contains('delete-btn')) {
                    if (confirm(`确定要删除 "${data.name}" 吗？`)) {
                        const index = collection.findIndex(item => item.id === id);
                        collection.splice(index, 1);
                        switchTab(currentResource);
                    }
                } else if (target.classList.contains('view-details-btn') && currentResource === 'courses') {
                    openCourseDetailsDrawer(data);
                } else if (target.classList.contains('auntie-view-details-btn')) {
                    openAuntieDetailsDrawer(data);
                }
            });

            // Initial render
            renderPartnersTable();
        })();

        // --- 高校实践模块逻辑 ---

        const practiceCenterContainer = document.getElementById('practice-center');
        const practiceProjectDrawer = document.getElementById('practice-project-drawer');
        const practiceClassDrawer = document.getElementById('practice-class-drawer');
        const scheduleItemDrawer = document.getElementById('schedule-item-drawer');

        let currentEditingProject = null;
        let currentEditingClass = null;

        function openDrawer(drawerId) {
            const drawer = document.getElementById(drawerId);
            if(drawer) {
                drawer.classList.add('open');
                overlay.classList.add('active');
            }
        }

        function closeDrawer(drawerId) {
            const drawer = document.getElementById(drawerId);
            if(drawer) {
                drawer.classList.remove('open');
                // Only hide overlay if no other drawers are open
                if (document.querySelectorAll('.drawer.open').length === 0) {
                    overlay.classList.remove('active');
                }
            }
        }

        // 渲染项目列表视图
        function renderPracticeProjectList() {
            practiceCenterContainer.innerHTML = `
                <div class="dashboard-header">
                    <h2><i class="fas fa-graduation-cap"></i> 高校实践项目管理</h2>
                    <button class="btn btn-primary" id="add-practice-project-btn"><i class="fas fa-plus"></i> 新建实践项目</button>
                </div>
                <div class="card">
                    <div class="card-body" style="padding: 0;">
                        <div class="table-wrapper">
                            <table class="data-table" id="practice-projects-table">
                                <thead>
                                    <tr>
                                        <th>项目名称</th>
                                        <th>合作高校</th>
                                        <th>班次数</th>
                                        <th>报名总览</th>
                                        <th>项目状态</th>
                                        <th>项目负责人</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${practiceProjects.map(p => `
                                        <tr>
                                            <td>${p.name}</td>
                                            <td>${p.school}</td>
                                            <td>${p.classes.length}</td>
                                            <td>${p.confirmedStudents}/${p.totalCapacity}</td>
                                            <td><span class="status-badge status-approved">${p.status}</span></td>
                                            <td>${p.owner}</td>
                                            <td class="action-buttons">
                                                <button class="btn btn-primary btn-sm manage-project-btn" data-id="${p.id}">管理</button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            // 绑定事件
            document.getElementById('add-practice-project-btn').addEventListener('click', () => {
                currentEditingProject = null;
                document.getElementById('practice-project-form').reset();
                document.getElementById('practice-project-drawer-title').textContent = '新建实践项目';
                openDrawer('practice-project-drawer');
            });

            document.querySelectorAll('.manage-project-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const projectId = e.target.dataset.id;
                    currentEditingProject = practiceProjects.find(p => p.id === projectId);
                    renderProjectDetailView(currentEditingProject);
                });
            });
        }

        // 渲染项目详情视图
        function renderProjectDetailView(project) {
            practiceCenterContainer.innerHTML = `
                <div class="dashboard-header">
                    <h2 style="font-size: 20px;"><button id="back-to-list-btn" class="btn btn-outline" style="margin-right: 15px;"><i class="fas fa-arrow-left"></i></button> ${project.name}</h2>
                </div>
                <div class="card">
                    <div class="tabs" id="project-details-tabs">
                        <div class="tab active" data-tab="info-classes">基础信息与班次管理</div>
                        <div class="tab" data-tab="enrollment">报名进度监控</div>
                        <div class="tab" data-tab="preparation">岗前准备与配置</div>
                        <div class="tab" data-tab="process">过程管理与影像</div>
                        <div class="tab" data-tab="summary">评价与总结</div>
                        <div class="tab" data-tab="report">报告生成与结算</div>
                    </div>
                    <div class="card-body">
                        <div id="project-tab-content">
                            <!-- Tab 内容动态加载 -->
                        </div>
                    </div>
                </div>
            `;
            
            // 默认加载第一个tab
            renderProjectTabContent(project, 'info-classes');

            // 详情页内事件绑定
            document.getElementById('back-to-list-btn').addEventListener('click', renderPracticeProjectList);
            
            document.querySelectorAll('#project-details-tabs .tab').forEach(tab => {
                tab.addEventListener('click', e => {
                    document.querySelector('#project-details-tabs .tab.active').classList.remove('active');
                    e.target.classList.add('active');
                    renderProjectTabContent(project, e.target.dataset.tab);
                });
            });
        }

        // 渲染项目详情的Tab内容
        function renderProjectTabContent(project, tabName) {
            const contentDiv = document.getElementById('project-tab-content');
            switch(tabName) {
                case 'info-classes':
                    contentDiv.innerHTML = `
                        <div class="section">
                            <h4 class="section-title">项目通用信息</h4>
                            <p><strong>实践内容与目标:</strong> ${project.description}</p>
                            <p><strong>面向对象与要求:</strong> ${project.requirements}</p>
                        </div>
                        <div class="section">
                             <div style="display: flex; justify-content: space-between; align-items: center;">
                                <h4 class="section-title">班次管理</h4>
                                <button class="btn btn-primary" id="add-class-btn"><i class="fas fa-plus"></i> 新增班次</button>
                            </div>
                            <div class="table-wrapper">
                                <table class="data-table">
                                    <thead>
                                        <tr><th>班次名称</th><th>实践周期</th><th>报名情况</th><th>班次状态</th><th>操作</th></tr>
                                    </thead>
                                    <tbody>
                                    ${project.classes.map(c => `
                                        <tr>
                                            <td>${c.name}</td>
                                            <td>${c.startDate} ~ ${c.endDate}</td>
                                            <td>${c.confirmed}/${c.capacity}</td>
                                            <td><span class="status-badge status-approved">${c.status}</span></td>
                                            <td class="action-buttons">
                                                <button class="btn btn-primary btn-sm edit-class-btn" data-id="${c.id}">编辑</button>
                                                <button class="btn btn-danger-outline btn-sm delete-class-btn" data-id="${c.id}">删除</button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    `;
                    document.getElementById('add-class-btn').addEventListener('click', () => {
                        currentEditingClass = null;
                        document.getElementById('practice-class-form').reset();
                        document.getElementById('practice-class-drawer-title').textContent = '新增班次';
                        document.getElementById('schedule-table').getElementsByTagName('tbody')[0].innerHTML = ''; // 清空日程
                        openDrawer('practice-class-drawer');
                    });
                    document.querySelectorAll('.edit-class-btn').forEach(btn => {
                        btn.addEventListener('click', e => {
                            const classId = e.target.dataset.id;
                            currentEditingClass = project.classes.find(c => c.id === classId);
                            document.getElementById('practice-class-drawer-title').textContent = '编辑班次';
                            document.getElementById('practice-class-id').value = currentEditingClass.id;
                            document.getElementById('practice-class-name').value = currentEditingClass.name;
                            document.getElementById('practice-class-start-date').value = currentEditingClass.startDate;
                            document.getElementById('practice-class-end-date').value = currentEditingClass.endDate;
                            document.getElementById('practice-class-capacity').value = currentEditingClass.capacity;
                            renderScheduleTable(currentEditingClass);
                            openDrawer('practice-class-drawer');
                        });
                    });
                    break;
                case 'enrollment':
                     contentDiv.innerHTML = `
                        <div class="filter-bar" style="box-shadow: none; padding: 0 0 20px 0;">
                             <div class="filter-group">
                                <label for="class-filter">班次筛选:</label>
                                <select id="class-filter">
                                    <option value="all">全部班次</option>
                                    ${project.classes.map(c => `<option value="${c.id}">${c.name}</option>`).join('')}
                                </select>
                            </div>
                        </div>
                        <div class="table-wrapper">
                            <table class="data-table">
                                <thead><tr><th>姓名</th><th>学号</th><th>所属班次</th><th>状态</th><th>报名时间</th></tr></thead>
                                <tbody>
                                    ${project.enrolledStudents.map(s => `
                                        <tr><td>${s.name}</td><td>${s.studentId}</td><td>${s.className}</td><td><span class="status-badge ${s.status === '已确认' ? 'status-approved' : 'status-pending'}">${s.status}</span></td><td>${s.applyDate}</td></tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                     `;
                    break;
                default:
                    contentDiv.innerHTML = `<p>此功能模块 (${tabName}) 正在加紧建设中...</p>`;
            }
        }
        
        function renderScheduleTable(cls) {
            const tbody = document.getElementById('schedule-table').getElementsByTagName('tbody')[0];
            tbody.innerHTML = (cls.schedule || []).map(s => `
                <tr>
                    <td>${s.date}</td>
                    <td>${s.time}</td>
                    <td>${s.topic}</td>
                    <td>${s.lecturer}</td>
                    <td>${s.location}</td>
                    <td class="action-buttons">
                        <button class="btn btn-danger-outline btn-sm delete-schedule-btn" data-id="${s.id}"><i class="fas fa-trash"></i></button>
                    </td>
                </tr>
            `).join('');
        }
        
        // --- Event Listeners for Practice Module Drawers ---
        document.querySelectorAll('.close-drawer-btn').forEach(btn => {
            btn.addEventListener('click', e => {
                const drawer = e.target.closest('.drawer');
                if(drawer) closeDrawer(drawer.id);
            });
        });

        document.getElementById('add-schedule-item-btn').addEventListener('click', () => {
             document.getElementById('schedule-item-form').reset();
             document.getElementById('schedule-item-drawer-title').textContent = '新增日程';
             openDrawer('schedule-item-drawer');
        });

        document.getElementById('save-schedule-item-btn').addEventListener('click', () => {
            if (!currentEditingClass) return;
            const newScheduleItem = {
                id: 'S' + Date.now(),
                date: document.getElementById('schedule-item-date').value,
                time: document.getElementById('schedule-item-time').value,
                topic: document.getElementById('schedule-item-topic').value,
                lecturer: document.getElementById('schedule-item-lecturer').value,
                location: document.getElementById('schedule-item-location').value
            };
            if(!currentEditingClass.schedule) currentEditingClass.schedule = [];
            currentEditingClass.schedule.push(newScheduleItem);
            renderScheduleTable(currentEditingClass);
            closeDrawer('schedule-item-drawer');
        });

        document.getElementById('save-practice-class-btn').addEventListener('click', () => {
            if (!currentEditingProject) return;
            const formData = {
                id: currentEditingClass ? currentEditingClass.id : 'C' + Date.now(),
                name: document.getElementById('practice-class-name').value,
                startDate: document.getElementById('practice-class-start-date').value,
                endDate: document.getElementById('practice-class-end-date').value,
                capacity: parseInt(document.getElementById('practice-class-capacity').value) || 0,
                confirmed: currentEditingClass ? currentEditingClass.confirmed : 0,
                status: currentEditingClass ? currentEditingClass.status : '筹备中',
                schedule: currentEditingClass ? currentEditingClass.schedule : [],
            };
            
            if (currentEditingClass) {
                const index = currentEditingProject.classes.findIndex(c => c.id === formData.id);
                currentEditingProject.classes[index] = formData;
            } else {
                currentEditingProject.classes.push(formData);
            }
            closeDrawer('practice-class-drawer');
            renderProjectTabContent(currentEditingProject, 'info-classes'); // Re-render tab
        });

        document.getElementById('save-practice-project-btn').addEventListener('click', () => {
             // Logic to save project, for now just re-render list
             // In a real app, this would be an API call
             closeDrawer('practice-project-drawer');
             renderPracticeProjectList();
        });

        // --- Navigation Logic ---
        function switchView(targetId) {
            if (!targetId) return;
            mainContentDivs.forEach(div => {
                div.style.display = (div.id === targetId) ? 'block' : 'none';
            });
            // 高校实践模块的特殊视图处理
            if (targetId === 'practice-center') {
                renderPracticeProjectList();
            }
            // 建设中页面的处理
            if (targetId === 'under-construction') {
                // 建设中页面不需要特殊处理，只需要显示即可
            }
        }

        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                let targetId = this.dataset.target;

                if (this.textContent.includes('工作台')) {
                    targetId = 'dashboard';
                } else if (this.textContent.includes('高校实践')) {
                    targetId = 'practice-center';
                }
                
                if (targetId) {
                    // 只有线索管理保持激活状态，其他菜单点击后不改变激活状态
                    if (targetId === 'lead-center') {
                        navLinks.forEach(l => l.classList.remove('active'));
                        this.classList.add('active');
                    }
                    switchView(targetId);
                }
            });
        });

        // Initialize the view - 默认显示线索管理页面
        switchView('lead-center');
        
        // 设置线索管理导航链接为激活状态
        navLinks.forEach(link => {
            if (link.textContent.includes('线索管理')) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
        
        // 确保线索管理表格被渲染
        if (typeof renderLeadsTable === 'function') {
            renderLeadsTable();
        }
    });
    </script>
</body>
</html>
