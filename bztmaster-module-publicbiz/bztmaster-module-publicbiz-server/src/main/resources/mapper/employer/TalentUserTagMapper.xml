<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.TalentUserTagMapper">

    <sql id="selectFields">
        tut.user_tag_id, tut.user_id, tut.tag_id, tut.tag_type_id,
        ttl.tag_code, ttl.tag_name, ttl.description as tag_description,
        ttt.type_code, ttt.type_name, ttt.description as type_description
    </sql>

    <!-- 根据用户ID查询标签详情列表 -->
    <select id="selectTagDetailsByUserId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.TalentUserTagDetailDO">
        SELECT
        <include refid="selectFields"/>
        FROM talent_user_tag tut
        LEFT JOIN talent_tag_library ttl ON tut.tag_id = ttl.tag_id AND ttl.deleted = 0
        LEFT JOIN talent_tag_type ttt ON tut.tag_type_id = ttt.tag_type_id AND ttt.deleted = 0
        WHERE tut.user_id = #{userId}
        AND tut.deleted = 0
        ORDER BY tut.create_time DESC
    </select>

    <!-- 根据OneID查询标签详情列表 -->
    <select id="selectTagDetailsByOneid" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.TalentUserTagDetailDO">
        SELECT
        <include refid="selectFields"/>
        FROM talent_user_tag tut
        LEFT JOIN talent_tag_library ttl ON tut.tag_id = ttl.tag_id AND ttl.deleted = 0
        LEFT JOIN talent_tag_type ttt ON tut.tag_type_id = ttt.tag_type_id AND ttt.deleted = 0
        INNER JOIN talent_user tu ON tut.user_id = tu.user_id AND tu.deleted = 0
        WHERE tu.oneid = #{oneid}
        AND tut.deleted = 0
        ORDER BY tut.create_time DESC
    </select>

</mapper>
