<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset.CourseLessonMapper">
    
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseLessonDO">
        <id property="id" column="id" />
        <result property="courseId" column="course_id" />
        <result property="chapterId" column="chapter_id" />
        <result property="title" column="title" />
        <result property="lessonType" column="lesson_type" />
        <result property="isFree" column="is_free" />
        <result property="materialId" column="material_id" />
        <result property="materialName" column="material_name" />
        <result property="materialFileUrl" column="material_file_url" />
        <result property="sortOrder" column="sort_order" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creator" column="creator" />
        <result property="updater" column="updater" />
        <result property="deleted" column="deleted" />
        <result property="tenantId" column="tenant_id" />
    </resultMap>

    <!-- 根据章节ID查询课时列表 -->
    <select id="selectListByChapterId" resultMap="BaseResultMap">
        SELECT id, course_id, chapter_id, title, lesson_type, is_free, material_id, 
               material_name, material_file_url, sort_order, create_time, update_time, 
               creator, updater, deleted, tenant_id
        FROM publicbiz_course_lesson
        WHERE deleted = 0 AND chapter_id = #{chapterId}
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据课程ID查询课时列表 -->
    <select id="selectListByCourseId" resultMap="BaseResultMap">
        SELECT id, course_id, chapter_id, title, lesson_type, is_free, material_id, 
               material_name, material_file_url, sort_order, create_time, update_time, 
               creator, updater, deleted, tenant_id
        FROM publicbiz_course_lesson
        WHERE deleted = 0 AND course_id = #{courseId}
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据章节ID和课时标题查询（用于重复性校验） -->
    <select id="selectByChapterIdAndTitle" resultMap="BaseResultMap">
        SELECT id, course_id, chapter_id, title, lesson_type, is_free, material_id, 
               material_name, material_file_url, sort_order, create_time, update_time, 
               creator, updater, deleted, tenant_id
        FROM publicbiz_course_lesson
        WHERE deleted = 0 AND chapter_id = #{chapterId} AND title = #{title}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>

    <!-- 根据章节ID统计课时数量 -->
    <select id="countByChapterId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM publicbiz_course_lesson
        WHERE deleted = 0 AND chapter_id = #{chapterId}
    </select>

    <!-- 根据课程ID统计课时数量 -->
    <select id="countByCourseId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM publicbiz_course_lesson
        WHERE deleted = 0 AND course_id = #{courseId}
    </select>

    <!-- 获取章节下一个排序序号 -->
    <select id="getNextSortOrder" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(sort_order), 0) + 1
        FROM publicbiz_course_lesson
        WHERE deleted = 0 AND chapter_id = #{chapterId}
    </select>

    <!-- 批量删除章节下的所有课时 -->
    <update id="deleteByChapterId">
        UPDATE publicbiz_course_lesson 
        SET deleted = 1, update_time = NOW()
        WHERE chapter_id = #{chapterId} AND deleted = 0
    </update>

    <!-- 批量删除课程下的所有课时 -->
    <update id="deleteByCourseId">
        UPDATE publicbiz_course_lesson 
        SET deleted = 1, update_time = NOW()
        WHERE course_id = #{courseId} AND deleted = 0
    </update>

    <!-- 查询免费试看的课时列表 -->
    <select id="selectFreeListByCourseId" resultMap="BaseResultMap">
        SELECT id, course_id, chapter_id, title, lesson_type, is_free, material_id, 
               material_name, material_file_url, sort_order, create_time, update_time, 
               creator, updater, deleted, tenant_id
        FROM publicbiz_course_lesson
        WHERE deleted = 0 AND course_id = #{courseId} AND is_free = 1
        ORDER BY sort_order ASC, id ASC
    </select>

</mapper>
