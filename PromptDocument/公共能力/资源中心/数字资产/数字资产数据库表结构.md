## 数据库表结构设计

### 1. 主课程表 (publicbiz_digital_asset_course)

```sql
CREATE TABLE `publicbiz_digital_asset_course` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '课程ID',
  `name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '课程名称',
  `teach_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '授课方式：线上授课、线下授课',
  `cover_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '课程封面图片URL',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '课程分类：家政技能、职业素养、高校实践、企业管理',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '待发布' COMMENT '课程状态：待发布、已上架、已下架',
  `teacher_id` bigint(20) DEFAULT NULL COMMENT '关联讲师ID',
  `teacher_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '关联讲师名称',
  `business_module` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '所属业务板块：家政服务、高校实践、培训管理、就业服务、兼职零工',
  `merchant` bigint(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '收款商户ID',
  `merchant_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '收款商户名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '课程详情介绍',

  -- 线下授课专用字段
  `location` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '上课地点（线下授课专用）',
  `schedule` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '排期安排（线下授课专用）',
  `total_seats` int(11) DEFAULT 0 COMMENT '总名额（线下授课专用）',
  `enrolled_count` int(11) DEFAULT 0 COMMENT '已报名人数',

  -- 线上授课专用字段
  `total_duration` decimal(10,2) DEFAULT 0.00 COMMENT '课程总时长（小时，线上授课专用）',

  -- 标准字段
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',

  PRIMARY KEY (`id`),
  KEY `idx_teach_type` (`teach_type`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_teacher_id` (`teacher_id`),
  KEY `idx_business_module` (`business_module`),
  KEY `idx_merchant` (`merchant`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数字资产课程表';
```

### 2. 课程章节表 (publicbiz_course_chapter)

```sql
CREATE TABLE `publicbiz_course_chapter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '章节ID',
  `course_id` bigint(20) NOT NULL COMMENT '课程ID',
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '章节标题',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序序号',

  -- 标准字段
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',

  PRIMARY KEY (`id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程章节表（线上课程专用）';
```

### 3. 课程课时表 (publicbiz_course_lesson)

```sql
CREATE TABLE `publicbiz_course_lesson` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '课时ID',
  `course_id` bigint(20) NOT NULL COMMENT '课程ID',
  `chapter_id` bigint(20) NOT NULL COMMENT '章节ID',
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '课时标题',
  `lesson_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '视频' COMMENT '课时类型：视频、文档、音频',
  `is_free` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否免费试看',
  `material_id` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '关联素材ID',
  `material_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联素材名称',
  `material_file_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '关联素材文件URL',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序序号',

  -- 标准字段
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',

  PRIMARY KEY (`id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_lesson_type` (`lesson_type`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程课时表（线上课程专用）';
```

### 4. 课程附件表 (publicbiz_course_attachment)

```sql
CREATE TABLE `publicbiz_course_attachment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `course_id` bigint(20) NOT NULL COMMENT '课程ID',
  `attachment_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '附件名称',
  `attachment_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '附件类型：视频、文档、音频',
  `file_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '文件URL',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',

  -- 标准字段
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',

  PRIMARY KEY (`id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_attachment_type` (`attachment_type`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程附件表';