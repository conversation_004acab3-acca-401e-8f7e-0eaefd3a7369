package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 机构响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构响应 VO")
@Data
public class AgencyRespVO {

    @Schema(description = "机构ID", example = "1")
    private Long id;

    @Schema(description = "机构名称", example = "测试机构")
    private String agencyName;

    @Schema(description = "机构简称", example = "测试")
    private String agencyShortName;

    @Schema(description = "机构编号", example = "AG001")
    private String agencyNo;

    @Schema(description = "机构类型", example = "cooperation")
    private String agencyType;

    @Schema(description = "法人代表", example = "李四")
    private String legalRepresentative;

    @Schema(description = "统一社会信用代码", example = "91110000123456789X")
    private String unifiedSocialCreditCode;

    @Schema(description = "成立日期", example = "2020-01-01")
    private String establishmentDate;

    @Schema(description = "注册地址", example = "四川省成都市高新区")
    private String registeredAddress;

    @Schema(description = "经营地址", example = "四川省成都市高新区")
    private String operatingAddress;

    @Schema(description = "经营范围", example = "家政服务")
    private String businessScope;

    @Schema(description = "申请人姓名", example = "张三")
    private String applicantName;

    @Schema(description = "申请人电话", example = "***********")
    private String applicantPhone;

    @Schema(description = "申请时间", example = "2024-01-01 10:00:00")
    private LocalDateTime applicationTime;

    @Schema(description = "联系人", example = "张三")
    private String contactPerson;

    @Schema(description = "联系电话", example = "***********")
    private String contactPhone;

    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String contactEmail;

    @Schema(description = "省份", example = "四川省")
    private String province;

    @Schema(description = "城市", example = "成都市")
    private String city;

    @Schema(description = "区县", example = "高新区")
    private String district;

    @Schema(description = "合作状态", example = "cooperating")
    private String cooperationStatus;

    @Schema(description = "审核状态", example = "pending")
    private String reviewStatus;

    @Schema(description = "审核人", example = "admin")
    private String reviewer;

    @Schema(description = "审核时间", example = "2024-01-01 10:00:00")
    private LocalDateTime reviewTime;

    @Schema(description = "审核备注", example = "审核通过，资料齐全")
    private String reviewRemark;

    @Schema(description = "资质文件列表")
    private List<AgencyQualificationVO> qualifications;
} 