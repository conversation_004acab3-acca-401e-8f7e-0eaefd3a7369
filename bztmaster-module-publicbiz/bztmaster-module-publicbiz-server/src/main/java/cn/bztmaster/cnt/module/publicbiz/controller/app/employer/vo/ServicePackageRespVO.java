package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 服务套餐响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端 - 服务套餐响应 VO")
@Data
public class ServicePackageRespVO {

    @Schema(description = "套餐ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4")
    private Long id;

    @Schema(description = "套餐名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "月度保洁服务 | 30天")
    private String name;

    @Schema(description = "服务分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "日常保洁")
    private String category;

    @Schema(description = "服务分类ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Long categoryId;

    @Schema(description = "套餐主图URL", example = "/static/保洁服务图片.png")
    private String thumbnail;

    @Schema(description = "套餐价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "2800.00")
    private BigDecimal price;

    @Schema(description = "原价", example = "3200.00")
    private BigDecimal originalPrice;

    @Schema(description = "价格单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "天")
    private String unit;

    @Schema(description = "服务时长", example = "30天 | 每日2小时")
    private String serviceDuration;

    @Schema(description = "套餐类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "long-term")
    private String packageType;

    @Schema(description = "服务描述", example = "专业的日常保洁服务，每日2小时，持续30天")
    private String serviceDescription;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "active")
    private String status;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "approved")
    private String auditStatus;

    @Schema(description = "所属机构ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long agencyId;

    @Schema(description = "所属机构名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "金牌家政")
    private String agencyName;

    @Schema(description = "合作伙伴ID", example = "2001")
    private Long partnerId;

    @Schema(description = "合作伙伴名称", example = "XX合作伙伴")
    private String partnerName;

    @Schema(description = "机构经度", example = "116.397128")
    private BigDecimal agencyLongitude;

    @Schema(description = "机构纬度", example = "39.916527")
    private BigDecimal agencyLatitude;

    @Schema(description = "特色标签列表")
    private List<String> features;

    @Schema(description = "距离用户位置的距离（公里）", example = "0.5")
    private Double distance;

    @Schema(description = "满意度评分", example = "4.8")
    private BigDecimal satisfactionRate;

    // 次数次卡套餐专用字段
    @Schema(description = "服务次数", example = "2")
    private Integer serviceTimes;

    @Schema(description = "有效期（天）", example = "90")
    private Integer validityPeriod;

    @Schema(description = "有效期单位", example = "day")
    private String validityPeriodUnit;

    @Schema(description = "服务间隔类型", example = "weekly")
    private String serviceIntervalType;

    @Schema(description = "服务间隔数值", example = "1")
    private Integer serviceIntervalValue;

    @Schema(description = "单次服务时长（小时）", example = "2")
    private Integer singleDurationHours;

    // 长周期套餐专用字段
    @Schema(description = "服务时间", example = "9:00-13:00")
    private String serviceTimespan;

    @Schema(description = "服务开始时间", example = "09:00:00")
    private String serviceTimeStart;

    @Schema(description = "服务结束时间", example = "13:00:00")
    private String serviceTimeEnd;

    @Schema(description = "休息日类型", example = "sunday")
    private String restDayType;

}