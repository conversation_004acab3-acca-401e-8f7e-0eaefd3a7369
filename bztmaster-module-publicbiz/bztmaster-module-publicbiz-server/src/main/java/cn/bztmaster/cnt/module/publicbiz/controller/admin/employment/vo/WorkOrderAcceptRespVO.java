package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 工单接单响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单接单响应 VO")
@Data
public class WorkOrderAcceptRespVO {

    @Schema(description = "工单编号", example = "WO202312010001")
    private String workOrderNo;

    @Schema(description = "接单时间")
    private LocalDateTime acceptTime;

    @Schema(description = "接单人ID", example = "123")
    private Long assigneeId;

    @Schema(description = "接单人姓名", example = "张三")
    private String assigneeName;

    @Schema(description = "接单是否成功", example = "true")
    private Boolean success;
} 