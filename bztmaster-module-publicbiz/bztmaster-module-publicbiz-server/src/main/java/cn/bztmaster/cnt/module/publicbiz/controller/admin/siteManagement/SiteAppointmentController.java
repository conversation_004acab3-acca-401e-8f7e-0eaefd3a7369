package cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.siteManagement.SiteAppointmentConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteAppointmentDO;
import cn.bztmaster.cnt.module.publicbiz.service.siteManagement.SiteAppointmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 场地预约
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 场地预约")
@RestController
@RequestMapping("/publicbiz/site-management/appointment")
@Validated
@Slf4j
public class SiteAppointmentController {

    @Resource
    private SiteAppointmentService siteAppointmentService;

    @PostMapping("/create")
    @Operation(summary = "新增预约")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-appointment:create')")
    public CommonResult<Long> createSiteAppointment(@Valid @RequestBody SiteAppointmentSaveReqVO createReqVO) {
        return success(siteAppointmentService.createSiteAppointment(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新预约")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-appointment:update')")
    public CommonResult<Boolean> updateSiteAppointment(@Valid @RequestBody SiteAppointmentSaveReqVO updateReqVO) {
        siteAppointmentService.updateSiteAppointment(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除预约")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-appointment:delete')")
    public CommonResult<Boolean> deleteSiteAppointment(@PathVariable("id") Long id) {
        siteAppointmentService.deleteSiteAppointment(id);
        return success(true);
    }

    @PutMapping("/cancel/{id}")
    @Operation(summary = "取消预约")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-appointment:update')")
    public CommonResult<Boolean> cancelSiteAppointment(@PathVariable("id") Long id) {
        siteAppointmentService.cancelSiteAppointment(id);
        return success(true);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获得预约详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-appointment:query')")
    public CommonResult<SiteAppointmentRespVO> getSiteAppointment(@PathVariable("id") Long id) {
        SiteAppointmentDO siteAppointment = siteAppointmentService.getSiteAppointment(id);
        return success(SiteAppointmentConvert.INSTANCE.convert(siteAppointment));
    }

    @GetMapping("/page")
    @Operation(summary = "获得预约分页")
    @PreAuthorize("@ss.hasPermission('publicbiz:site-appointment:query')")
    public CommonResult<PageResult<SiteAppointmentRespVO>> getSiteAppointmentPage(@Valid SiteAppointmentPageReqVO pageReqVO) {
        PageResult<SiteAppointmentDO> pageResult = siteAppointmentService.getSiteAppointmentPage(pageReqVO);
        return success(SiteAppointmentConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list")
    @Operation(summary = "获得预约列表")
    public CommonResult<List<SiteAppointmentRespVO>> getSiteAppointmentList(@Valid SiteAppointmentListReqVO listReqVO) {
        List<SiteAppointmentDO> list = siteAppointmentService.getSiteAppointmentList(listReqVO);
        return success(SiteAppointmentConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/listBySiteAndDate")
    @Operation(summary = "根据场地和日期查询预约")
    public CommonResult<List<SiteAppointmentRespVO>> getSiteAppointmentListBySiteAndDate(@Valid SiteAppointmentBySiteAndDateReqVO reqVO) {
        List<SiteAppointmentDO> list = siteAppointmentService.getSiteAppointmentListBySiteAndDate(reqVO);
        return success(SiteAppointmentConvert.INSTANCE.convertList(list));
    }

}
