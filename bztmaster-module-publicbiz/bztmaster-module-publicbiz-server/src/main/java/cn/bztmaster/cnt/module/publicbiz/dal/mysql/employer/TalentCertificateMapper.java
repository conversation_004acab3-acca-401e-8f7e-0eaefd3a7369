package cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.TalentCertificateDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 人才资质证书 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TalentCertificateMapper extends BaseMapperX<TalentCertificateDO> {

    /**
     * 根据用户ID查询已审核通过的资质证书
     *
     * @param userId 用户ID
     * @return 资质证书列表
     */
    @Select("SELECT * FROM talent_certificate WHERE user_id = #{userId} AND status = 'VERIFIED' AND deleted = 0")
    List<TalentCertificateDO> selectVerifiedListByUserId(@Param("userId") Long userId);
}

