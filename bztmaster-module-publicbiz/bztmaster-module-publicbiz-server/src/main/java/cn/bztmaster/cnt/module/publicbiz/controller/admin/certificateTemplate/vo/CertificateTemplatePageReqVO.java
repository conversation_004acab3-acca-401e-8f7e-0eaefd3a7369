package cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 管理后台 - 证书模板分页查询 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 证书模板分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CertificateTemplatePageReqVO extends PageParam {

    @Schema(description = "证书类型", example = "training")
    private String type;

    @Schema(description = "模板状态", example = "active")
    private String status;

    @Schema(description = "搜索关键词", example = "新媒体")
    private String keyword;

}