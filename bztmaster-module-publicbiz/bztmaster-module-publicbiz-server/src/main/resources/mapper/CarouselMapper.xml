<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.CarouselMapper">

    <sql id="selectFields">
        id, tenant_id, creator, create_time, updater, update_time, deleted, platform, 
        carousel_title, carousel_image_url, carousel_link_url, sort_order, status, 
        start_time, end_time
    </sql>

    <!-- 根据平台和状态查询轮播图列表（带时间范围过滤） -->
    <select id="selectByPlatformAndStatus" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.CarouselDO">
        SELECT <include refid="selectFields"/>
        FROM publicbiz_carousel
        WHERE deleted = 0
        AND platform = #{platform} 
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="currentTime != null">
            AND (start_time IS NULL OR start_time &lt;= #{currentTime})
            AND (end_time IS NULL OR end_time &gt;= #{currentTime})
        </if>
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 根据标题和平台检查是否存在重复 -->
    <select id="existsByTitleAndPlatform" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM publicbiz_carousel
        WHERE deleted = 0
        AND carousel_title = #{carouselTitle}
        AND platform = #{platform}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量更新排序值 -->
    <update id="updateSortOrderBatch">
        <foreach collection="carouselList" item="carousel" separator=";">
            UPDATE publicbiz_carousel
            SET sort_order = #{carousel.sortOrder},
                update_time = NOW(),
                updater = #{carousel.updater}
            WHERE id = #{carousel.id}
            AND deleted = 0
        </foreach>
    </update>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.CarouselDO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="platform" property="platform"/>
        <result column="carousel_title" property="carouselTitle"/>
        <result column="carousel_image_url" property="carouselImageUrl"/>
        <result column="carousel_link_url" property="carouselLinkUrl"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, creator, create_time, updater, update_time, deleted,
        platform, carousel_title, carousel_image_url, carousel_link_url, sort_order, status, start_time, end_time
    </sql>

</mapper> 