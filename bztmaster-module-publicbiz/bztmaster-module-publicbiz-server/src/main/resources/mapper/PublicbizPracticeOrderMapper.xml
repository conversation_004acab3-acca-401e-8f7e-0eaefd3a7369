<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizPracticeOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizPracticeOrderDO">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="order_id" property="orderId" />
        <result column="order_no" property="orderNo" />
        <result column="university_name" property="universityName" />
        <result column="university_contact" property="universityContact" />
        <result column="university_phone" property="universityPhone" />
        <result column="university_email" property="universityEmail" />
        <result column="enterprise_name" property="enterpriseName" />
        <result column="enterprise_contact" property="enterpriseContact" />
        <result column="enterprise_phone" property="enterprisePhone" />
        <result column="enterprise_email" property="enterpriseEmail" />
        <result column="project_name" property="projectName" />
        <result column="project_description" property="projectDescription" />
        <result column="student_count" property="studentCount" />
        <result column="practice_duration" property="practiceDuration" />
        <result column="practice_location" property="practiceLocation" />
        <result column="service_fee" property="serviceFee" />
        <result column="management_fee" property="managementFee" />
        <result column="other_fee" property="otherFee" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, creator, create_time, updater, update_time, deleted,
        order_id, order_no, university_name, university_contact, university_phone, university_email,
        enterprise_name, enterprise_contact, enterprise_phone, enterprise_email, project_name, project_description,
        student_count, practice_duration, practice_location, service_fee, management_fee, other_fee
    </sql>

</mapper>





