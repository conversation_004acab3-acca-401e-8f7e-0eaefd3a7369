<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper">

    <sql id="selectFields">
        id, tenant_id, creator, create_time, updater, update_time, deleted,
        order_id, order_no, domestic_order_id, task_no, task_sequence, task_name, task_description, task_type, task_status,
        planned_start_time, planned_end_time, actual_start_time, actual_end_time, duration,
        practitioner_oneid, practitioner_name, practitioner_phone,
        schedule_date, service_category_id, service_category_name, customer_id, customer_name, customer_phone, service_address,
        punch_in_time, punch_out_time, punch_location, punch_latitude, punch_longitude
    </sql>

    <!-- 根据阿姨OneID和日期范围查询排班任务 -->
    <select id="selectByAuntOneIdAndDateRange" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO">
        SELECT *
        FROM publicbiz_domestic_task
        WHERE practitioner_oneid = #{auntOneId}
          AND schedule_date >= #{startDate}
          AND schedule_date &lt;= #{endDate}
          AND deleted = 0
        ORDER BY schedule_date ASC, planned_start_time ASC
    </select>

    <!-- 根据阿姨OneID查询指定日期的排班任务 -->
    <select id="selectByAuntOneIdAndScheduleDate" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO">
        SELECT *
        FROM publicbiz_domestic_task
        WHERE practitioner_oneid = #{auntOneId}
          AND schedule_date = #{scheduleDate}
          AND deleted = 0
        ORDER BY planned_start_time ASC
    </select>

    <!-- 根据阿姨OneID查询进行中的任务 -->
    <select id="selectInProgressByAuntOneId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO">
        SELECT *
        FROM publicbiz_domestic_task
        WHERE practitioner_oneid = #{auntOneId}
          AND task_status = 'in_progress'
          AND deleted = 0
        ORDER BY planned_start_time ASC
    </select>

    <!-- 查询阿姨今日排班数 -->
    <select id="selectTodayScheduleCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM publicbiz_domestic_task
        WHERE practitioner_oneid = #{auntOneId}
          AND schedule_date = #{scheduleDate}
          AND deleted = 0
    </select>

    <!-- 查询阿姨本月服务时长 -->
    <select id="selectMonthlyServiceHours" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(
            CASE
                WHEN actual_start_time IS NOT NULL AND actual_end_time IS NOT NULL
                THEN TIMESTAMPDIFF(HOUR, actual_start_time, actual_end_time)
                ELSE 0
            END
        ), 0) as total_hours
        FROM publicbiz_domestic_task
        WHERE practitioner_oneid = #{auntOneId}
          AND actual_start_time >= #{startTime}
          AND actual_start_time &lt;= #{endTime}
          AND task_status = 'completed'
          AND deleted = 0
    </select>

    <!-- 根据任务编号列表查询任务 -->
    <select id="selectByTaskNoList" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO">
        SELECT <include refid="selectFields"/>
        FROM publicbiz_domestic_task
        WHERE deleted = 0
        AND task_no IN
        <foreach collection="taskNoList" item="taskNo" open="(" separator="," close=")">
            #{taskNo}
        </foreach>
    </select>

    <!-- 根据任务编号查询任务 -->
    <select id="selectByTaskNo" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO">
        SELECT <include refid="selectFields"/>
        FROM publicbiz_domestic_task
        WHERE deleted = 0
        AND task_no = #{taskNo}
    </select>

    <!-- 根据任务编号列表更新服务人员信息 -->
    <update id="updatePractitionerByTaskNoList">
        UPDATE publicbiz_domestic_task
        SET practitioner_oneid = #{practitionerOneid},
            practitioner_name = #{practitionerName},
            practitioner_phone = #{practitionerPhone},
            update_time = NOW(),
            updater = 'system'
        WHERE deleted = 0
        AND task_no IN
        <foreach collection="taskNoList" item="taskNo" open="(" separator="," close=")">
            #{taskNo}
        </foreach>
    </update>

</mapper>
