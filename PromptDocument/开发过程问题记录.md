

### 部署Nacos注意：
1. 修改application.yml中的nacos地址
2. 修改application-dev.yml中的nacos地址
3. 修改application-local.yml中的nacos地址
4. 注册中心的 group 需要保持一致，例如都配置dev
5. Nacos 配置的语法比较严格，服务名一定要和项目中application.yml 的服务名保持一致

### 以人才库的部署为案例 bztmaster-module-talentpool
1.文件路径:bztmaster-module-talentpool/bztmaster-module-talentpool-server/src/main/resources/application.yaml
   服务名： name: apachecore-server
   nacos配置：
            命名空间 :HR-CNT
            Data ID:apachecore-server
            Group：dev
    nacos配置完成后 新增的模块需要在bztmaster-gateway/src/main/resources/application.yaml文件中添加相关配置
可搜索 关键字“apachecore”查看全部配置项
2.接口联调注意事项：
    1.DO文件需要有注解内容：包括数据库表名等信息（具体地址：cn/bztmaster/cnt/module/talentpool/dal/dataobject/talent/TalentUserDO.java）
    2.接口调用的底层文件中方法体需要实现，具体地址：cn/bztmaster/cnt/module/talentpool/dal/mysql/talent/TalentUserMapper.java
3. 图片上传,远端服务器配置表：infra_file_config where master=1  目前用的是七牛服务器
