package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 考题 - 批量导入校验单项 VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "考题 - 批量导入校验单项 VO")
public class QuestionImportValidateItemVO {
    
    @Schema(description = "行号")
    private Integer rowNum;
    
    @Schema(description = "一级名称")
    private String level1Name;
    
    @Schema(description = "一级代码")
    private String level1Code;
    
    @Schema(description = "二级名称")
    private String level2Name;
    
    @Schema(description = "二级代码")
    private String level2Code;
    
    @Schema(description = "三级名称")
    private String level3Name;
    
    @Schema(description = "三级代码")
    private String level3Code;
    
    @Schema(description = "认定点名称")
    private String certName;
    
    @Schema(description = "认定点代码")
    private String certCode;
    
    @Schema(description = "题型")
    private String type;
    
    @Schema(description = "题干")
    private String title;
    
    @Schema(description = "选择项")
    private String options;
    
    @Schema(description = "参考答案")
    private String answer;
    
    @Schema(description = "错误信息")
    private String errorMessage;
    
    @Schema(description = "校验状态：VALID-有效，ERROR-错误")
    private String validateStatus;
    
    @Schema(description = "原始数据")
    private QuestionSaveReqVO originalData;

}
