package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工单分页查询请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单分页查询请求 VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkOrderPageReqVO extends PageParam {

    @Schema(description = "工单类型：complaint-投诉/substitution_request-换人申请/take_leave-请假/顶岗/separation_application-离职申请/leave_adjustment-调休", example = "complaint")
    private String workOrderType;

    @Schema(description = "工单状态：pending-待处理/processing-处理中/resolved-已解决/closed-已关闭/approved-已批准/rejected-已驳回", example = "pending")
    private String workOrderStatus;

    @Schema(description = "优先级：low-低/medium-中/high-高/urgent-紧急", example = "medium")
    private String priority;

    @Schema(description = "机构名称，支持模糊查询", example = "某某家政公司")
    private String agencyName;
} 