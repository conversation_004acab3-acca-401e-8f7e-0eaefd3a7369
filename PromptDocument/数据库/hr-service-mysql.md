---
description: 
globs: 
alwaysApply: false
---
# 汇成人力资源服务平台 - 数据表结构与建表SQL

每个表需要包含以下几个字段：
`deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
`creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

## 1. 人才库中枢（OneID）相关表

## 1.1. 用户主表（talent_user）
```sql
CREATE TABLE `talent_user` (
  `user_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `name` VARCHAR(50) NOT NULL COMMENT '姓名',
  `identity_id` VARCHAR(18) NOT NULL UNIQUE COMMENT '身份证号',
  `birth_date` DATE NOT NULL COMMENT '出生日期',
  `gender` VARCHAR(10) COMMENT '性别（可选值：男、女、其他）',
  `phone` VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号',
  `email` VARCHAR(100) COMMENT '邮箱',
  `avatar_url` VARCHAR(255) COMMENT '头像URL',
  `status` VARCHAR(30) COMMENT 'OneID状态（可选值：正常、待合并、已禁用）',
  `register_source` VARCHAR(50) COMMENT '用户来源',
  `oneid` CHAR(36) NOT NULL DEFAULT '' COMMENT 'OneID GUID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`)
) COMMENT='平台用户主表';


ALTER TABLE `talent_user`
    ADD COLUMN `completeness` TINYINT DEFAULT 0 COMMENT '档案完整度百分比';

ALTER TABLE `talent_user` ADD COLUMN
`tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';



ALTER TABLE `talent_user`
ADD COLUMN `org_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID' AFTER `tenant_id`,
ADD COLUMN `org_name` varchar(200) DEFAULT NULL COMMENT '所属机构名称' AFTER `org_id`,
ADD COLUMN `talent_source` varchar(100) DEFAULT NULL COMMENT '人才来源' AFTER `org_name`,
ADD COLUMN `is_self_support` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否为平台自营（0-否，1-是）' AFTER `talent_source`;


```

## 1.2 教育背景表（talent_education）
```sql
CREATE TABLE `talent_education` (
  `education_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `institution` VARCHAR(100) NOT NULL COMMENT '毕业院校',
  `college_address` VARCHAR(255) COMMENT '学院地址',
  `degree_type` VARCHAR(20) COMMENT '学位类型（可选值：专科、本科、硕士、博士）',
  `major` VARCHAR(100) COMMENT '专业',
  `start_date` DATE COMMENT '入学时间',
  `end_date` DATE COMMENT '毕业时间',
  `academic_ranking` VARCHAR(50) COMMENT '学业排名',
  `is_internship` TINYINT(1) DEFAULT 0 COMMENT '是否实习 0-否 1-是',
  `internship_type` VARCHAR(20) COMMENT '实习类别（可选值：生产实习、认知实习、毕业实习、其他）',
  `internship_duration` INT COMMENT '实习时长（天）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='教育背景';
```

### 1.3 校园实践表（talent_campus_practice）
```sql
CREATE TABLE `talent_campus_practice` (
  `practice_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `practice_name` VARCHAR(100) NOT NULL COMMENT '实践名称',
  `organizer` VARCHAR(100) COMMENT '组织方',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `practice_report` TEXT COMMENT '实践报告/总结',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='校园实践';
```

### 1.4 校园实践表（talent_campus_practice）
```sql
CREATE TABLE `talent_campus_practice` (
  `practice_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `practice_name` VARCHAR(100) NOT NULL COMMENT '实践名称',
  `organizer` VARCHAR(100) COMMENT '组织方',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `practice_report` TEXT COMMENT '实践报告/总结',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='校园实践';
```

### 1.5 实习经历表（talent_internship）
```sql
CREATE TABLE `talent_internship` (
  `internship_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `company` VARCHAR(100) NOT NULL COMMENT '实习公司',
  `position` VARCHAR(100) COMMENT '实习岗位',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `responsibilities` TEXT COMMENT '工作职责',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='实习经历';
```

### 1.6 项目经历表（talent_project）
```sql
CREATE TABLE `talent_project` (
  `project_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `name` VARCHAR(100) NOT NULL COMMENT '项目名称',
  `description` TEXT COMMENT '项目描述',
  `start_date` DATE COMMENT '开始时间',
  `end_date` DATE COMMENT '结束时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='项目经历';
```

### 1.7 培训记录表（talent_training）
```sql
CREATE TABLE `talent_training` (
  `training_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `provider` VARCHAR(100) COMMENT '培训机构',
  `course` VARCHAR(100) COMMENT '课程名称',
  `complete_date` DATE COMMENT '完成日期',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='培训记录';
```

### 1.8 技能清单表（talent_skill）
```sql
CREATE TABLE `talent_skill` (
  `skill_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `name` VARCHAR(100) NOT NULL COMMENT '技能名称',
  `level` VARCHAR(10) COMMENT '掌握程度（可选值：了解、熟悉、精通）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='技能清单';
```

### 1.9 认证与资质表（talent_certificate）
```sql
CREATE TABLE `talent_certificate` (
  `certificate_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `name` VARCHAR(100) NOT NULL COMMENT '证书名称',
  `issuer` VARCHAR(100) COMMENT '发证机构',
  `issue_date` DATE COMMENT '颁发日期',
  `source` VARCHAR(20) COMMENT '记录来源（可选值：PLATFORM、AGENCY、SELF）',
  `status` VARCHAR(30) COMMENT '审核状态（可选值：VERIFIED、PENDING_VERIFICATION、REJECTED）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='认证与资质';

ALTER TABLE `talent_certificate`
ADD COLUMN `certificate_no` VARCHAR(100) COMMENT '证书编号' AFTER `certificate_id`,
ADD COLUMN `expiry_date` DATE COMMENT '到期时间' AFTER `issue_date`,
ADD COLUMN `certificate_image_url` VARCHAR(255) COMMENT '证书图片' AFTER `status`;

```

### 1.10 求职记录表（talent_job_application）
```sql
CREATE TABLE `talent_job_application` (
  `application_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `company` VARCHAR(100) NOT NULL COMMENT '申请公司',
  `position` VARCHAR(100) COMMENT '申请职位',
  `apply_date` DATE COMMENT '申请日期',
  `status` VARCHAR(30) COMMENT '状态（可选值：已投递、面试中、已录用、未通过）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='求职记录';
```

### 1.11 工作履历表（talent_employment）
```sql
CREATE TABLE `talent_employment` (
  `employment_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `company` VARCHAR(100) NOT NULL COMMENT '就职公司',
  `position` VARCHAR(100) COMMENT '职位',
  `start_date` DATE COMMENT '入职时间',
  `end_date` DATE COMMENT '离职时间',
  `salary` DECIMAL(10,2) COMMENT '薪资',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='工作履历';
```
### 1.12 人才标签表（talent_user_tag）
```sql
CREATE TABLE `talent_user_tag` (
  `user_tag_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `tag_id` BIGINT NOT NULL COMMENT '标签ID，关联标签库',
  `tag_type_id` BIGINT NOT NULL COMMENT '标签类型ID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='用户标签关联表';
```
### 1.13 标签类型表（talent_tag_type）
```sql
CREATE TABLE `talent_tag_type` (
  `tag_type_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签类型ID',
  `type_code` VARCHAR(50) NOT NULL UNIQUE COMMENT '标签类型编码',
  `type_name` VARCHAR(100) NOT NULL COMMENT '标签类型名称',
  `description` VARCHAR(255) COMMENT '描述',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='标签类型表';
```

## 1.14. 标签库表（talent_tag_library）
```sql
CREATE TABLE `talent_tag_library` (
  `tag_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
  `tag_type_id` BIGINT NOT NULL COMMENT '标签类型ID',
  `tag_code` VARCHAR(50) NOT NULL UNIQUE COMMENT '标签编码',
  `tag_name` VARCHAR(100) NOT NULL COMMENT '标签名称',
  `description` VARCHAR(255) COMMENT '标签描述',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) COMMENT='标签库表';




ALTER TABLE `talent_education` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';

ALTER TABLE `talent_campus_practice` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';

ALTER TABLE `talent_internship` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';

ALTER TABLE `talent_project` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';
ALTER TABLE `talent_training` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';

ALTER TABLE `talent_skill` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';
ALTER TABLE `talent_certificate` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';


ALTER TABLE `talent_job_application` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';
ALTER TABLE `talent_employment` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';


ALTER TABLE `talent_user_tag` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';
ALTER TABLE `talent_tag_type` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';
ALTER TABLE `talent_tag_library` ADD COLUMN
    `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';



CREATE TABLE `talent_user_comment` (
  `comment_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '被评价用户ID',
  `role` VARCHAR(20) NOT NULL COMMENT '评价角色（雇主/培训师等）',
  `reviewer_name` VARCHAR(50) COMMENT '评价人姓名',
  `service_type` VARCHAR(50) COMMENT '服务类型：订单、课程、其它',
  `service_name` VARCHAR(100) COMMENT '服务名称：订单号、课程名称、其它',
  `content` TEXT COMMENT '评价内容',
  `score` TINYINT NOT NULL DEFAULT 5 COMMENT '评分（1-5星）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号'
) COMMENT='用户评价表';

```

---

## 2. 公共能力模块相关表

### 2.1 商机中心（opportunity）
```sql
CREATE TABLE `opportunity` (
  `opportunity_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `name` VARCHAR(100) NOT NULL COMMENT '商机名称',
  `partner_id` BIGINT NOT NULL COMMENT '关联客户ID',
  `expected_amount` DECIMAL(12,2) NOT NULL COMMENT '预计成交金额',
  `expected_date` DATE COMMENT '预计成交日期',    
  `stage` VARCHAR(30) NOT NULL COMMENT '销售阶段（可选值：初步接洽、需求分析、方案报价、合同谈判、已关闭、跟进中）',
  `owner_id` BIGINT NOT NULL COMMENT '负责人后台账号ID',
  `status` VARCHAR(30) COMMENT '商机状态（可选值：待审批、跟进中、已关闭）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) COMMENT='商机中心';
```

### 2.2 商机跟进记录表（opportunity_followup）
```sql
CREATE TABLE `opportunity_followup` (
  `followup_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `opportunity_id` BIGINT NOT NULL COMMENT '关联商机ID',
  `content` TEXT COMMENT '跟进内容',
  `attachment_url` VARCHAR(255) COMMENT '附件',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) COMMENT='商机跟进记录';
```

### 2.3 线索中心（lead）
```sql
CREATE TABLE `lead` (
  `lead_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `name` VARCHAR(50) COMMENT '姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
  `source` VARCHAR(50) COMMENT '线索来源',
  `status` VARCHAR(30) COMMENT '线索状态（可选值：待分配、跟进中、已转化、公海）',
  `owner_id` BIGINT NOT NULL COMMENT '负责人后台账号ID',
  `last_followup_at` DATETIME COMMENT '最后跟进时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='销售线索';


CREATE TABLE `lead_assignment_rule` (
  `rule_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `rule_name` VARCHAR(100) NOT NULL COMMENT '规则名称',
  `channel` VARCHAR(50) COMMENT '适用渠道（如为空则为全部渠道）',
  `assign_type` VARCHAR(20) NOT NULL COMMENT '分配方式（轮询/指定/平均/自定义）',
  `sales_ids` VARCHAR(255) COMMENT '参与分配的销售ID列表（逗号分隔）',
  `priority` INT DEFAULT 1 COMMENT '优先级，数字越大优先级越高',
  `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用 1-启用 0-禁用',
  `description` VARCHAR(255) COMMENT '规则说明',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='线索分配规则表';


CREATE TABLE `lead_assignment_log` (
  `log_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `lead_id` BIGINT NOT NULL COMMENT '关联线索ID',
  `from_owner_id` BIGINT COMMENT '原负责人ID',
  `to_owner_id` BIGINT NOT NULL COMMENT '新负责人ID',
  `assign_type` VARCHAR(20) COMMENT '分配类型（手动/自动/公海认领）',
  `assign_time` DATETIME NOT NULL COMMENT '分配时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT='线索分配日志';
```

### 2.4 订单中心（order）
```sql
CREATE TABLE `order` (
  `order_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
  `order_no` VARCHAR(32) NOT NULL UNIQUE COMMENT '订单号',
    `admin_id` BIGINT NOT NULL COMMENT '创建订单的后台账号ID',
    `order_source` VARCHAR(50) COMMENT '订单来源（如：后台手动、小程序、外部渠道等）',
  `order_type` VARCHAR(50) NOT NULL COMMENT '订单业务类型（高校实践、企业培训、个人培训、家政服务、**考试认证等）',
  `user_id` BIGINT NOT NULL COMMENT '关联客户/学员ID',
  `product_name` VARCHAR(100) NOT NULL COMMENT '商品/服务名称',
  `amount` DECIMAL(12,2) NOT NULL COMMENT '订单金额',
  `order_status` VARCHAR(30) NOT NULL COMMENT '订单状态（可选值：待支付、待履约、已完成、已关闭、已退款）',
  `pay_status` VARCHAR(30) NOT NULL COMMENT '支付状态（可选值：未支付、已支付、已退款）',
    `contract_no` VARCHAR(50) COMMENT '线下合同编号',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='订单中心';


ALTER TABLE `order`
ADD COLUMN `escrow_status` VARCHAR(20) COMMENT '托管状态（未托管、托管中、已结算、已退款）' AFTER `pay_status`;


CREATE TABLE `order_review` (
  `review_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `order_id` BIGINT NOT NULL COMMENT '关联订单ID',
  `user_id` BIGINT NOT NULL COMMENT '评价人用户ID',
  `reviewer_role` VARCHAR(30) COMMENT '评价人角色（雇主/学员/企业/家政员/讲师等）',
  `score` DECIMAL(3,1) NOT NULL COMMENT '评分（1-5分，支持小数）',
  `content` TEXT COMMENT '评价内容',
  `tags` VARCHAR(255) COMMENT '评价标签（逗号分隔）',
  `images` VARCHAR(1000) COMMENT '图片URL列表（逗号分隔）',
  `reply` TEXT COMMENT '平台/被评价方回复',
  `review_time` DATETIME NOT NULL COMMENT '评价时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='订单评价表';


CREATE TABLE `order_payment_proof` (
  `proof_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `order_id` BIGINT NOT NULL COMMENT '关联订单ID',
  `admin_id` BIGINT NOT NULL COMMENT '上传人后台账号ID',
  `proof_url` VARCHAR(255) NOT NULL COMMENT '凭证文件URL',
  `remark` VARCHAR(255) COMMENT '备注',
  `status` VARCHAR(20) DEFAULT '待审核' COMMENT '凭证状态（待审核、已通过、已驳回）',
  `uploaded_at` DATETIME NOT NULL COMMENT '上传时间',
  `reviewer_id` BIGINT COMMENT '审核人后台账号ID',
  `reviewed_at` DATETIME COMMENT '审核时间',
  `review_remark` VARCHAR(255) COMMENT '审核意见',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='订单付款凭证表';


```

### 2.5 合同管理表（contract）
```sql
CREATE TABLE `contract` (
  `contract_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '合同ID',
  `contract_no` VARCHAR(32) NOT NULL UNIQUE COMMENT '合同编号',
  `contract_name` VARCHAR(100) NOT NULL COMMENT '合同名称',
  `order_id` BIGINT NOT NULL COMMENT '关联订单ID',
  `contract_type` VARCHAR(30) NOT NULL COMMENT '合同类型（可选值：电子合同、纸质合同）',
  `signer_info` VARCHAR(255) COMMENT '签署方信息',
  `status` VARCHAR(30) NOT NULL COMMENT '合同状态（可选值：待签署、签署中、已归档、已作废）',
  `start_date` DATE COMMENT '签署日期(必须为有效日期，且不能晚于今天)',
  `amount` DECIMAL(12,2) COMMENT '合同金额',
  `attachment_url` VARCHAR(255) COMMENT '合同附件',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='合同管理';


ALTER TABLE `contract`
ADD COLUMN `valid_from` DATE COMMENT '合同有效期起' AFTER `start_date`,
ADD COLUMN `valid_to` DATE COMMENT '合同有效期止' AFTER `valid_from`,
ADD COLUMN `party_a_name` VARCHAR(100) COMMENT '甲方名称' AFTER `contract_name`,
ADD COLUMN `party_a_id_card` VARCHAR(32) COMMENT '甲方法人/负责人身份证号' AFTER `party_a_name`,
ADD COLUMN `party_b_name` VARCHAR(100) COMMENT '乙方名称' AFTER `party_a_id_card`,
ADD COLUMN `party_b_id_card` VARCHAR(32) COMMENT '乙方法人/负责人身份证号' AFTER `party_b_name`,
ADD COLUMN `service_start_date` DATE COMMENT '服务开始日期' AFTER `amount`,
ADD COLUMN `service_end_date` DATE COMMENT '服务结束日期' AFTER `service_start_date`,
ADD COLUMN `completed_at` DATETIME COMMENT '所有签署方完成或合同归档的时间' AFTER `UPDATE_DATE`,
ADD COLUMN `template_category` VARCHAR(50) COMMENT '合同模板分类' AFTER `contract_type`;
ADD COLUMN `approval_status` VARCHAR(30) COMMENT '审批状态（待审批、审批中、已通过、已驳回）' AFTER `status`,
ADD COLUMN `current_approver_id` BIGINT COMMENT '当前审批人后台账号ID' AFTER `approval_status`,
ADD COLUMN `approval_remark` VARCHAR(255) COMMENT '审批备注' AFTER `current_approver_id`;



CREATE TABLE `todo_task` (
  `task_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `task_type` VARCHAR(50) NOT NULL COMMENT '任务类型（合同审批、收款审批、订单审批、线索跟进、退款处理等）',
  `related_id` BIGINT NOT NULL COMMENT '关联业务ID（如合同ID、订单ID等）',
  `title` VARCHAR(100) NOT NULL COMMENT '任务标题',
  `content` VARCHAR(255) COMMENT '任务内容/简要说明',
  `admin_id` BIGINT NOT NULL COMMENT '待办人后台账号ID',
  `status` VARCHAR(20) NOT NULL DEFAULT '待处理' COMMENT '任务状态（待处理、已处理、已撤回等）',
  `completed_at` DATETIME COMMENT '完成时间',
  `action_url` VARCHAR(255) COMMENT '操作入口URL',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='后台代办任务表';

ALTER TABLE `todo_task`
ADD COLUMN `priority` VARCHAR(20) COMMENT '紧急程度（高/中/低）' AFTER `task_type`;
ADD COLUMN `due_date` DATETIME COMMENT '截至日期' AFTER `priority`;
--- ADD COLUMN `cc_ids` VARCHAR(1000) COMMENT '抄送人后台账号ID列表（逗号分隔）' AFTER `assignee_id`;
ADD COLUMN `handle_comment` VARCHAR(255) COMMENT '任务处理意见' AFTER `status`;
ADD COLUMN `attachments` VARCHAR(2000) COMMENT '任务附件URL（逗号分隔）' AFTER `handle_comment`;


### 抄送人 单独新增一个表 便于统计数量

CREATE TABLE `todo_task_cc` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `task_id` BIGINT NOT NULL COMMENT '关联任务ID',
  `admin_id` BIGINT NOT NULL COMMENT '抄送人后台账号ID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT='任务抄送人表';


CREATE TABLE `todo_task_node` (
  `node_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `task_id` BIGINT NOT NULL COMMENT '关联任务ID',
  `step` INT NOT NULL COMMENT '节点顺序（1,2,3...）',
  `handler_id` BIGINT NOT NULL COMMENT '节点处理人后台账号ID',
  `handler_role` VARCHAR(50) COMMENT '处理人角色',
  `status` VARCHAR(20) NOT NULL DEFAULT '待处理' COMMENT '节点状态（待处理、已处理、已撤回等）',
  `completed_at` DATETIME COMMENT '节点完成时间',
  `action` VARCHAR(50) COMMENT '处理动作（同意/驳回/转交/加签等）',
  `comment` VARCHAR(255) COMMENT '处理意见',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='任务流节点表';


ALTER TABLE `todo_task_node`
ADD COLUMN `delegated_from_id` BIGINT COMMENT '转交来源人后台账号ID' AFTER `handler_id`,
ADD COLUMN `delegate_remark` VARCHAR(255) COMMENT '转交说明' AFTER `delegated_from_id`;


CREATE TABLE `todo_task_comment` (
  `comment_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `task_id` BIGINT NOT NULL,
  `admin_id` BIGINT NOT NULL COMMENT '评论人ID',
  `content` TEXT NOT NULL COMMENT '评论内容',

  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT='任务评论表';


CREATE TABLE `todo_task_log` (
  `log_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `task_id` BIGINT NOT NULL,
  `action` VARCHAR(50) NOT NULL COMMENT '操作类型（创建/分配/处理/评论/关闭等）',
  `content` VARCHAR(255) COMMENT '操作内容',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT='任务审计日志表';


CREATE TABLE `announcement` (
  `announcement_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `title` VARCHAR(100) NOT NULL,
  `content` TEXT NOT NULL,
  `attachments` VARCHAR(2000) COMMENT '附件URL（逗号分隔）',
  `status` VARCHAR(20) DEFAULT '已发布' COMMENT '状态（草稿/已发布/已撤回）',
  `publish_at` DATETIME COMMENT '发布时间',
  `expire_at` DATETIME COMMENT '失效时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='系统公告表';



```



---

## 3. 资源中心相关表

### 3.1 合作伙伴主表（partner）
```sql
CREATE TABLE `partner` (
  `partner_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '合作伙伴ID',
  `partner_type` VARCHAR(30) NOT NULL COMMENT '机构类型（可选值：高校、企业、供应商、家政机构、渠道）',
  `partner_name` VARCHAR(100) NOT NULL COMMENT '机构名称',
  `partner_code` VARCHAR(50) COMMENT '机构编码',
  `parent_id` BIGINT COMMENT '上级机构ID',
  `registered_address` VARCHAR(255) COMMENT '注册地址',
  `office_address` VARCHAR(255) COMMENT '办公地址',
  `legal_representative` VARCHAR(50) COMMENT '法定代表人',
  `foundation_date` DATE COMMENT '成立日期',
  `partner_level` VARCHAR(50) COMMENT '机构级别',
  `tax_id` VARCHAR(50) COMMENT '税号',
  `license_number` VARCHAR(50) COMMENT '执照编号',
  `license_expiry_date` DATE COMMENT '执照有效期',
  `primary_contact_name` VARCHAR(50) COMMENT '主联系人',
  `primary_contact_phone` VARCHAR(20) COMMENT '主联系电话',
  `primary_contact_email` VARCHAR(100) COMMENT '主联系邮箱',
  `website_url` VARCHAR(255) COMMENT '官网',
  `status` VARCHAR(30) COMMENT '合作状态（可选值：潜在、合作中、已终止、待审核）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='合作伙伴主表';
```

### 3.2 高校扩展表（partner_university_info）
```sql
CREATE TABLE `partner_university_info` (
  `university_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `partner_id` BIGINT NOT NULL COMMENT '关联合作伙伴ID',
  `school_type` VARCHAR(50) COMMENT '办学类型',
  `authority` VARCHAR(100) COMMENT '隶属单位',
  `dominant_discipline` VARCHAR(100) COMMENT '优势学科',
  `key_labs` TEXT COMMENT '重点实验室',
  `student_scale` VARCHAR(50) COMMENT '学生规模',
  `faculty_scale` VARCHAR(50) COMMENT '教职工规模',
  `research_funding` DECIMAL(12,2) COMMENT '科研经费',
  `cooperation_intention` TEXT COMMENT '合作意向',
  `academic_leader` VARCHAR(50) COMMENT '学术带头人',
  `tech_transfer_office` VARCHAR(100) COMMENT '技术转移办公室',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='高校扩展信息';
```

### 3.3 企业/供应商扩展表（partner_enterprise_info）
```sql
CREATE TABLE `partner_enterprise_info` (
  `enterprise_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `partner_id` BIGINT NOT NULL COMMENT '关联合作伙伴ID',
  `supplier_type` VARCHAR(50) COMMENT '供应商类型',
  `registered_capital` DECIMAL(12,2) COMMENT '注册资本',
  `business_scope` TEXT COMMENT '经营范围',
  `industry` VARCHAR(50) COMMENT '所属行业',
  `credit_rating` VARCHAR(50) COMMENT '信用评级',
  `annual_revenue` DECIMAL(12,2) COMMENT '年营业额',
  `main_products` TEXT COMMENT '主要产品',
  `quality_certifications` TEXT COMMENT '质量认证',
  `production_capacity` TEXT COMMENT '生产能力',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='企业/供应商扩展信息';
```

### 3.4 合作合同表（partner_contract）
```sql
CREATE TABLE `partner_contract` (
  `contract_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `partner_id` BIGINT NOT NULL COMMENT '关联合作伙伴ID',
  `contract_name` VARCHAR(100) NOT NULL COMMENT '合同名称',
  `contract_number` VARCHAR(50) NOT NULL COMMENT '合同编号',
  `start_date` DATE COMMENT '合同开始日期',
  `end_date` DATE COMMENT '合同结束日期',
  `amount` DECIMAL(12,2) COMMENT '合同金额',
  `status` VARCHAR(30) COMMENT '合同状态（可选值：有效、无效、已终止）',
  `attachment_path` VARCHAR(255) COMMENT '附件路径',
  `signer` VARCHAR(50) COMMENT '签约人',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='合作合同';
```

### 3.5 资质证书表（partner_certificate）
```sql
CREATE TABLE `partner_certificate` (
  `certificate_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `partner_id` BIGINT NOT NULL COMMENT '关联合作伙伴ID',
  `certificate_type` VARCHAR(50) COMMENT '证书类型',
  `certificate_number` VARCHAR(50) COMMENT '证书编号',
  `issuer` VARCHAR(100) COMMENT '发证机构',
  `issue_date` DATE COMMENT '发证日期',
  `expiry_date` DATE COMMENT '到期日期',
  `attachment_path` VARCHAR(255) COMMENT '附件路径',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='合作伙伴资质证书';
```

### 3.6 合作项目表（partner_project）
```sql
CREATE TABLE `partner_project` (
  `project_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `partner_id` BIGINT NOT NULL COMMENT '关联合作伙伴ID',
  `project_name` VARCHAR(100) NOT NULL COMMENT '项目名称',
  `project_manager` VARCHAR(50) COMMENT '项目经理',
  `budget` DECIMAL(12,2) COMMENT '项目预算',
  `status` VARCHAR(30) COMMENT '项目状态（可选值：进行中、已完成、已终止）',
  `description` TEXT COMMENT '项目描述',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='合作项目';
```

### 3.7 联系人表（partner_contact）
```sql
CREATE TABLE `partner_contact` (
  `contact_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `partner_id` BIGINT NOT NULL COMMENT '关联合作伙伴ID',
  `name` VARCHAR(50) NOT NULL COMMENT '姓名',
  `position` VARCHAR(50) COMMENT '职位',
  `department` VARCHAR(50) COMMENT '部门',
  `is_primary` BOOLEAN DEFAULT FALSE COMMENT '是否主要联系人',
  `phone` VARCHAR(20) COMMENT '手机',
  `email` VARCHAR(100) COMMENT '邮箱',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='合作伙伴联系人';
```

### 3.8 银行账户表（partner_bank_account）
```sql
CREATE TABLE `partner_bank_account` (
  `account_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `partner_id` BIGINT NOT NULL COMMENT '关联合作伙伴ID',
  `account_name` VARCHAR(100) NOT NULL COMMENT '账户名',
  `bank_name` VARCHAR(100) COMMENT '银行名称',
  `account_number` VARCHAR(50) COMMENT '账号',
  `is_primary` BOOLEAN DEFAULT FALSE COMMENT '是否主要账户',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='合作伙伴银行账户';
```

### 3.9 评估记录表（partner_evaluation）
```sql
CREATE TABLE `partner_evaluation` (
  `evaluation_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `partner_id` BIGINT NOT NULL COMMENT '关联合作伙伴ID',
  `evaluator` VARCHAR(50) COMMENT '评估人',
  `evaluation_date` DATE COMMENT '评估日期',
  `score` DECIMAL(5,2) COMMENT '综合评分',
  `result` TEXT COMMENT '评估结果',
  `recommendations` TEXT COMMENT '改进建议',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='合作伙伴评估记录';
```

---

## 4. 讲师与课程相关表

### 4.1 讲师表（teacher）
```sql
CREATE TABLE `teacher` (
  `teacher_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '讲师ID',
  `name` VARCHAR(50) NOT NULL COMMENT '讲师姓名',
  `type` VARCHAR(30) COMMENT '讲师类型（可选值：内部讲师、外部讲师）',
  `specialty` VARCHAR(100) COMMENT '擅长领域',
  `phone` VARCHAR(20) COMMENT '联系电话',
  `status` VARCHAR(30) COMMENT '合作状态（可选值：合作中、待沟通）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='讲师信息';
```

### 4.2 课程表（course）
```sql
CREATE TABLE `course` (
  `course_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '课程ID',
  `name` VARCHAR(100) NOT NULL COMMENT '课程名称',
  `category` VARCHAR(50) COMMENT '课程分类',
  `teacher_id` BIGINT COMMENT '主讲老师ID',
  `duration` DECIMAL(5,2) COMMENT '课程时长(小时)',
  `status` VARCHAR(30) COMMENT '课程状态（可选值：已上架、已下架）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='课程信息';
```

---

## 5. 结算、零工与申诉相关表

### 5.1 结算账户表（settlement_account）
```sql
CREATE TABLE `settlement_account` (
  `account_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `user_id` BIGINT NOT NULL COMMENT '关联用户ID',
  `balance` DECIMAL(12,2) DEFAULT 0 COMMENT '账户余额',
  `frozen_amount` DECIMAL(12,2) DEFAULT 0 COMMENT '冻结金额',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='结算账户';
```

### 5.2 结算流水表（settlement_record）
```sql
CREATE TABLE `settlement_record` (
  `record_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `account_id` BIGINT NOT NULL COMMENT '结算账户ID',
  `order_id` BIGINT COMMENT '关联订单ID',
  `amount` DECIMAL(12,2) NOT NULL COMMENT '结算金额',
  `type` VARCHAR(30) COMMENT '流水类型（可选值：收入、支出、冻结、解冻、退款）',
  `status` VARCHAR(30) COMMENT '结算状态（可选值：待结算、已结算、已退款）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='结算流水';
```

### 5.3 零工岗位表（gig_job）
```sql
CREATE TABLE `gig_job` (
  `job_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '岗位ID',
  `partner_id` BIGINT NOT NULL COMMENT '发布企业ID',
  `title` VARCHAR(100) NOT NULL COMMENT '岗位名称',
  `description` TEXT COMMENT '岗位描述',
  `requirement` TEXT COMMENT '岗位要求',
  `reward` DECIMAL(10,2) NOT NULL COMMENT '悬赏金额',
  `status` VARCHAR(30) COMMENT '岗位状态（可选值：招聘中、进行中、待验收、已完成、已关闭）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='零工岗位';
```

### 5.4 零工接单表（gig_order）
```sql
CREATE TABLE `gig_order` (
  `gig_order_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `job_id` BIGINT NOT NULL COMMENT '关联岗位ID',
  `user_id` BIGINT NOT NULL COMMENT '接单用户ID',
  `status` VARCHAR(30) COMMENT '接单状态（可选值：进行中、待验收、已完成、已申诉、已关闭）',
  `submit_at` DATETIME COMMENT '交付申请时间',
  `submit_remark` VARCHAR(500) COMMENT '交付申请说明',
  `accepted_at` DATETIME COMMENT '验收通过时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='零工接单';

ALTER TABLE `gig_order`
ADD COLUMN `reject_reason` VARCHAR(255) COMMENT '拒绝验收理由' AFTER `status`;

```

### 5.5 申诉工单表（dispute_ticket）
```sql
CREATE TABLE `dispute_ticket` (
  `ticket_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '工单ID',
  `order_id` BIGINT NOT NULL COMMENT '关联订单ID',
  `initiator_id` BIGINT NOT NULL COMMENT '申诉方用户ID',
  `respondent_id` BIGINT NOT NULL COMMENT '被申诉方用户ID',
  `type` VARCHAR(50) COMMENT '申诉类型',
  `reason` TEXT COMMENT '申诉理由',
  `status` VARCHAR(30) COMMENT '工单状态（可选值：待处理、处理中、已关闭、已判责）',
  `result` TEXT COMMENT '判责结果',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='申诉工单';


ALTER TABLE `dispute_ticket`
ADD COLUMN `initiator_evidence_text` TEXT COMMENT '申诉方证据文字说明' AFTER `reason`,
ADD COLUMN `initiator_evidence_images` VARCHAR(1000) COMMENT '申诉方证据图片URL（逗号分隔）' AFTER `initiator_evidence_text`,
ADD COLUMN `initiator_evidence_files` VARCHAR(1000) COMMENT '申诉方证据文件URL（逗号分隔）' AFTER `initiator_evidence_images`,
ADD COLUMN `respondent_evidence_text` TEXT COMMENT '被申诉方证据文字说明' AFTER `initiator_evidence_files`,
ADD COLUMN `respondent_evidence_images` VARCHAR(1000) COMMENT '被申诉方证据图片URL（逗号分隔）' AFTER `respondent_evidence_text`,
ADD COLUMN `respondent_evidence_files` VARCHAR(1000) COMMENT '被申诉方证据文件URL（逗号分隔）' AFTER `respondent_evidence_images`,
ADD COLUMN `judgement_reason` TEXT COMMENT '判责理由' AFTER `result`,
ADD COLUMN `is_refund` int(1) DEFAULT 0 COMMENT '是否退款 0-否 1-是' AFTER `judgement_reason`,
ADD COLUMN `compensation_amount` DECIMAL(12,2) DEFAULT 0 COMMENT '补偿金额' AFTER `is_refund`;

```

### 5.5 申诉工单沟通记录表 dispute_ticket_message
```sql
CREATE TABLE `dispute_ticket_message` (
  `message_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `ticket_id` BIGINT NOT NULL COMMENT '关联申诉工单ID',
  `sender_id` BIGINT NOT NULL COMMENT '发送方用户ID',
  `sender_role` VARCHAR(30) NOT NULL COMMENT '发送方角色（运营/申诉方/被申诉方）',
  `content` TEXT COMMENT '沟通内容',
  `images` VARCHAR(1000) COMMENT '图片URL（逗号分隔）',
  `files` VARCHAR(1000) COMMENT '文件URL（逗号分隔）',
  `send_time` DATETIME NOT NULL COMMENT '发送时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='申诉工单沟通记录表';
```

### 6. 审批业务相关表

### 6.1  通用审批流表 （approval_flow）
```sql
CREATE TABLE `approval_flow` (
  `approval_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `biz_type` VARCHAR(50) NOT NULL COMMENT '业务类型（如contract/partner/order等）',
  `biz_id` BIGINT NOT NULL COMMENT '业务ID（如合同ID、合作伙伴ID等）',
  `step` INT NOT NULL COMMENT '审批节点顺序',
  `approver_id` BIGINT NOT NULL COMMENT '审批人后台账号ID',
  `approver_role` VARCHAR(50) COMMENT '审批人角色',
  `action` VARCHAR(20) NOT NULL COMMENT '操作（提交/批准/驳回/转交）',
  `remark` VARCHAR(255) COMMENT '审批意见/备注',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='通用审批流表';
```

### 6.2  审批流程配置主表 approval_flow_config
```sql
CREATE TABLE `approval_flow_config` (
  `config_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `biz_type` VARCHAR(50) NOT NULL COMMENT '业务类型（如contract/partner/order/task等）',
  `config_name` VARCHAR(100) NOT NULL COMMENT '流程名称',
  `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用 1-启用 0-禁用',
  `description` VARCHAR(255) COMMENT '流程说明',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='审批流程配置主表';
```
### 6.2  审批流程节点表 approval_flow_node
```sql
CREATE TABLE `approval_flow_node` (
  `node_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `config_id` BIGINT NOT NULL COMMENT '关联流程配置ID',
  `step` INT NOT NULL COMMENT '节点顺序（1,2,3...）',
  `role_code` VARCHAR(50) NOT NULL COMMENT '审批角色编码（如manager, finance, admin等）',
  `role_name` VARCHAR(100) COMMENT '审批角色名称',
  `is_end` TINYINT(1) DEFAULT 0 COMMENT '是否为终节点 1-是 0-否',
  `description` VARCHAR(255) COMMENT '节点说明',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='审批流程节点表';
```
### 7. 操作日志相关

### 7.1 通用操作日志表 operation_log
```sql
CREATE TABLE `operation_log` (
  `log_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `biz_type` VARCHAR(50) NOT NULL COMMENT '业务类型（如partner/contract/order等）',
  `biz_id` BIGINT NOT NULL COMMENT '业务主键ID',
  `action` VARCHAR(50) NOT NULL COMMENT '操作类型（创建/修改/审批/合同关联/删除等）',
  `operator_id` BIGINT NOT NULL COMMENT '操作人后台账号ID',
  `operator_name` VARCHAR(100) COMMENT '操作人姓名',
  `content` VARCHAR(255) COMMENT '操作内容/变更摘要',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='通用操作日志表';
```

### 8. 发票与税务管理

### 8.1 发票表 invoice
```sql
CREATE TABLE `invoice` (
  `invoice_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `order_id` BIGINT NOT NULL COMMENT '关联订单ID',
  `applicant_id` BIGINT NOT NULL COMMENT '申请人后台账号ID',
  `invoice_type` VARCHAR(30) NOT NULL COMMENT '发票类型（增值税专票/普票等）',
  `title` VARCHAR(100) NOT NULL COMMENT '发票抬头',
  `tax_no` VARCHAR(50) COMMENT '税号',
  `amount` DECIMAL(12,2) NOT NULL COMMENT '开票金额',
  `status` VARCHAR(20) NOT NULL DEFAULT '待开票' COMMENT '发票状态（待开票、已开票、已寄送、已作废）',
  `apply_date` DATETIME NOT NULL COMMENT '申请时间',
  `issue_date` DATETIME COMMENT '开票时间',
  `send_date` DATETIME COMMENT '寄送时间',
  `remark` VARCHAR(255) COMMENT '备注',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='发票申请表';

```

### 9.通知中心

### 
```sql
-- 1. 通知渠道配置表
CREATE TABLE notification_channel (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    name VARCHAR(50) NOT NULL COMMENT '渠道名称',
    type VARCHAR(20) NOT NULL COMMENT '渠道类型（sms/email/inbox等）',
    config JSON NOT NULL COMMENT '配置内容（如AccessKey、SMTP等参数）',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='通知渠道配置表';

-- 2. 通知模板表
CREATE TABLE notification_template (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    type VARCHAR(20) NOT NULL COMMENT '模板类型（sms/email/inbox）',
    content TEXT NOT NULL COMMENT '模板内容（支持变量占位符）',
    variables JSON DEFAULT NULL COMMENT '可用变量列表',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='通知模板表';

-- 3. 触发规则表
CREATE TABLE notification_rule (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    name VARCHAR(100) NOT NULL COMMENT '规则名称',
    trigger_event VARCHAR(100) NOT NULL COMMENT '触发事件标识',
    template_id INT NOT NULL COMMENT '通知模板ID',
    receivers JSON NOT NULL COMMENT '接收人配置',
    channels JSON NOT NULL COMMENT '发送渠道',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='触发规则表';

-- 4. 通知发送历史表
CREATE TABLE notification_dispatch_log (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    rule_id INT NOT NULL COMMENT '触发规则ID',
    template_id INT NOT NULL COMMENT '通知模板ID',
    channel VARCHAR(20) NOT NULL COMMENT '发送渠道',
    receiver VARCHAR(100) NOT NULL COMMENT '接收人标识',
    content TEXT NOT NULL COMMENT '实际发送内容',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '发送状态（0-失败，1-成功）',
    error_msg VARCHAR(255) DEFAULT NULL COMMENT '失败原因',
    sent_at DATETIME NOT NULL COMMENT '发送时间',    
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='通知发送历史表';

-- 5. 业务事件定义表
CREATE TABLE notification_event (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    code VARCHAR(100) NOT NULL COMMENT '事件唯一标识',
    name VARCHAR(100) NOT NULL COMMENT '事件名称',
    description VARCHAR(255) DEFAULT NULL COMMENT '事件描述',
    variables JSON DEFAULT NULL COMMENT '该事件可用变量',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',    
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='业务事件定义表';
```


### 10. 高校实践服务平台

### 10.1 高校实践项目主表 practice_project
```sql
CREATE TABLE `practice_project` (
  `project_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目ID',
  `project_name` VARCHAR(100) NOT NULL COMMENT '项目名称',
  `university_id` BIGINT NOT NULL COMMENT '合作高校ID',
  `enterprise_id` BIGINT COMMENT '实践企业ID',
  `contract_id` BIGINT COMMENT '关联合同ID',
  `start_date` DATE COMMENT '项目开始日期',
  `end_date` DATE COMMENT '项目结束日期',
  `status` VARCHAR(30) COMMENT '项目状态（筹备中、报名中、进行中、已完成、已关闭）',
  `admin_id` BIGINT COMMENT '项目负责人后台账号ID',
  `theme` VARCHAR(255) COMMENT '实践项目主题',
  `objective` TEXT COMMENT '实践内容与目标',
  `requirement` TEXT COMMENT '面向对象与要求',
  `class_count` INT DEFAULT 0 COMMENT '班次数',
  `enroll_confirmed` INT DEFAULT 0 COMMENT '已确认报名人数',
  `enroll_planned` INT DEFAULT 0 COMMENT '计划报名人数',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='高校实践项目主表';

```

###  10.2 实践项目班次/组别表 practice_class
```sql
CREATE TABLE `practice_class` (
  `class_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '班次ID',
  `project_id` BIGINT NOT NULL COMMENT '所属项目ID',
  `class_name` VARCHAR(100) NOT NULL COMMENT '班次名称',
  `start_date` DATE COMMENT '实践开始日期',
  `end_date` DATE COMMENT '实践结束日期',
  `plan_number` INT COMMENT '计划人数',
  `status` VARCHAR(30) COMMENT '班次状态（报名中、已满、进行中、已结束）',
  `enroll_confirmed` INT DEFAULT 0 COMMENT '已确认报名人数',
  `enroll_planned` INT DEFAULT 0 COMMENT '计划报名人数',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='实践项目班次/组别表';
```

###  10.3 班次日程安排表 practice_schedule
```sql

CREATE TABLE `practice_schedule` (
  `schedule_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日程ID',
  `class_id` BIGINT NOT NULL COMMENT '所属班次ID',
  `date` DATE NOT NULL COMMENT '日期',
  `time_range` VARCHAR(50) COMMENT '时间段（如09:00-12:00）',
  `topic` VARCHAR(255) COMMENT '主题/内容',
  `teacher` VARCHAR(100) COMMENT '讲师/负责人',
  `location` VARCHAR(100) COMMENT '地点',
  `remark` VARCHAR(255) COMMENT '备注',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='班次日程安排表';

```

###  10.4 实践项目报名表
```sql
CREATE TABLE `practice_enroll` (
  `enroll_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报名ID',
  `project_id` BIGINT NOT NULL COMMENT '项目ID',
  `class_id` BIGINT NOT NULL COMMENT '班次ID',
  `student_id` BIGINT NOT NULL COMMENT '学生用户ID',
  `name` VARCHAR(50) NOT NULL COMMENT '姓名',
  `student_no` VARCHAR(50) COMMENT '学号',
  `college` VARCHAR(100) COMMENT '学院',
  `phone` VARCHAR(20) COMMENT '手机号',
  `identity_id` VARCHAR(18) COMMENT '身份证号',
  `status` VARCHAR(20) DEFAULT '待确认' COMMENT '报名状态（待确认、已确认、已驳回、已退出）',
  `apply_time` DATETIME DEFAULT NULL COMMENT '报名时间',
  `confirm_time` DATETIME DEFAULT NULL COMMENT '确认时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='实践项目报名表';
```

###  10.5 考勤/打卡表（practice_attendance）
```sql
CREATE TABLE `practice_attendance` (
  `attendance_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '考勤ID',
  `class_id` BIGINT NOT NULL COMMENT '班次ID',
  `schedule_id` BIGINT COMMENT '日程ID',
  `student_id` BIGINT NOT NULL COMMENT '学生用户ID',
  `checkin_time` DATETIME COMMENT '签到时间',
  `status` VARCHAR(20) COMMENT '考勤状态（正常、迟到、缺勤）',
  `remark` VARCHAR(255) COMMENT '备注',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='实践项目考勤表';
```

###  10.6  现场影像表（practice_media）
```sql
CREATE TABLE `practice_media` (
  `media_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '影像ID',
  `project_id` BIGINT NOT NULL COMMENT '项目ID',
  `class_id` BIGINT COMMENT '班次ID',
  `url` VARCHAR(255) NOT NULL COMMENT '图片/视频URL',
  `title` VARCHAR(100) COMMENT '标题/说明',
  `tag` VARCHAR(50) COMMENT '标签（如开营仪式、参观等）',
  `is_featured` TINYINT(1) DEFAULT 0 COMMENT '是否精选',
  `uploaded_by` BIGINT COMMENT '上传人后台账号ID',
  `uploaded_at` DATETIME DEFAULT NULL COMMENT '上传时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='实践项目现场影像表';
```

###  10.7 评价与总结表（practice_evaluation）
```sql
CREATE TABLE `practice_evaluation` (
  `evaluation_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评价ID',
  `project_id` BIGINT NOT NULL COMMENT '项目ID',
  `class_id` BIGINT COMMENT '班次ID',
  `student_id` BIGINT NOT NULL COMMENT '学生用户ID',
  `score` INT COMMENT '评分（1-5）',
  `level` VARCHAR(20) COMMENT '评级（优秀、良好、合格等）',
  `content` TEXT COMMENT '评价内容',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='实践项目评价表';

ALTER TABLE `practice_evaluation`
ADD COLUMN `evaluation_type` VARCHAR(30) DEFAULT 'course' COMMENT '评估类型（course-课程内容/teacher-讲师授课/other-其他）' AFTER `level`;
ADD COLUMN `teacher_id` BIGINT COMMENT '被评估讲师ID' AFTER `evaluation_type`;
```
###  10.8 实践项目报告表（practice_report）
```sql
CREATE TABLE `practice_report` (
  `report_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报告ID',
  `project_id` BIGINT NOT NULL COMMENT '所属项目ID',
  `class_id` BIGINT COMMENT '所属班次ID（为空表示项目总报告）',
  `report_type` VARCHAR(30) NOT NULL COMMENT '报告类型（总报告/班次报告）',
  `title` VARCHAR(200) NOT NULL COMMENT '报告标题',
  `file_url` VARCHAR(255) COMMENT '报告文件URL（如PDF）',
  `content` TEXT COMMENT '报告内容摘要/HTML',
  `generated_by` BIGINT COMMENT '生成操作人后台账号ID',
  `generated_at` DATETIME NOT NULL COMMENT '生成时间',
  `share_url` VARCHAR(255) COMMENT '报告分享链接',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='实践项目报告生成记录表';
```

###  10.9 实践项目报名入口表（practice_enroll_entry）
```sql
CREATE TABLE `practice_enroll_entry` (
  `entry_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报名入口ID',
  `project_id` BIGINT NOT NULL COMMENT '所属项目ID',
  `entry_type` VARCHAR(20) NOT NULL DEFAULT 'project' COMMENT '入口类型（project-项目总入口/class-班次入口）',
  `class_id` BIGINT COMMENT '所属班次ID（如为班次入口时）',
  `qr_code_url` VARCHAR(255) COMMENT '二维码图片URL',
  `enroll_url` VARCHAR(255) NOT NULL COMMENT '报名链接',
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态（active-有效/disabled-禁用/expired-已过期）',
  `expire_at` DATETIME COMMENT '二维码/链接失效时间',
  `generated_by` BIGINT COMMENT '生成操作人后台账号ID',
  `generated_at` DATETIME NOT NULL COMMENT '生成时间',
  `remark` VARCHAR(255) COMMENT '备注',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='实践项目报名入口/二维码表';
```


### 11. 电子证书模板管理中心

### 11.1 电子证书模板表（certificate_template）
```sql
CREATE TABLE `certificate_template` (
  `template_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
  `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
  `template_type` VARCHAR(50) NOT NULL DEFAULT 'practice' COMMENT '模板类型（practice-实践项目/other-其他）',
  `background_url` VARCHAR(255) COMMENT '证书底图URL',
  `content_html` TEXT COMMENT '证书内容HTML模板（支持变量占位符）',
  `preview_url` VARCHAR(255) COMMENT '模板预览图URL',
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态（active-启用/disabled-禁用）',
  `remark` VARCHAR(255) COMMENT '备注',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='电子证书模板表';
```

### 11.2 实践项目证书发放表（practice_certificate）
```sql
CREATE TABLE `practice_certificate` (
  `practice_certificate_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '实践证书ID',
  `project_id` BIGINT NOT NULL COMMENT '所属项目ID',
  `class_id` BIGINT COMMENT '所属班次ID',
  `student_id` BIGINT NOT NULL COMMENT '学员用户ID',
  `template_id` BIGINT NOT NULL COMMENT '证书模板ID',
  `certificate_no` VARCHAR(100) NOT NULL COMMENT '证书编号（唯一）',
  `issue_date` DATE NOT NULL COMMENT '发放日期',
  `score` DECIMAL(5,2) COMMENT '成绩',
  `level` VARCHAR(20) COMMENT '评级（优秀/合格等）',
  `file_url` VARCHAR(255) COMMENT '电子证书文件URL（PDF/JPG）',
  `status` VARCHAR(20) NOT NULL DEFAULT 'valid' COMMENT '状态（valid-有效/invalid-作废）',
  `remark` VARCHAR(255) COMMENT '备注',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='实践项目电子证书发放表';


ALTER TABLE `practice_certificate`
ADD COLUMN `verify_qr_url` VARCHAR(255) COMMENT '查验二维码URL' AFTER `file_url`,
ADD COLUMN `revoke_reason` VARCHAR(255) COMMENT '吊销原因' AFTER `status`,
ADD COLUMN `revoked_at` DATETIME COMMENT '吊销时间' AFTER `revoke_reason`;


```

### 11.3 认证项目表（certification_project）
```sql
CREATE TABLE `certification_project` (
  `project_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '认证项目ID',
  `name` VARCHAR(100) NOT NULL COMMENT '认证名称',
  `field` VARCHAR(100) COMMENT '认证领域',
  `description` TEXT COMMENT '认证简介',
  `issuer` VARCHAR(100) COMMENT '发证机构',
  `certificate_sample_url` VARCHAR(255) COMMENT '证书样本图片URL',
  `apply_condition` TEXT COMMENT '报考条件说明',
  `status` VARCHAR(20) DEFAULT 'active' COMMENT '状态（active-启用/disabled-禁用）',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
) COMMENT='技能认证项目表';
```

### 11.4 认证报名规则表（certification_apply_rule）
```sql
CREATE TABLE `certification_apply_rule` (
  `rule_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '规则ID',
  `project_id` BIGINT NOT NULL COMMENT '关联认证项目ID',
  `rule_content` TEXT NOT NULL COMMENT '规则内容/描述',
  `rule_type` VARCHAR(10) NOT NULL COMMENT '校验类型（strong-强校验/weak-弱校验）',
  `priority` INT DEFAULT 1 COMMENT '优先级，数字越大优先',
  `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='认证报名规则表';
```

### 11.5 认证班次表（certification_class）
```sql
CREATE TABLE `certification_class` (
  `class_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '班次ID',
  `project_id` BIGINT NOT NULL COMMENT '关联认证项目ID',
  `class_name` VARCHAR(100) NOT NULL COMMENT '班次名称',
  `period` VARCHAR(100) COMMENT '培训周期',
  `fee` DECIMAL(10,2) DEFAULT 0 COMMENT '报名费用',
  `quota` INT DEFAULT 0 COMMENT '名额库存',
  `enrolled_count` INT DEFAULT 0 COMMENT '已报名人数',
  `pending_review_count` INT DEFAULT 0 COMMENT '待复核人数',
  `status` VARCHAR(20) DEFAULT 'enrolling' COMMENT '状态（enrolling-报名中/closed-已结束）',
  `start_date` DATE COMMENT '开班日期',
  `end_date` DATE COMMENT '结束日期',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='认证班次/考次表';
```

### 11.6 认证报名表（certification_enroll）
```sql
CREATE TABLE `certification_enroll` (
  `enroll_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报名ID',
  `class_id` BIGINT NOT NULL COMMENT '关联班次ID',
  `student_id` BIGINT NOT NULL COMMENT '学员用户ID',
  `apply_info`
