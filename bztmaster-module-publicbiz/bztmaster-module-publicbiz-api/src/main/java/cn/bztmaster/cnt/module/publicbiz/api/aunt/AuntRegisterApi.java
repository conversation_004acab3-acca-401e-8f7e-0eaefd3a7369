package cn.bztmaster.cnt.module.publicbiz.api.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.AuntRegisterSubmitReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.AuntRegisterSubmitRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.AuntRegisterStatusRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.AuntRegisterDetailRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.AuntRegisterResubmitReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.AgencyListRespDTO;

import java.util.List;

/**
 * 阿姨注册 API 接口
 *
 * <AUTHOR>
 */
public interface AuntRegisterApi {

    /**
     * 提交阿姨注册申请
     *
     * @param reqDTO 注册申请信息
     * @return 申请结果
     */
    CommonResult<AuntRegisterSubmitRespDTO> submitRegister(AuntRegisterSubmitReqDTO reqDTO);

    /**
     * 获取阿姨注册申请状态
     *
     * @param mobile 手机号
     * @param openId 微信openId
     * @return 申请状态
     */
    CommonResult<AuntRegisterStatusRespDTO> getRegisterStatus(String mobile, String openId);

    /**
     * 获取阿姨注册申请详情
     *
     * @param applicationId 申请ID
     * @return 申请详情
     */
    CommonResult<AuntRegisterDetailRespDTO> getRegisterDetail(String applicationId);

    /**
     * 重新提交阿姨注册申请
     *
     * @param reqDTO 重新提交信息
     * @return 申请结果
     */
    CommonResult<AuntRegisterSubmitRespDTO> resubmitRegister(AuntRegisterResubmitReqDTO reqDTO);

    /**
     * 获取家政机构列表
     *
     * @param keyword 搜索关键词
     * @return 机构列表
     */
    CommonResult<List<AgencyListRespDTO>> getAgencyList(String keyword);
}
