spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
      - de.codecentric.boot.admin.client.config.SpringBootAdminClientAutoConfiguration
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 1
        min-idle: 1
        max-active: 20
        max-wait: 600000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        max-evictable-idle-time-millis: 900000
        validation-query: SELECT 1 FROM DUAL
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: **************************************************************************************************************************************************************************************
          username: admin
          password: XoVSeYdfQ3.3
        #          username: sa # SQL Server 连接的示例
        #          password: JSm:g(*%lU4ZAkz06cd52KqT3)i1?H7W # SQL Server 连接的示例
        #          username: SYSDBA # DM 连接的示例
        #          password: SYSDBA # DM 连接的示例
        slave: # 模拟从库，可根据自己需要修改
          lazy: true
          url: **************************************************************************************************************************************************************************************
          username: admin
          password: XoVSeYdfQ3.3

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    host: test-redis.bzmaster.cn
    port: 7018
    database: 1
    password: nPaaL6C8MUdm2ZHf9cfC
# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  name-server: 127.0.0.1:9876

spring:
  # RabbitMQ 配置项，对应 RabbitProperties 配置类
  rabbitmq:
    host: 127.0.0.1
    port: 5672
    username: guest
    password: guest
  # Kafka 配置项，对应 KafkaProperties 配置类
  kafka:
    bootstrap-servers: 127.0.0.1:9092

xxl:
  job:
    enabled: false
    admin:
      addresses: http://127.0.0.1:9090/xxl-job-admin
# Lock4j 配置项
lock4j:
  acquire-timeout: 3000
  expire: 30000

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        instance:
          service-host-type: IP

# 日志文件配置
logging:
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    cn.bztmaster.cnt.module.system.dal.mysql: debug
    cn.bztmaster.cnt.module.system.dal.mysql.sms.SmsChannelMapper: INFO
    org.springframework.context.support.PostProcessorRegistrationDelegate: ERROR
wx:
  mp: # 公众号配置（必填），参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-mp-spring-boot-starter/README.md 文档
    #    app-id: wx041349c6f39b268b # 测试号（牛希尧提供的）
    #    secret: 5abee519483bc9f8cb37ce280e814bd0
    app-id: wx5b23ba7a5589ecbb
    secret: 2a7b3b20c537e52e74afd395eb85f61f
    #    app-id: wxa69ab825b163be19 # 测试号（Kongdy 提供的）
    #    secret: bd4f9fab889591b62aeac0d7b8d8b4a0
    # 存储配置，解决 AccessToken 的跨节点的共享
    config-storage:
      type: RedisTemplate
      key-prefix: wx
      http-client-type: HttpClient
  miniapp: # 小程序配置（必填），参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-miniapp-spring-boot-starter/README.md 文档
    #    appid: wx62056c0d5e8db250 # 测试号（牛希尧提供的）
    #    secret: 333ae72f41552af1e998fe1f54e1584a
    #    appid: wx63c280fe3248a3e7 # wenhualian的接口测试号
    #    secret: 6f270509224a7ae1296bbf1c8cb97aed
    appid: wxc4598c446f8a9cb3
    secret: 4a1a04e07f6a4a0751b39c3064a92c8b
#    appid: wx66186af0759f47c9 # 测试号（puhui 提供的）
#    secret: 3218bcbd112cbc614c7264ceb20144ac
    config-storage:
      type: RedisTemplate
      key-prefix: wa
      http-client-type: HttpClient

# 芋道配置项，设置当前项目所有自定义的配置
bztmaster:
  env: # 多环境的配置项
    tag: ${HOSTNAME}
  captcha:
    enable: false
  security:
    mock-enable: true
  access-log: # 访问日志的配置项
    enable: true
  wxa-code:
    env-version: develop
  wxa-subscribe-message:
    miniprogram-state: developer

justauth:
  enabled: true
  type:
    DINGTALK: # 钉钉
      client-id: dingvrnreaje3yqvzhxg
      client-secret: i8E6iZyDvZj51JIb0tYsYfVQYOks9Cq1lgryEjFRqC79P3iJcrxEwT6Qk2QvLrLI
      ignore-check-redirect-uri: true
    WECHAT_ENTERPRISE: # 企业微信
      client-id: wwd411c69a39ad2e54
      client-secret: 1wTb7hYxnpT2TUbIeHGXGo7T0odav1ic10mLdyyATOw
      agent-id: 1000004
      ignore-check-redirect-uri: true
    # noinspection SpringBootApplicationYaml
    WECHAT_MINI_PROGRAM: # 微信小程序
      client-id: ${wx.miniapp.appid}
      client-secret: ${wx.miniapp.secret}
      ignore-check-redirect-uri: true
      ignore-check-state: true 
    WECHAT_MP: # 微信公众号
      client-id: ${wx.mp.app-id}
      client-secret: ${wx.mp.secret}
      ignore-check-redirect-uri: true
  cache:
    type: REDIS
    prefix: 'social_auth_state:'
    timeout: 24h
