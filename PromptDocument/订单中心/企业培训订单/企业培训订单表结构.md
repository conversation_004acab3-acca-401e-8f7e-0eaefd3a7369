-- ==================== 订单中心相关表 ====================

-- 订单主表
CREATE TABLE `publicbiz_order` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 订单基本信息
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
  `order_type` VARCHAR(30) NOT NULL COMMENT '订单类型：practice-高校实践/training-企业培训/personal-个人培训/domestic-家政服务/certification-考试认证',
  `business_line` VARCHAR(50) NOT NULL COMMENT '业务线：高校实践/企业培训/个人培训与认证/家政服务',
  
  -- 关联信息
  `opportunity_id` VARCHAR(50) COMMENT '关联商机ID',
  `lead_id` VARCHAR(50) COMMENT '关联线索ID',
  
  -- 项目信息
  `project_name` VARCHAR(200) COMMENT '项目名称',
  `project_description` TEXT COMMENT '项目描述',
  `start_date` DATE COMMENT '开始日期',
  `end_date` DATE COMMENT '结束日期',
  
  -- 金额信息
  `total_amount` DECIMAL(12,2) NOT NULL COMMENT '订单总金额',
  `paid_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '已支付金额',
  `refund_amount` DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `payment_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '支付状态：pending-待支付/paid-已支付/refunded-已退款/cancelled-已取消',
  
  -- 订单状态
  `order_status` VARCHAR(30) NOT NULL DEFAULT 'draft' COMMENT '订单状态：draft-草稿/pending_approval-待审批/approving-审批中/approved-已批准/rejected-已拒绝/pending_payment-待支付/executing-执行中/completed-已完成/cancelled-已取消',
  
  -- 负责人信息
  `manager_id` BIGINT COMMENT '负责人ID',
  `manager_name` VARCHAR(50) COMMENT '负责人姓名',
  `manager_phone` VARCHAR(20) COMMENT '负责人电话',
  
  -- 合同信息
  `contract_type` VARCHAR(20) DEFAULT 'electronic' COMMENT '合同类型：electronic-电子合同/paper-纸质合同',
  `contract_file_url` VARCHAR(500) COMMENT '合同文件URL',
  `contract_status` VARCHAR(20) DEFAULT 'unsigned' COMMENT '合同状态：unsigned-未签署/signed-已签署/rejected-已拒绝',
  
  -- 备注信息
  `remark` TEXT COMMENT '备注',
  
  -- 结算相关字段
  `settlement_status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '结算状态：pending-待结算/processing-结算中/completed-已结算/failed-结算失败',
  `settlement_time` DATETIME COMMENT '结算时间',
  `settlement_method` VARCHAR(30) COMMENT '结算方式',
  `is_selected_for_reconciliation` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否被选中生成对账单：0-未选中，1-已选中',
  `selection_time` DATETIME COMMENT '选中时间',
  `selector_id` BIGINT COMMENT '选择人ID',
  `selector_name` VARCHAR(50) COMMENT '选择人姓名',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_business_line` (`business_line`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_settlement_status` (`settlement_status`),
  KEY `idx_is_selected_for_reconciliation` (`is_selected_for_reconciliation`),
  KEY `idx_manager_id` (`manager_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='订单主表';

-- 订单支付记录表
CREATE TABLE `publicbiz_order_payment` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 业务字段
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
  `payment_no` VARCHAR(50) NOT NULL COMMENT '支付单号',
  `payment_type` VARCHAR(20) NOT NULL COMMENT '支付类型：cash-现金/wechat-微信支付/alipay-支付宝/bank_transfer-银行转账/pos-POS机刷卡/other-其他',
  `payment_amount` DECIMAL(12,2) NOT NULL COMMENT '支付金额',
  `payment_status` VARCHAR(20) NOT NULL COMMENT '支付状态：pending-待支付/success-支付成功/failed-支付失败/cancelled-已取消',
  `payment_time` DATETIME COMMENT '支付时间',
  `operator_id` BIGINT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `payment_remark` TEXT COMMENT '支付备注',
  `transaction_id` VARCHAR(100) COMMENT '第三方交易号',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_payment_type` (`payment_type`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_payment_time` (`payment_time`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='订单支付记录表';
 


-- 订单中心处理日志表
CREATE TABLE `publicbiz_order_log` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 关联工单
  `order_no` VARCHAR(64) NOT NULL COMMENT '关联工单编号',
  
  -- 日志信息
  `log_type` VARCHAR(30) NOT NULL COMMENT '日志类型：订单创建/编辑/审批通过/审批驳回/确认收款/完成/系统管理员',
  `log_title` VARCHAR(200) COMMENT '日志标题',
  `log_content` TEXT COMMENT '日志内容',
	`old_status` VARCHAR(50) NOT NULL COMMENT '原状态',
  `new_status` VARCHAR(50) NOT NULL COMMENT '新状态',

  -- 操作人信息
  `operator_id` BIGINT COMMENT '操作人ID',
  `operator_name` VARCHAR(50) COMMENT '操作人姓名',
  `operator_role` VARCHAR(50) COMMENT '操作人角色',
  
  -- 关联方信息（用于显示如"雇主: 王先生"）
  `related_party_type` VARCHAR(30) COMMENT '关联方类型',
  `related_party_name` VARCHAR(100) COMMENT '关联方名称',
  
  -- 索引
  PRIMARY KEY (`id`),
  KEY `idx_work_order_no` (`order_no`),
  KEY `idx_log_type` (`log_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='订单中心处理日志表';

-- 企业培训订单详情表
CREATE TABLE `publicbiz_training_order` (
  -- 公共字段
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  
  -- 关联字段
  `order_id` BIGINT NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单号',
  
  -- 企业信息
  `enterprise_name` VARCHAR(200) NOT NULL COMMENT '企业名称',
  `enterprise_contact` VARCHAR(50) COMMENT '企业联系人',
  `enterprise_phone` VARCHAR(20) COMMENT '企业联系电话',
  `enterprise_email` VARCHAR(100) COMMENT '企业联系邮箱',
  `enterprise_address` VARCHAR(500) COMMENT '企业地址',
  
  -- 培训信息
  `training_project` VARCHAR(200) NOT NULL COMMENT '培训项目名称',
  `training_description` TEXT COMMENT '培训项目描述',
  `participants_count` INT NOT NULL COMMENT '培训人数',
  `training_duration` VARCHAR(50) COMMENT '培训周期',
  `training_location` VARCHAR(500) COMMENT '培训地点',
  `training_type` VARCHAR(50) COMMENT '培训类型：技能培训/管理培训/认证培训',
  
  -- 费用信息
  `per_person_fee` DECIMAL(10,2) COMMENT '人均培训费',
  `total_fee` DECIMAL(10,2) COMMENT '总培训费',
  `material_fee` DECIMAL(10,2) COMMENT '教材费',
  `certification_fee` DECIMAL(10,2) COMMENT '认证费',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_enterprise_name` (`enterprise_name`),
  KEY `idx_training_project` (`training_project`),
  KEY `idx_training_type` (`training_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) COMMENT='企业培训订单详情表';