<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageMapper">

    <select id="selectPackageListByCondition" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO">
        SELECT * FROM publicbiz_service_package 
        <where>
            deleted = 0
            AND status = 'active'
            AND audit_status = 'approved'
            <if test="categoryId != null">
                AND category_id = #{categoryId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%') 
                OR service_description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        <choose>
            <when test="sortType == 'price'">
                ORDER BY price ASC
            </when>
            <when test="sortType == 'satisfaction'">
                <!-- 暂时按创建时间排序，因为没有满意度字段 -->
                ORDER BY create_time DESC
            </when>
            <when test="sortType == 'comprehensive'">
                <!-- 暂时按价格和创建时间综合排序 -->
                ORDER BY price ASC, create_time DESC
            </when>
            <otherwise>
                <!-- 默认按距离排序，这里简化处理，实际应该计算距离 -->
                ORDER BY id DESC
            </otherwise>
        </choose>
        <if test="page != null and pageSize != null">
            LIMIT #{pageSize} OFFSET #{offset}
        </if>
    </select>

    <select id="selectMaxPriceByCategory" resultType="java.math.BigDecimal">
        SELECT MAX(price) FROM publicbiz_service_package WHERE deleted = 0 AND category_id = #{categoryId}
    </select>

    <select id="selectRecommendationsByAgencyId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO">
        SELECT * FROM publicbiz_service_package 
        WHERE deleted = 0 AND agency_id = #{agencyId} AND status = 'active'
        ORDER BY id DESC
    </select>

    <select id="selectRecommendationCountByAgencyId" resultType="java.lang.Long">
        SELECT COUNT(*) FROM publicbiz_service_package 
        WHERE deleted = 0 AND agency_id = #{agencyId} AND status = 'active'
    </select>

</mapper> 