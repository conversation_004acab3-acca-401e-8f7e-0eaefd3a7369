# 服务套餐创建时合作伙伴名称设置逻辑验证

## 修改概述

在ServicePackageServiceImpl的createServicePackage方法中添加了合作伙伴名称自动设置逻辑，确保在设置partnerId后同时设置对应的partnerName。

## 修改详情

### 1. 添加依赖注入 ✅

**新增import**：
```java
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.partner.PartnerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO;
```

**新增注入**：
```java
@Resource
private PartnerMapper partnerMapper;
```

### 2. 合作伙伴名称设置逻辑 ✅

**添加位置**：在合作伙伴ID设置逻辑之后，审核状态设置之前

**完整逻辑**：
```java
// 设置合作伙伴ID和审核状态
Long currentUserPartnerId = SecurityFrameworkUtils.getLoginUserPartnerId();
if (createReqVO.getPartnerId() == null) {
    // 如果前端未传递合作伙伴ID，则使用当前登录用户的合作伙伴ID
    servicePackage.setPartnerId(currentUserPartnerId);
}

// 设置合作伙伴名称
Long finalPartnerId = servicePackage.getPartnerId();
if (finalPartnerId != null) {
    try {
        PartnerDO partner = partnerMapper.selectById(finalPartnerId);
        if (partner != null && !Boolean.TRUE.equals(partner.getDeleted())) {
            servicePackage.setPartnerName(partner.getName());
        } else {
            servicePackage.setPartnerName(null);
            log.warn("合作伙伴ID {} 对应的合作伙伴不存在或已删除", finalPartnerId);
        }
    } catch (Exception e) {
        servicePackage.setPartnerName(null);
        log.error("查询合作伙伴信息失败，partnerId: {}", finalPartnerId, e);
    }
}

servicePackage.setAuditStatus("auditing"); // 默认审核中
```

## 逻辑特点

### 1. 安全性保障 ✅
- **空值检查**：只有在partnerId不为null时才执行查询
- **异常处理**：使用try-catch捕获查询异常，避免影响主流程
- **删除状态检查**：验证合作伙伴是否已删除

### 2. 数据一致性 ✅
- **自动同步**：partnerId和partnerName自动保持一致
- **优先级处理**：优先使用前端传递的partnerId，其次使用当前用户的partnerId
- **失败处理**：查询失败时将partnerName设置为null，保证数据完整性

### 3. 性能优化 ✅
- **条件查询**：只有在partnerId不为null时才执行数据库查询
- **单次查询**：使用selectById进行高效的主键查询
- **日志记录**：记录警告和错误日志，便于问题排查

## 执行流程

### 场景1：前端传递partnerId
1. 使用前端传递的partnerId
2. 根据partnerId查询合作伙伴信息
3. 设置partnerName为查询到的合作伙伴名称

### 场景2：前端未传递partnerId
1. 使用当前登录用户的partnerId
2. 根据partnerId查询合作伙伴信息
3. 设置partnerName为查询到的合作伙伴名称

### 场景3：partnerId为null
1. 跳过合作伙伴名称查询
2. partnerName保持为null（或前端传递的值）

### 场景4：查询失败或合作伙伴不存在
1. 设置partnerName为null
2. 记录相应的警告或错误日志

## 错误处理

### 1. 合作伙伴不存在
```java
if (partner != null && !Boolean.TRUE.equals(partner.getDeleted())) {
    servicePackage.setPartnerName(partner.getName());
} else {
    servicePackage.setPartnerName(null);
    log.warn("合作伙伴ID {} 对应的合作伙伴不存在或已删除", finalPartnerId);
}
```

### 2. 数据库查询异常
```java
try {
    // 查询逻辑
} catch (Exception e) {
    servicePackage.setPartnerName(null);
    log.error("查询合作伙伴信息失败，partnerId: {}", finalPartnerId, e);
}
```

## 测试场景

### 1. 正常场景测试
- ✅ 前端传递有效的partnerId
- ✅ 前端未传递partnerId，使用当前用户的partnerId
- ✅ 合作伙伴存在且未删除

### 2. 异常场景测试
- ✅ partnerId为null
- ✅ partnerId对应的合作伙伴不存在
- ✅ partnerId对应的合作伙伴已删除
- ✅ 数据库查询异常

### 3. 边界场景测试
- ✅ 当前用户的partnerId为null
- ✅ 前端传递的partnerId为0或负数
- ✅ 合作伙伴名称为空字符串

## 日志记录

### 1. 警告日志
```
合作伙伴ID {} 对应的合作伙伴不存在或已删除
```
- **触发条件**：partnerId存在但查询不到有效的合作伙伴记录
- **处理方式**：设置partnerName为null

### 2. 错误日志
```
查询合作伙伴信息失败，partnerId: {}
```
- **触发条件**：数据库查询过程中发生异常
- **处理方式**：设置partnerName为null，记录异常堆栈

## 数据一致性保证

### 1. 创建时一致性 ✅
- partnerId和partnerName在创建时自动保持一致
- 即使前端只传递partnerId，系统也会自动设置对应的partnerName

### 2. 更新时一致性 ✅
- 已在updateServicePackage方法中添加类似逻辑
- 确保更新partnerId时同步更新partnerName
- 只有在partnerId发生变化时才执行查询，提高性能

**updateServicePackage方法中的逻辑**：
```java
// 同步合作伙伴名称（如果partnerId发生变化）
if (updateReqVO.getPartnerId() != null &&
    !Objects.equals(updateReqVO.getPartnerId(), oldServicePackage.getPartnerId())) {
    try {
        PartnerDO partner = partnerMapper.selectById(updateReqVO.getPartnerId());
        if (partner != null && !Boolean.TRUE.equals(partner.getDeleted())) {
            servicePackage.setPartnerName(partner.getName());
        } else {
            servicePackage.setPartnerName(null);
            log.warn("更新服务套餐时，合作伙伴ID {} 对应的合作伙伴不存在或已删除", updateReqVO.getPartnerId());
        }
    } catch (Exception e) {
        servicePackage.setPartnerName(null);
        log.error("更新服务套餐时查询合作伙伴信息失败，partnerId: {}", updateReqVO.getPartnerId(), e);
    }
}
```

### 3. 查询时一致性
- 查询结果中的partnerId和partnerName保持一致
- 避免数据不一致导致的显示问题

## 性能影响分析

### 1. 额外查询开销
- 每次创建服务套餐时增加一次合作伙伴查询
- 使用主键查询，性能影响较小

### 2. 优化建议
- 考虑添加合作伙伴信息缓存
- 批量创建时可以考虑批量查询优化

## 完整修改总结

### 1. createServicePackage方法 ✅
- 在设置partnerId后自动查询并设置partnerName
- 支持前端传递partnerId或使用当前用户的partnerId
- 完善的异常处理和日志记录

### 2. updateServicePackage方法 ✅
- 在partnerId发生变化时自动同步partnerName
- 使用Objects.equals比较，避免空指针异常
- 只有在partnerId变化时才执行查询，优化性能

### 3. 性能优化特点
- **条件查询**：只在必要时执行数据库查询
- **变化检测**：更新时只在partnerId变化时才查询
- **主键查询**：使用高效的selectById方法

### 4. 错误处理机制
- **异常捕获**：防止查询异常影响主流程
- **日志记录**：详细记录警告和错误信息
- **降级处理**：查询失败时设置partnerName为null

## 结论

合作伙伴名称设置逻辑已成功添加到创建和更新方法中，具备以下特点：
1. **安全可靠**：完善的异常处理和空值检查
2. **数据一致**：partnerId和partnerName在创建和更新时自动同步
3. **性能友好**：条件查询和变化检测，避免不必要的数据库操作
4. **易于维护**：清晰的日志记录和错误处理
5. **完整覆盖**：创建和更新操作都支持合作伙伴名称自动设置

修改符合所有要求，确保了数据的完整性和一致性，同时保持了良好的性能和可维护性。
