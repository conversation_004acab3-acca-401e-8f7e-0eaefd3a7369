package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.mzt.logapi.starter.annotation.DiffLogField;
import java.util.Date;

@Data
@Schema(description = "师资库 - 新增讲师资质 Request VO")
public class TeacherCertSaveReqVO {
    @Schema(description = "资质文件ID（更新时必填，新增时为空）")
    private Long id;
    private Long teacherId;
    @DiffLogField(name = "证件类型")
    private String certType;
    @DiffLogField(name = "证件名称")
    private String certName;
    @DiffLogField(name = "文件名")
    private String fileName;
    @DiffLogField(name = "文件URL")
    private String fileUrl;
    @Schema(description = "证件有效期开始时间")
    @DiffLogField(name = "证件有效期开始时间", function = "compareDate")
    private Date validStartDate;
    @Schema(description = "证件有效期结束时间")
    @DiffLogField(name = "证件有效期结束时间", function = "compareDate")
    private Date validEndDate;
} 