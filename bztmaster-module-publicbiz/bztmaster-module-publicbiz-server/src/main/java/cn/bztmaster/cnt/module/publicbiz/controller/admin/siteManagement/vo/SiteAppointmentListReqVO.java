package cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static cn.bztmaster.cnt.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 管理后台 - 场地预约列表 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 场地预约列表 Request VO")
@Data
public class SiteAppointmentListReqVO {

    @Schema(description = "场地ID筛选", example = "1")
    private Long siteId;

    @Schema(description = "活动类型筛选", example = "培训")
    private String activityType;

    @Schema(description = "预约状态筛选", example = "已确认")
    private String status;

    @Schema(description = "开始日期筛选", example = "2025-08-15")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期筛选", example = "2025-08-20")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @Schema(description = "联系人姓名筛选", example = "张老师")
    private String contactName;

}
