# 阿姨注册后端接口开发文档

## 项目概述

本文档为传能家政小程序阿姨注册功能的后端接口开发提供详细的技术规范和实现指导。阿姨注册流程包含4个步骤：手机验证、信息录入、归属选择、证件上传。

## 数据库表结构

### 1. 阿姨基本信息表 (publicbiz_practitioner)

```sql
CREATE TABLE `publicbiz_practitioner` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `aunt_oneid` VARCHAR(36) NOT NULL DEFAULT '' COMMENT '阿姨OneID GUID',
  
  -- 基本信息字段
  `name` VARCHAR(50) NOT NULL COMMENT '阿姨姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号，用于登录',
  `id_card` VARCHAR(18) NOT NULL COMMENT '身份证号',
  `hometown` VARCHAR(100) COMMENT '籍贯，如：四川成都',
  `age` INT COMMENT '年龄',
  `gender` VARCHAR(10) COMMENT '性别：male-男/female-女',
  `avatar` VARCHAR(500) COMMENT '头像URL',

  -- 服务信息字段
  `service_type` VARCHAR(50) NOT NULL COMMENT '主要服务类型：月嫂/育儿嫂/保洁/护工',
  `experience_years` INT NOT NULL COMMENT '从业年限',
  `platform_status` VARCHAR(20) NOT NULL DEFAULT 'cooperating' COMMENT '平台状态：cooperating-合作中/terminated-已解约',
  `rating` DECIMAL(2,1) NOT NULL DEFAULT 4.5 COMMENT '评级，1.0-5.0',

  -- 机构关联字段
  `agency_id` BIGINT COMMENT '所属机构ID',
  `agency_name` VARCHAR(200) COMMENT '所属机构名称',

  -- 状态字段
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-正常/inactive-停用/pending-待审核',
  `current_status` VARCHAR(50) COMMENT '当前状态：服务中/待岗/休假中',
  `current_order_id` VARCHAR(50) COMMENT '当前服务订单ID',

  -- 统计字段
  `total_orders` INT NOT NULL DEFAULT 0 COMMENT '累计服务单数',
  `total_income` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '累计收入',
  `customer_satisfaction` DECIMAL(3,1) COMMENT '客户满意度评分',

  PRIMARY KEY (`id`)
) COMMENT='阿姨基本信息表';
```

### 2. 阿姨资质文件表 (publicbiz_practitioner_qualification)

```sql
CREATE TABLE `publicbiz_practitioner_qualification` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',

  -- 业务字段
  `practitioner_oneId` VARCHAR(36) NOT NULL COMMENT '阿姨oneID',
  `file_type` VARCHAR(50) NOT NULL COMMENT '文件类型：id_card-身份证/health_cert-健康证/skill_cert-专业技能证书/other-其他附件',
  `file_name` VARCHAR(200) NOT NULL COMMENT '文件名',
  `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL',
  `file_size` BIGINT COMMENT '文件大小（字节）',
  `file_extension` VARCHAR(20) COMMENT '文件扩展名',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序，数字越小越靠前',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-有效，0-无效',

  PRIMARY KEY (`id`)
) COMMENT='阿姨资质文件表';
```

## API接口设计

### 1. 提交阿姨注册申请

**接口地址**: `POST /publicbiz/aunt/register/submit`

**功能描述**: 提交阿姨注册申请，包含基本信息、机构选择和证件上传

**请求参数**:
```json
{
  "mobile": "15527495882",
  "openId": "wx_openid_123456",
  "name": "小月测试",
  "idCard": "******************",
  "selectedAgency": "platform", // "platform" 表示平台自营，数字表示机构ID
  "idCardFront": "https://example.com/id_front.jpg",
  "idCardBack": "https://example.com/id_back.jpg",
  "healthCert": "https://example.com/health_cert.jpg",
  "skillCert": "https://example.com/skill_cert.jpg" // 可选
}
```

**响应数据**:
```json
{
  "code": 0,
  "msg": "申请提交成功",
  "data": {
    "applicationId": "APP202412160001",
    "auntOneId": "aunt_guid_123456",
    "status": "pending",
    "submitTime": "2024-12-16 10:30:00",
    "estimatedReviewTime": "1-2个工作日"
  }
}
```

**业务逻辑**:
1. 验证手机号格式和唯一性
2. 验证身份证号格式和唯一性
3. 生成阿姨OneID (UUID)
4. 保存阿姨基本信息到 `publicbiz_practitioner` 表
5. 保存证件文件信息到 `publicbiz_practitioner_qualification` 表
6. 生成申请编号并返回

### 2. 获取阿姨注册申请状态

**接口地址**: `GET /publicbiz/aunt/register/status`

**功能描述**: 根据手机号和openId查询阿姨注册申请状态

**请求参数**:
```
mobile: 15527495882
openId: wx_openid_123456
```

**响应数据**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "applicationId": "APP202412160001",
    "auntOneId": "aunt_guid_123456",
    "status": "pending", // pending-待审核/approved-已通过/rejected-已拒绝
    "submitTime": "2024-12-16 10:30:00",
    "reviewTime": "2024-12-16 14:30:00",
    "rejectReason": "身份证照片不清晰，请重新上传",
    "canResubmit": true
  }
}
```

### 3. 获取阿姨注册申请详情

**接口地址**: `GET /publicbiz/aunt/register/detail`

**功能描述**: 获取阿姨注册申请的详细信息

**请求参数**:
```
applicationId: APP202412160001
```

**响应数据**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": {
    "applicationId": "APP202412160001",
    "auntOneId": "aunt_guid_123456",
    "status": "rejected",
    "submitTime": "2024-12-16 10:30:00",
    "reviewTime": "2024-12-16 14:30:00",
    "rejectReason": "身份证照片不清晰，请重新上传",
    "basicInfo": {
      "name": "小月测试",
      "phone": "15527495882",
      "idCard": "******************"
    },
    "agencyInfo": {
      "agencyId": null,
      "agencyName": "平台自营"
    },
    "certificates": {
      "idCardFront": "https://example.com/id_front.jpg",
      "idCardBack": "https://example.com/id_back.jpg",
      "healthCert": "https://example.com/health_cert.jpg",
      "skillCert": "https://example.com/skill_cert.jpg"
    }
  }
}
```

### 4. 重新提交阿姨注册申请

**接口地址**: `POST /publicbiz/aunt/register/resubmit`

**功能描述**: 重新提交被拒绝的阿姨注册申请

**请求参数**:
```json
{
  "applicationId": "APP202412160001",
  "mobile": "15527495882",
  "openId": "wx_openid_123456",
  "name": "小月测试",
  "idCard": "******************",
  "selectedAgency": "platform",
  "idCardFront": "https://example.com/id_front_new.jpg",
  "idCardBack": "https://example.com/id_back_new.jpg",
  "healthCert": "https://example.com/health_cert_new.jpg",
  "skillCert": "https://example.com/skill_cert_new.jpg"
}
```

**响应数据**:
```json
{
  "code": 0,
  "msg": "重新提交成功",
  "data": {
    "applicationId": "APP202412160002",
    "auntOneId": "aunt_guid_123456",
    "status": "pending",
    "submitTime": "2024-12-16 15:30:00",
    "estimatedReviewTime": "1-2个工作日"
  }
}
```

### 6. 获取家政机构列表

**接口地址**: `GET /publicbiz/aunt/register/agencies`

**功能描述**: 获取可选择的家政机构列表

**请求参数**:
```
keyword: XX家政 (可选，搜索关键词)
```

**响应数据**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": [
    {
      "id": 1,
      "name": "XX家政",
      "description": "专业家政服务，信誉良好",
      "status": "cooperating"
    },
    {
      "id": 2,
      "name": "YY家政",
      "description": "服务周到，客户满意度高",
      "status": "cooperating"
    },
    {
      "id": 3,
      "name": "ZZ家政",
      "description": "经验丰富，服务专业",
      "status": "cooperating"
    }
  ]
}
```

## 业务逻辑实现

### 1. 数据验证规则

#### 手机号验证
- 格式：11位数字
- 唯一性：同一手机号只能注册一次
- 微信授权：支持微信授权获取的手机号直接通过验证

#### 身份证号验证
- 格式：15位或18位身份证号
- 唯一性：同一身份证号只能注册一次
- 校验位：18位身份证号需要验证校验位

#### 证件照片验证
- 格式：jpg、png、jpeg
- 大小：单张不超过5MB
- 清晰度：支持OCR识别验证

### 2. 状态流转

```
未注册 -> 提交申请 -> 待审核 -> 审核通过/审核拒绝
                                    ↓
                              重新提交 -> 待审核
```

### 3. 机构选择逻辑

- **平台自营**: `selectedAgency = "platform"`，`agency_id = null`
- **机构加盟**: `selectedAgency = 机构ID`，`agency_id = 机构ID`


## 错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 0 | 成功 | 操作成功 |
| 1001 | 手机号格式错误 | 手机号必须是11位数字 |
| 1002 | 手机号已注册 | 该手机号已被注册 |
| 1003 | 身份证号格式错误 | 身份证号格式不正确 |
| 1004 | 身份证号已注册 | 该身份证号已被注册 |
| 1005 | 必填信息不完整 | 请填写完整的注册信息 |
| 1006 | 证件照片上传失败 | 文件上传失败，请重试 |
| 1007 | 申请不存在 | 注册申请不存在 |
| 1008 | 申请状态不允许重新提交 | 当前状态不允许重新提交 |
| 1009 | 机构不存在 | 选择的机构不存在 |
| 1010 | 文件格式不支持 | 只支持jpg、png、jpeg格式 |
| 1011 | 文件大小超限 | 文件大小不能超过5MB |


## 安全考虑

### 1. 数据脱敏
- 身份证号显示时进行脱敏处理
- 手机号显示时进行脱敏处理

### 2. 权限控制
- 只能查看和操作自己的注册申请
- 管理员可以查看所有申请

### 3. 数据加密
- 身份证号等敏感信息需要加密存储
- 文件上传需要验证文件安全性

### 4. 防重复提交
- 使用幂等性设计
- 添加请求频率限制

## 性能优化

### 1. 数据库优化
- 为常用查询字段添加索引
- 使用分页查询避免大量数据查询

### 2. 文件上传优化
- 支持断点续传
- 文件压缩处理
- CDN加速访问

### 3. 缓存策略
- 机构列表缓存
- 申请状态缓存

## 监控和日志

### 1. 操作日志
- 记录所有注册相关操作
- 包含操作人、操作时间、操作内容

### 2. 异常监控
- 文件上传失败监控
- 数据库操作异常监控
- 接口调用异常监控

### 3. 业务统计
- 注册申请数量统计
- 审核通过率统计
- 各机构注册人数统计

## 测试用例

### 1. 正常注册流程
1. 提交完整注册信息
2. 验证申请状态为"待审核"
3. 验证数据库记录正确

### 2. 异常情况测试
1. 重复手机号注册
2. 重复身份证号注册
3. 文件上传失败
4. 网络异常处理

### 3. 边界条件测试
1. 最大文件大小限制
2. 特殊字符处理
3. 超长文本处理

## 部署说明

### 1. 环境要求
- JDK 8+
- MySQL 5.7+
- Redis 3.0+
- 文件存储服务

### 2. 配置项
- 数据库连接配置
- 文件上传配置
- 短信服务配置
- 微信API配置

### 3. 启动顺序
1. 启动数据库服务
2. 启动Redis服务
3. 启动文件存储服务
4. 启动应用服务

---

**注意**: 本文档仅用于开发参考，实际实现时请根据具体技术栈和业务需求进行调整。
