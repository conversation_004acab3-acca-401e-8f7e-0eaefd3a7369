package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 工单类型统计响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单类型统计响应 VO")
@Data
public class WorkOrderTypeStatsRespVO {

    @Schema(description = "总工单数", example = "100")
    private Integer all;

    @Schema(description = "投诉工单数", example = "30")
    private Integer complaint;

    @Schema(description = "换人申请工单数", example = "25")
    private Integer substitutionRequest;

    @Schema(description = "请假/顶岗工单数", example = "20")
    private Integer takeLeave;

} 