package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 课程课时 - 新增/更新 Request VO
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/components/ManagementCourseForOnline.vue
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "课程课时 - 新增/更新 Request VO")
public class CourseLessonSaveReqVO {

    @Schema(description = "课时ID", example = "1")
    private Long id;

    @NotNull(message = "课程ID不能为空")
    @Schema(description = "课程ID", example = "123", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long courseId;

    @NotNull(message = "章节ID不能为空")
    @Schema(description = "章节ID", example = "1001", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long chapterId;

    @NotBlank(message = "课时标题不能为空")
    @Schema(description = "课时标题", example = "课时1：SWOT分析法概述", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    @NotBlank(message = "课时类型不能为空")
    @Schema(description = "课时类型", example = "视频", requiredMode = Schema.RequiredMode.REQUIRED)
    private String lessonType;

    @Schema(description = "是否免费试看", example = "true")
    private Boolean isFree;

    @Schema(description = "关联素材ID", example = "material001")
    private String materialId;

    @NotBlank(message = "关联素材名称不能为空")
    @Schema(description = "关联素材名称", example = "课程导论.mp4", requiredMode = Schema.RequiredMode.REQUIRED)
    private String materialName;

    @Schema(description = "关联素材文件URL", example = "https://example.com/video1.mp4")
    private String materialFileUrl;

    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;
}
