package cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 课程附件 - 新增 Request VO
 * 对应前端页面：src/views/infra/ResourceCenter/DigitalAsset/components/ManagementCourse.vue
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "课程附件 - 新增 Request VO")
public class CourseAttachmentSaveReqVO {

    @NotNull(message = "课程ID不能为空")
    @Schema(description = "课程ID", example = "123", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long courseId;

    @NotBlank(message = "附件名称不能为空")
    @Schema(description = "附件名称", example = "开课须知.md", requiredMode = Schema.RequiredMode.REQUIRED)
    private String attachmentName;

    @NotBlank(message = "附件类型不能为空")
    @Schema(description = "附件类型", example = "文档", requiredMode = Schema.RequiredMode.REQUIRED)
    private String attachmentType;

    @NotBlank(message = "文件URL不能为空")
    @Schema(description = "文件URL", example = "https://example.com/notice.md", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fileUrl;

    @Schema(description = "文件大小", example = "2048")
    private Long fileSize;
}
