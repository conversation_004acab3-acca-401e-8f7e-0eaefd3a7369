# 1.生成增加字段SQL脚本
请参照PromptDocument/系统管理/系统管理数据表脚本.md中的用户表结构生成增加字段SQL脚本,字段如下：
- 所属合作伙伴
- 所属合作伙伴名称
放置在login_ip字段前面。

# 2.生成后端代码
根据SQL脚本新增的字段，改造’bztmaster-module-system/bztmaster-module-system-server/src/main/java/cn/bztmaster/cnt/module/system/controller/admin/user/UserController.java‘
中的接口，改造如下：
- 新增用户和修改用户接口，当用户类型是合作伙伴时，需要所属合作伙伴ID和名称字段必填。
- 改造获得用户分页列表接口，入参增加所属合作伙伴ID和名称字段，出参增加账户类型和名称、所属合作伙伴ID和名称字段。
- 改造获得用户详情接口，出参增加账户类型、所属合作伙伴ID。


