package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 工单阿姨响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 工单阿姨响应 VO")
@Data
public class WorkOrderPractitionerRespVO {

    @Schema(description = "主键", example = "1")
    private Long id;

    @Schema(description = "OneID GUID", example = "a1b2c3d4-e5f6-7890-abcd-ef1234567890")
    private String auntOneid;

    @Schema(description = "阿姨姓名", example = "李阿姨")
    private String name;

    @Schema(description = "评级", example = "4.5")
    private BigDecimal rating;

    @Schema(description = "主要服务类型", example = "月嫂/育儿嫂/保洁/护工")
    private String serviceType;
} 