-- OAuth2客户端初始化脚本
-- 为雇主端小程序添加OAuth2客户端配置
INSERT INTO system_oauth2_client (
    client_id,
    secret,
    name,
    logo,
    description,
    status,
    access_token_validity_seconds,
    refresh_token_validity_seconds,
    redirect_uris,
    authorized_grant_types,
    scopes,
    auto_approve_scopes,
    authorities,
    resource_ids,
    additional_information,
    creator,
    create_time,
    updater,
    update_time,
    deleted
) VALUES (
             'wxf0cb5e5168f30b6a', -- 微信小程序appid作为客户端ID
             'c4add048842fa0d7149d82f51f2d1852', -- 微信小程序secret作为客户端密钥
             '雇主端小程序',
             'https://example.com/logo.png',
             '雇主端小程序OAuth2客户端',
             0, -- 状态：0-启用，1-禁用
             7200, -- 访问令牌有效期2小时
             604800, -- 刷新令牌有效期7天
             'https://example.com/callback',
             'password,refresh_token',
             'read',
             'read',
             'read', -- 权限
             '', -- 资源
             '{"app_type": "miniprogram", "user_type": "employer"}', -- 附加信息
             'system',
             NOW(),
             'system',
             NOW(),
             0
         ) ON DUPLICATE KEY UPDATE
    secret = VALUES(secret),
    name = VALUES(name),
    description = VALUES(description),
    status = VALUES(status),
    access_token_validity_seconds = VALUES(access_token_validity_seconds),
    refresh_token_validity_seconds = VALUES(refresh_token_validity_seconds),
    authorized_grant_types = VALUES(authorized_grant_types),
    scopes = VALUES(scopes),
    auto_approve_scopes = VALUES(auto_approve_scopes),
    authorities = VALUES(authorities),
    additional_information = VALUES(additional_information),
    updater = VALUES(updater),
    update_time = VALUES(update_time);
