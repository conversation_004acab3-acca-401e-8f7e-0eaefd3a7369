package cn.bztmaster.cnt.module.publicbiz.enums;

/**
 * PublicBiz 操作日志枚举
 * 目的：统一管理公共业务相关操作日志类型、子类型、模板
 */
public interface LogRecordConstants {
    // ======================= TEACHER 讲师 =======================
    String TEACHER_TYPE = "TEACHER 讲师";
    String TEACHER_CREATE_SUB_TYPE = "创建讲师";
    String TEACHER_CREATE_SUCCESS = "创建了讲师【{{#teacher.name}}】";
    String TEACHER_UPDATE_SUB_TYPE = "更新讲师";
    String TEACHER_UPDATE_SUCCESS = "更新了讲师【{{#teacher.name}}】: {{#fieldChanges}}{{#certChangeLog != null ? '；' + #certChangeLog : ''}}";
    String TEACHER_DELETE_SUB_TYPE = "删除讲师";
    String TEACHER_DELETE_SUCCESS = "删除了讲师【{{#teacher.name}}】{{#certDeleteLog != null ? '；' + #certDeleteLog : ''}}";

    // ======================= TEACHER_CERT 讲师资质 =======================
    String TEACHER_CERT_TYPE = "TEACHER 讲师资质";
    String TEACHER_CERT_CREATE_SUB_TYPE = "新增讲师资质";
    String TEACHER_CERT_CREATE_SUCCESS = "新增了讲师【{{#cert.teacherId}}】的资质【{{#cert.certName}}】";
    String TEACHER_CERT_DELETE_SUB_TYPE = "删除讲师资质";
    String TEACHER_CERT_DELETE_SUCCESS = "删除了讲师【{{#cert.teacherId}}】的资质【{{#cert.certName}}】";

    // ======================= PARTNER 合作伙伴 =======================
    String PARTNER_TYPE = "PARTNER 合作伙伴";
    String PARTNER_CREATE_SUB_TYPE = "新增合作伙伴";
    String PARTNER_CREATE_SUCCESS = "新增了合作伙伴【{{#partner.name}}】";
    String PARTNER_UPDATE_SUB_TYPE = "更新合作伙伴";
    String PARTNER_UPDATE_SUCCESS = "更新了合作伙伴【{{#partner.name}}】: {_DIFF{#updateReqVO}}";
    String PARTNER_DELETE_SUB_TYPE = "删除合作伙伴";
    String PARTNER_DELETE_SUCCESS = "删除了合作伙伴【{{#partner.name}}】";

    // ======================= SERVICE_PACKAGE 服务套餐 =======================
    String SERVICE_PACKAGE_TYPE = "SERVICE_PACKAGE 服务套餐";
    String SERVICE_PACKAGE_CREATE_SUB_TYPE = "新增服务套餐";
    String SERVICE_PACKAGE_CREATE_SUCCESS = "新增了服务套餐【{{#servicePackage.name}}】";
    String SERVICE_PACKAGE_UPDATE_SUB_TYPE = "更新服务套餐";
    String SERVICE_PACKAGE_UPDATE_SUCCESS = "更新了服务套餐【{{#servicePackage.name}}】: {{#fieldChanges}}{{#carouselChangeLog != null ? '；' + #carouselChangeLog : ''}}{{#featureChangeLog != null ? '；' + #featureChangeLog : ''}}";
    String SERVICE_PACKAGE_DELETE_SUB_TYPE = "删除服务套餐";
    String SERVICE_PACKAGE_DELETE_SUCCESS = "删除了服务套餐【{{#servicePackage.name}}】";
    String SERVICE_PACKAGE_MOVE_TO_RECYCLE_SUB_TYPE = "移动服务套餐到回收站";
    String SERVICE_PACKAGE_MOVE_TO_RECYCLE_SUCCESS = "将服务套餐【{{#servicePackage.name}}】移动到回收站";
    String SERVICE_PACKAGE_STATUS_UPDATE_SUB_TYPE = "更新服务套餐状态";
    String SERVICE_PACKAGE_STATUS_UPDATE_SUCCESS = "批量更新了服务套餐状态为【{{#reqVO.status}}】";

    // ======================= SERVICE_PACKAGE_CAROUSEL 服务套餐轮播图
    // =======================
    String SERVICE_PACKAGE_CAROUSEL_TYPE = "SERVICE_PACKAGE_CAROUSEL 服务套餐轮播图";
    String SERVICE_PACKAGE_CAROUSEL_CREATE_SUB_TYPE = "新增服务套餐轮播图";
    String SERVICE_PACKAGE_CAROUSEL_CREATE_SUCCESS = "新增了服务套餐轮播图【{{#carousel.imageUrl}}】";
    String SERVICE_PACKAGE_CAROUSEL_DELETE_SUB_TYPE = "删除服务套餐轮播图";
    String SERVICE_PACKAGE_CAROUSEL_DELETE_SUCCESS = "删除了服务套餐轮播图【{{#carousel.imageUrl}}】";

    // ======================= SERVICE_PACKAGE_FEATURE 服务套餐特色标签
    // =======================
    String SERVICE_PACKAGE_FEATURE_TYPE = "SERVICE_PACKAGE_FEATURE 服务套餐特色标签";
    String SERVICE_PACKAGE_FEATURE_CREATE_SUB_TYPE = "新增服务套餐特色标签";
    String SERVICE_PACKAGE_FEATURE_CREATE_SUCCESS = "新增了服务套餐特色标签【{{#feature.featureName}}】";
    String SERVICE_PACKAGE_FEATURE_DELETE_SUB_TYPE = "删除服务套餐特色标签";
    String SERVICE_PACKAGE_FEATURE_DELETE_SUCCESS = "删除了服务套餐特色标签【{{#feature.featureName}}】";

    // ======================= PRACTITIONER 阿姨 =======================
    String PRACTITIONER_TYPE = "PRACTITIONER 阿姨";
    String PRACTITIONER_CREATE_SUB_TYPE = "新增阿姨";
    String PRACTITIONER_CREATE_SUCCESS = "新增了阿姨【{{#practitioner.name}}】";
    String PRACTITIONER_UPDATE_SUB_TYPE = "更新阿姨";
    String PRACTITIONER_UPDATE_SUCCESS = "更新了阿姨【{{#practitioner.name}}】: {{#fieldChanges}}{{#qualificationChangeLog != null ? '；' + #qualificationChangeLog : ''}}";

    String PRACTITIONER_STATUS_UPDATE_SUB_TYPE = "更新阿姨状态";
    String PRACTITIONER_STATUS_UPDATE_SUCCESS = "更新了阿姨【{{#practitioner.name}}】的状态为【{{#reqVO.platformStatus}}】";
    String PRACTITIONER_RATING_UPDATE_SUB_TYPE = "更新阿姨评级";
    String PRACTITIONER_RATING_UPDATE_SUCCESS = "更新了阿姨【{{#practitioner.name}}】的评级为【{{#reqVO.newRating}}】";

    // ======================= PRACTITIONER_QUALIFICATION 阿姨资质文件
    // =======================
    String PRACTITIONER_QUALIFICATION_TYPE = "PRACTITIONER_QUALIFICATION 阿姨资质文件";
    String PRACTITIONER_QUALIFICATION_CREATE_SUB_TYPE = "新增阿姨资质文件";
    String PRACTITIONER_QUALIFICATION_CREATE_SUCCESS = "新增了阿姨【{{#qualification.practitionerId}}】的资质文件【{{#qualification.fileName}}】";
    String PRACTITIONER_QUALIFICATION_DELETE_SUB_TYPE = "删除阿姨资质文件";
    String PRACTITIONER_QUALIFICATION_DELETE_SUCCESS = "删除了阿姨【{{#qualification.practitionerId}}】的资质文件【{{#qualification.fileName}}】";

    // ======================= QUESTION 考题管理 =======================
    String QUESTION_TYPE = "QUESTION 考题管理";
    String QUESTION_CREATE_SUB_TYPE = "创建考题";
    String QUESTION_CREATE_SUCCESS = "创建了考题【{{#question.title}}】，题型为【{{#question.type}}】";
    String QUESTION_UPDATE_SUB_TYPE = "更新考题";
    String QUESTION_UPDATE_SUCCESS = "更新了考题【{{#question.title}}】: {_DIFF{#updateReqVO}}";
    String QUESTION_DELETE_SUB_TYPE = "删除考题";
    String QUESTION_DELETE_SUCCESS = "删除了考题【{{#question.title}}】";

    // ======================= QUESTION_CATEGORY 考题分类管理 =======================
    String QUESTION_CATEGORY_TYPE = "QUESTION_CATEGORY 考题分类管理";
    String QUESTION_CATEGORY_CREATE_SUB_TYPE = "创建考题分类";
    String QUESTION_CATEGORY_CREATE_SUCCESS = "创建了考题分类【{{#category.level1Name}}】-【{{#category.level2Name}}】-【{{#category.level3Name}}】";
    String QUESTION_CATEGORY_UPDATE_SUB_TYPE = "更新考题分类";
    String QUESTION_CATEGORY_UPDATE_SUCCESS = "更新了考题分类【{{#category.level1Name}}】-【{{#category.level2Name}}】-【{{#category.level3Name}}】: {_DIFF{#updateReqVO}}";
    String QUESTION_CATEGORY_DELETE_SUB_TYPE = "删除考题分类";
    String QUESTION_CATEGORY_DELETE_SUCCESS = "删除了考题分类【{{#category.level1Name}}】-【{{#category.level2Name}}】-【{{#category.level3Name}}】";

    // ======================= SITE_MANAGEMENT 场地管理 =======================
    String SITE_MANAGEMENT_TYPE = "SITE_MANAGEMENT 场地管理";
    String SITE_MANAGEMENT_CREATE_SUB_TYPE = "新增场地";
    String SITE_MANAGEMENT_CREATE_SUCCESS = "新增了场地【{{#siteManagement.name}}】，校区【{{#siteManagement.campusName}}】";
    String SITE_MANAGEMENT_UPDATE_SUB_TYPE = "更新场地";
    String SITE_MANAGEMENT_UPDATE_SUCCESS = "更新了场地【{{#siteManagement.name}}】: {_DIFF{#updateReqVO}}";
    String SITE_MANAGEMENT_DELETE_SUB_TYPE = "删除场地";
    String SITE_MANAGEMENT_DELETE_SUCCESS = "删除了场地【{{#siteManagement.name}}】，校区【{{#siteManagement.campusName}}】";

    // ======================= AGENCY 机构管理 =======================
    String AGENCY_TYPE = "AGENCY 机构管理";
    String AGENCY_CREATE_SUB_TYPE = "新增机构";
    String AGENCY_CREATE_SUCCESS = "新增了机构【{{#agency.agencyName}}】";
    String AGENCY_UPDATE_SUB_TYPE = "更新机构";
    String AGENCY_UPDATE_SUCCESS = "更新了机构【{{#agency.agencyName}}】: {_DIFF{#updateReqVO}}";
    String AGENCY_DELETE_SUB_TYPE = "删除机构";
    String AGENCY_DELETE_SUCCESS = "删除了机构【{{#agency.agencyName}}】";
    String AGENCY_REVIEW_SUB_TYPE = "审核机构";
    String AGENCY_REVIEW_SUCCESS = "审核了机构【{{#agency.agencyName}}】: 审核状态【{{#review.reviewStatus}}】，审核备注【{{#review.reviewRemark}}】，审核时间【{{#agency.reviewTime}}】";

    // ======================= UNIVERSITY_PRACTICE_ORDER 高校实践订单
    // =======================
    String UNIVERSITY_PRACTICE_ORDER_TYPE = "UNIVERSITY_PRACTICE_ORDER 高校实践订单";
    String UNIVERSITY_PRACTICE_ORDER_CREATE_SUB_TYPE = "创建高校实践订单";
    String UNIVERSITY_PRACTICE_ORDER_CREATE_SUCCESS = "创建了高校实践订单【{{#order.orderNo}}】，项目为【{{#order.projectName}}】";
    String UNIVERSITY_PRACTICE_ORDER_UPDATE_SUB_TYPE = "更新高校实践订单";
    String UNIVERSITY_PRACTICE_ORDER_UPDATE_SUCCESS = "更新了高校实践订单【{{#order.orderNo}}】: {_DIFF{#updateReqVO}}";
    String UNIVERSITY_PRACTICE_ORDER_DELETE_SUB_TYPE = "删除高校实践订单";
    String UNIVERSITY_PRACTICE_ORDER_DELETE_SUCCESS = "删除了高校实践订单【{{#order.orderNo}}】，项目为【{{#order.orderNo}}】";

    // ======================= CAROUSEL 轮播图管理 =======================
    String CAROUSEL_TYPE = "CAROUSEL 轮播图管理";
    String CAROUSEL_CREATE_SUB_TYPE = "新增轮播图";
    String CAROUSEL_CREATE_SUCCESS = "新增了轮播图【{{#carousel.carouselTitle}}】";
    String CAROUSEL_UPDATE_SUB_TYPE = "编辑轮播图";
    String CAROUSEL_UPDATE_SUCCESS = "编辑了轮播图【{{#carousel.carouselTitle}}】: {_DIFF{#updateReqVO}}";
    String CAROUSEL_DELETE_SUB_TYPE = "删除轮播图";
    String CAROUSEL_DELETE_SUCCESS = "删除了轮播图【{{#carousel.carouselTitle}}】";
    String CAROUSEL_STATUS_UPDATE_SUB_TYPE = "修改轮播图状态";
    String CAROUSEL_STATUS_UPDATE_SUCCESS = "修改了轮播图【{{#carousel.carouselTitle}}】的状态为【{{#status}}】";
}