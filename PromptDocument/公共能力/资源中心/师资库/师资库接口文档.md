# 师资库接口文档

## 1. 新增讲师

- **接口地址**：/publicbiz/teacher/create
- **请求方式**：POST
- **入参**：

```
{
  "avatar": "头像URL",
  "name": "讲师姓名",
  "desc": "简介",
  "type": "讲师类型",
  "biz": "业务模块",
  "org": "关联机构",
  "field": "擅长领域",
  "phone": "联系电话",
  "status": "合作状态",
  "sign_status": "电子签约状态",
  "sign_date": "2023-09-05",
  "contract_type": "合同类型",
  "contract_template": "合同模板",
  "contract_no": "合同编号",
  "contract_name": "合同名称",
  "contract_period_start": "2023-09-01",
  "contract_period_end": "2024-09-01",
  "contract_amount": 10000.00,
  "contract_file_name": "合同附件.pdf",
  "contract_file_url": "https://xxx.com/contract.pdf",
  "certFiles": [
    {
      "cert_type": "资质类型",
      "cert_name": "资质名称",
      "file_name": "文件名.pdf",
      "file_url": "https://xxx.com/file.pdf",
      "valid_start_date": "2024-01-01",
      "valid_end_date": "2026-12-31"
    }
  ]
}
```

- **出参**：

```
{
  "code": 0,
  "msg": "success",
  "data": { "id": 1 }
}
```

## 2. 编辑讲师

- **接口地址**：/publicbiz/teacher/update
- **请求方式**：POST
- **入参**：同新增讲师，需包含id字段
- **出参**：

```
{
  "code": 0,
  "msg": "success"
}
```

## 3. 删除讲师

- **接口地址**：/publicbiz/teacher/delete
- **请求方式**：POST
- **入参**：

```
{ "id": 1 }
```

- **出参**：

```
{ "code": 0, "msg": "success" }
```

## 4. 讲师详情

- **接口地址**：/publicbiz/teacher/detail
- **请求方式**：GET
- **入参**：`?id=1`
- **出参**：

```
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "avatar": "url",
    "name": "王老师",
    "desc": "简介",
    "type": "内部讲师",
    "biz": "高校业务",
    "org": "北京大学",
    "field": "职业规划",
    "phone": "13800138001",
    "status": "合作中",
    "sign_status": "已签约",
    "sign_date": "2023-09-05",
    "contract_type": "劳务合同",
    "contract_template": "标准模板A",
    "contract_no": "HT20230901",
    "contract_name": "2023年度讲师劳务合同",
    "contract_period_start": "2023-09-01",
    "contract_period_end": "2024-09-01",
    "contract_amount": 10000.00,
    "contract_file_name": "合同附件.pdf",
    "contract_file_url": "https://xxx.com/contract.pdf",
            "certFiles": [
          {
            "id": 1,
            "cert_type": "技能等级证书",
            "cert_name": "人力资源管理师",
            "file_name": "证书.pdf",
            "file_url": "https://xxx.com/cert.pdf",
            "valid_start_date": "2024-01-01",
            "valid_end_date": "2026-12-31"
          }
        ],
    "create_time": "2023-01-15 10:00:00",
    "update_time": "2023-01-15 10:00:00"
  }
}
```

## 5. 讲师分页查询

- **接口地址**：/publicbiz/teacher/page
- **请求方式**：GET
- **入参**：`?page=1&size=10&type=内部讲师&biz=高校业务&status=合作中&keyword=王`
- **出参**：

```
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 1,
    "list": [
      {
        "id": 1,
        "avatar": "url",
        "name": "王老师",
        "desc": "简介",
        "type": "内部讲师",
        "biz": "高校业务",
        "org": "北京大学",
        "field": "职业规划",
        "phone": "13800138001",
        "status": "合作中",
        "sign_status": "已签约",
        "sign_date": "2023-09-05",
        "contract_type": "劳务合同",
        "contract_template": "标准模板A",
        "contract_no": "HT20230901",
        "contract_name": "2023年度讲师劳务合同",
        "contract_period_start": "2023-09-01",
        "contract_period_end": "2024-09-01",
        "contract_amount": 10000.00,
        "contract_file_name": "合同附件.pdf",
        "contract_file_url": "https://xxx.com/contract.pdf",
        "certFiles": [
          {
            "id": 1,
            "cert_type": "技能等级证书",
            "cert_name": "人力资源管理师",
            "file_name": "证书.pdf",
            "file_url": "https://xxx.com/cert.pdf",
            "valid_start_date": "2024-01-01",
            "valid_end_date": "2026-12-31"
          }
        ],
        "create_time": "2023-01-15 10:00:00",
        "update_time": "2023-01-15 10:00:00"
      }
    ]
  }
}
```

## 6. 讲师统计卡片数据

- **接口地址**：/publicbiz/teacher/stat
- **请求方式**：GET
- **入参**：无
- **出参**：

```
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 3,
    "inner": 1,
    "outer": 2,
    "pending": 1
  }
}
```

- **请求示例**：

```
curl http://host/admin-api/publicbiz/teacher/stat
```

## 7. 讲师资质文件相关接口

### 7.1 新增讲师资质文件

- **接口地址**：/publicbiz/teacher/cert/create
- **请求方式**：POST
- **入参**：

```
{
  "teacher_id": 1,
  "cert_type": "资质类型",
  "cert_name": "资质名称",
  "file_name": "文件名.pdf",
  "file_url": "https://xxx.com/file.pdf",
  "valid_start_date": "2024-01-01",
  "valid_end_date": "2026-12-31"
}
```

- **出参**：

```
{ "code": 0, "msg": "success", "data": { "id": 1 } }
```

### 7.2 删除讲师资质文件

- **接口地址**：/publicbiz/teacher/cert/delete
- **请求方式**：POST
- **入参**：

```
{ "id": 1 }
```

- **出参**：

```
{ "code": 0, "msg": "success" }
```

### 7.3 查询讲师资质文件列表

- **接口地址**：/publicbiz/teacher/cert/list
- **请求方式**：GET
- **入参**：`?teacher_id=1`
- **出参**：

```
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "teacher_id": 1,
      "cert_type": "技能等级证书",
      "cert_name": "人力资源管理师",
      "file_name": "证书.pdf",
      "file_url": "https://xxx.com/cert.pdf",
      "valid_start_date": "2024-01-01",
      "valid_end_date": "2026-12-31"
    }
  ]
}
```

## 8. 批量导入讲师

- **接口地址**：/publicbiz/teacher/import
- **请求方式**：POST（multipart/form-data）
- **入参**：
  - file: 师资库导入模板（CSV文件，字段顺序如下）
    - 讲师姓名\*（name）
    - 讲师类型\*（type）
    - 业务模块\*（biz）
    - 关联机构（org）
    - 擅长领域\*（field）
    - 合作状态\*（status）
    - 联系电话（phone）
    - 邮箱地址（email）
    - 个人简介（desc）
- **出参**：

```
{
  "code": 0,
  "msg": "success",
  "data": {
    "successCount": 2,
    "failCount": 0,
    "failList": []
  }
}
```

- **请求示例**：

```
curl -X POST http://host/admin-api/publicbiz/teacher/import -F "file=@师资库导入模板.csv"
```

- **说明**：
  - 返回成功/失败条数及失败明细（如有）。
  - 字段含义及必填项详见模板及页面说明。
