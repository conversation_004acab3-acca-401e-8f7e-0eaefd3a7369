<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.service.ServicePunchRecordMapper">

    <!-- 统计阿姨指定日期范围内的出勤天数 -->
    <select id="countAttendanceDays" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT DATE(punch_time))
        FROM service_punch_record
        WHERE aunt_oneid = #{auntOneId}
          AND punch_time >= #{startDate}
          AND punch_time &lt; #{endDate} + INTERVAL 1 DAY
          AND deleted = 0
    </select>

    <!-- 根据阿姨OneID和日期范围查询打卡记录 -->
    <select id="selectByAuntOneIdAndDateRange" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.service.ServicePunchRecordDO">
        SELECT *
        FROM service_punch_record
        WHERE aunt_oneid = #{auntOneId}
          AND punch_time >= #{startDate}
          AND punch_time &lt; #{endDate} + INTERVAL 1 DAY
          AND deleted = 0
        ORDER BY punch_time DESC
    </select>

    <!-- 根据阿姨OneID查询指定日期的打卡记录 -->
    <select id="selectByAuntOneIdAndScheduleDate" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.service.ServicePunchRecordDO">
        SELECT *
        FROM service_punch_record
        WHERE aunt_oneid = #{auntOneId}
          AND DATE(punch_time) = #{scheduleDate}
          AND deleted = 0
        ORDER BY punch_time DESC
    </select>

    <!-- 根据阿姨OneID和打卡类型查询打卡记录 -->
    <select id="selectByAuntOneIdAndPunchType" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.service.ServicePunchRecordDO">
        SELECT *
        FROM service_punch_record
        WHERE aunt_oneid = #{auntOneId}
          AND punch_type = #{punchType}
          AND deleted = 0
        ORDER BY punch_time DESC
    </select>

</mapper>
