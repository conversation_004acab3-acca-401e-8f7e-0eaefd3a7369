<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper">

    <sql id="selectFields">
        id, agency_no, agency_name, agency_short_name, agency_type,
        legal_representative, unified_social_credit_code, establishment_date,
        registered_address, operating_address, business_scope,
        applicant_name, applicant_phone, application_time,
        contact_person, contact_phone, contact_email,
        agency_address, province_code, province, city_code, city,
        district_code, district, street_code, street, detail_address,
        longitude, latitude, location_accuracy,
        cooperation_status, contract_no, contract_start_date, contract_end_date, commission_rate,
        review_status, reviewer, review_time, review_remark,
        status, remark,
        tenant_id, creator, create_time, updater, update_time, deleted
    </sql>



    <select id="selectPage" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_agency
        <where>
            deleted = 0
            <if test="reqVO.keyword != null and reqVO.keyword != ''">
                AND (agency_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
                OR agency_no LIKE CONCAT('%', #{reqVO.keyword}, '%'))
            </if>
            <if test="reqVO.agencyNo != null and reqVO.agencyNo != ''">
                AND agency_no = #{reqVO.agencyNo}
            </if>
            <if test="reqVO.cooperationStatus != null and reqVO.cooperationStatus != ''">
                AND cooperation_status = #{reqVO.cooperationStatus}
            </if>
            <if test="reqVO.reviewStatus != null and reqVO.reviewStatus != ''">
                AND review_status = #{reqVO.reviewStatus}
            </if>
            <if test="reqVO.district != null and reqVO.district != ''">
                AND district = #{reqVO.district}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectByAgencyNo" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_agency
        WHERE agency_no = #{agencyNo}
        AND deleted = 0
        LIMIT 1
    </select>

    <select id="selectByUnifiedSocialCreditCode" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_agency
        WHERE unified_social_credit_code = #{unifiedSocialCreditCode}
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据机构ID列表查询机构信息 -->
    <select id="selectAgencyListByIds" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO">
        SELECT *
        FROM publicbiz_agency
        WHERE deleted = 0 
            AND status = 'active'
            AND id IN
        <foreach collection="agencyIds" item="agencyId" open="(" separator="," close=")">
            #{agencyId}
        </foreach>
    </select>

    <!-- 根据机构ID查询机构信息 -->
    <select id="selectAgencyById" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO">
        SELECT *
        FROM publicbiz_agency
        WHERE deleted = 0 
            AND status = 'active'
            AND id = #{agencyId}
    </select>

    <!-- ========== 雇主机构关联相关方法 ========== -->

    <!-- 分页查询雇主机构关联列表 -->
    <select id="selectEmployerAgencyPage" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO">
        SELECT a.*
        FROM publicbiz_agency a
        INNER JOIN publicbiz_employer_agency ea ON a.id = ea.agency_id
        <where>
            a.deleted = 0
            AND ea.deleted = 0
            <if test="reqVO.employerId != null">
                AND ea.employer_id = #{reqVO.employerId}
            </if>
            <if test="reqVO.agencyId != null">
                AND ea.agency_id = #{reqVO.agencyId}
            </if>
            <if test="reqVO.cooperationType != null and reqVO.cooperationType != ''">
                AND ea.cooperation_type = #{reqVO.cooperationType}
            </if>
            <if test="reqVO.cooperationStatus != null and reqVO.cooperationStatus != ''">
                AND ea.cooperation_status = #{reqVO.cooperationStatus}
            </if>
            <if test="reqVO.reviewStatus != null and reqVO.reviewStatus != ''">
                AND ea.review_status = #{reqVO.reviewStatus}
            </if>
        </where>
        ORDER BY a.id DESC
    </select>

    <!-- 根据雇主ID查询关联的机构列表 -->
    <select id="selectAgenciesByEmployerId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO">
        SELECT a.*
        FROM publicbiz_agency a
        INNER JOIN publicbiz_employer_agency ea ON a.id = ea.agency_id
        WHERE ea.employer_id = #{employerId}
            AND ea.deleted = 0
            AND ea.status = 'active'
            AND a.deleted = 0
            AND a.status = 'active'
    </select>

    <!-- 根据机构ID查询关联的雇主列表 -->
    <select id="selectEmployersByAgencyId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO">
        SELECT a.*
        FROM publicbiz_agency a
        INNER JOIN publicbiz_employer_agency ea ON a.id = ea.agency_id
        WHERE ea.agency_id = #{agencyId}
            AND ea.deleted = 0
            AND ea.status = 'active'
            AND a.deleted = 0
            AND a.status = 'active'
    </select>

    <!-- 根据雇主ID和机构ID查询关联关系 -->
    <select id="selectEmployerAgencyByEmployerAndAgency" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO">
        SELECT a.*
        FROM publicbiz_agency a
        INNER JOIN publicbiz_employer_agency ea ON a.id = ea.agency_id
        WHERE ea.employer_id = #{employerId}
            AND ea.agency_id = #{agencyId}
            AND ea.deleted = 0
            AND a.deleted = 0
        LIMIT 1
    </select>

    <!-- 根据ID查询雇主机构关联信息 -->
    <select id="selectEmployerAgencyById" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO">
        SELECT a.*
        FROM publicbiz_agency a
        INNER JOIN publicbiz_employer_agency ea ON a.id = ea.agency_id
        WHERE ea.id = #{id}
            AND ea.deleted = 0
            AND a.deleted = 0
        LIMIT 1
    </select>



    <!-- 检查雇主机构关联是否存在 -->
    <select id="existsEmployerAgency" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM publicbiz_employer_agency
        WHERE employer_id = #{employerId}
            AND agency_id = #{agencyId}
            AND deleted = 0
    </select>

    <!-- 统计雇主关联的机构数量 -->
    <select id="countAgenciesByEmployerId" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM publicbiz_employer_agency
        WHERE employer_id = #{employerId}
            AND deleted = 0
            AND status = 'active'
    </select>

    <!-- 统计机构关联的雇主数量 -->
    <select id="countEmployersByAgencyId" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM publicbiz_employer_agency
        WHERE agency_id = #{agencyId}
            AND deleted = 0
            AND status = 'active'
    </select>

</mapper> 