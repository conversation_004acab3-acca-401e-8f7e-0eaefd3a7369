CREATE TABLE `publicbiz_practitioner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `aunt_oneid` varchar(36) NOT NULL DEFAULT '' COMMENT '阿姨OneID GUID',
  `name` varchar(50) NOT NULL COMMENT '阿姨姓名',
  `phone` varchar(20) NOT NULL COMMENT '手机号，用于登录',
  `id_card` varchar(18) NOT NULL COMMENT '身份证号',
  `hometown` varchar(100) DEFAULT NULL COMMENT '籍贯，如：四川成都',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别：male-男/female-女',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `service_type` varchar(50) NOT NULL COMMENT '主要服务类型：月嫂/育儿嫂/保洁/护工',
  `experience_years` int(11) NOT NULL COMMENT '从业年限',
  `platform_status` varchar(20) NOT NULL DEFAULT 'cooperating' COMMENT '平台状态：cooperating-合作中/terminated-已解约',
  `rating` decimal(2,1) NOT NULL DEFAULT '4.5' COMMENT '评级，1.0-5.0',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
  `agency_name` varchar(200) DEFAULT NULL COMMENT '所属机构名称',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-正常/inactive-停用/pending-待审核',
  `current_status` varchar(50) DEFAULT NULL COMMENT '当前状态：服务中/待岗/休假中',
  `current_order_id` varchar(50) DEFAULT NULL COMMENT '当前服务订单ID',
  `total_orders` int(11) NOT NULL DEFAULT '0' COMMENT '累计服务单数',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计收入',
  `customer_satisfaction` decimal(3,1) DEFAULT NULL COMMENT '客户满意度评分',
  `profile` text COMMENT '阿姨简介，个人描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COMMENT='阿姨基本信息表';

CREATE TABLE `publicbiz_practitioner_qualification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `practitioner_oneId` varchar(36) NOT NULL COMMENT '阿姨oneID',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型：id_card-身份证/health_cert-健康证/skill_cert-专业技能证书/other-其他附件',
  `file_name` varchar(200) NOT NULL COMMENT '文件名',
  `file_url` varchar(500) NOT NULL COMMENT '文件URL',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_extension` varchar(20) DEFAULT NULL COMMENT '文件扩展名',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序，数字越小越靠前',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，0-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阿姨资质文件表';

CREATE TABLE `talent_user_tag` (
  `user_tag_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '关联用户ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID，关联标签库',
  `tag_type_id` bigint(20) NOT NULL COMMENT '标签类型ID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`user_tag_id`)
) ENGINE=InnoDB AUTO_INCREMENT=132 DEFAULT CHARSET=utf8mb4 COMMENT='用户标签关联表';


CREATE TABLE `publicbiz_practitioner_qualification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `practitioner_oneId` varchar(36) NOT NULL COMMENT '阿姨oneID',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型：id_card-身份证/health_cert-健康证/skill_cert-专业技能证书/other-其他附件',
  `file_name` varchar(200) NOT NULL COMMENT '文件名',
  `file_url` varchar(500) NOT NULL COMMENT '文件URL',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_extension` varchar(20) DEFAULT NULL COMMENT '文件扩展名',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序，数字越小越靠前',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，0-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阿姨资质文件表';


CREATE TABLE `publicbiz_aunt_review` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `aunt_id` bigint(20) NOT NULL COMMENT '阿姨OneID',
  `reviewer_id` bigint(20) NOT NULL COMMENT '评价人用户ID',
  `reviewer_name` varchar(50) NOT NULL COMMENT '评价人姓名',
  `reviewer_avatar` varchar(500) DEFAULT NULL COMMENT '评价人头像URL',
  `rating` decimal(2,1) NOT NULL COMMENT '评分：1.0-5.0',
  `review_tags` text COMMENT '评价标签（JSON格式，如：["专业","细心","守时"]）',
  `review_content` text COMMENT '评价内容',
  `review_images` text COMMENT '评价图片URL列表（JSON格式）',
  `review_type` varchar(20) DEFAULT 'service' COMMENT '评价类型：service-服务评价，attitude-态度评价，professional-专业评价',
  `is_anonymous` tinyint(1) DEFAULT '0' COMMENT '是否匿名评价：0-否，1-是',
  `is_recommend` tinyint(1) DEFAULT '0' COMMENT '是否推荐：0-否，1-是',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞数',
  `reply_content` text COMMENT '回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-隐藏，1-显示',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
  `service_package_id` bigint(20) DEFAULT NULL COMMENT '服务套餐ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='阿姨评价表';




CREATE TABLE `talent_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `identity_id` varchar(18) NOT NULL COMMENT '身份证号',
  `birth_date` date NOT NULL COMMENT '出生日期',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别（可选值：男、女、其他）',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `status` varchar(30) DEFAULT NULL COMMENT 'OneID状态（可选值：正常、待合并、已禁用）',
  `register_source` varchar(50) DEFAULT NULL COMMENT '用户来源',
  `oneid` char(36) NOT NULL DEFAULT '' COMMENT 'OneID GUID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `completeness` tinyint(4) DEFAULT '0' COMMENT '档案完整度百分比',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  `org_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
  `org_name` varchar(200) DEFAULT NULL COMMENT '所属机构名称',
  `talent_source` varchar(100) DEFAULT NULL COMMENT '人才来源',
  `is_self_support` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否为平台自营（0-否，1-是）',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8mb4 COMMENT='平台用户主表';

CREATE TABLE `talent_user_tag` (
  `user_tag_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '关联用户ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID，关联标签库',
  `tag_type_id` bigint(20) NOT NULL COMMENT '标签类型ID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`user_tag_id`)
) ENGINE=InnoDB AUTO_INCREMENT=132 DEFAULT CHARSET=utf8mb4 COMMENT='用户标签关联表';

CREATE TABLE `talent_tag_type` (
  `tag_type_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签类型ID',
  `type_code` varchar(50) NOT NULL COMMENT '标签类型编码',
  `type_name` varchar(100) NOT NULL COMMENT '标签类型名称',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`tag_type_id`),
  UNIQUE KEY `type_code` (`type_code`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='标签类型表';

CREATE TABLE `talent_tag_library` (
  `tag_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `tag_type_id` bigint(20) NOT NULL COMMENT '标签类型ID',
  `tag_code` varchar(50) NOT NULL COMMENT '标签编码',
  `tag_name` varchar(100) NOT NULL COMMENT '标签名称',
  `description` varchar(255) DEFAULT NULL COMMENT '标签描述',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`tag_id`),
  UNIQUE KEY `tag_code` (`tag_code`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COMMENT='标签库表';